{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": ["cuda-version >=12.8,<12.9.0a0", "cuda-cccl_win-64 12.8.90"], "extracted_package_dir": "", "files": [], "fn": "cuda-cccl-12.8.90-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "", "type": 1}, "md5": "42d091ec934d3ed3b3816c395fbf6177", "name": "cuda-cccl", "package_tarball_full_path": "", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "38f4c82e5da849e0e18fc678e94e001ff837619d5f3a2ddfe395ccb21dd41e71", "size": 16836, "subdir": "win-64", "timestamp": 1739456554000, "url": "https://conda.anaconda.org/nvidia/win-64/cuda-cccl-12.8.90-0.conda", "version": "12.8.90"}