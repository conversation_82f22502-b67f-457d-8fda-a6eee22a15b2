{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.8,<12.9.0a0"], "extracted_package_dir": "", "files": ["Library/bin/nvdisasm.exe"], "fn": "cuda-nvdisasm-12.8.90-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "", "type": 1}, "md5": "1a86b69aca6a471f7e3a78a074fb2efe", "name": "cuda-nvdisasm", "package_tarball_full_path": "", "paths_data": {"paths": [{"_path": "Library/bin/nvdisasm.exe", "path_type": "hardlink", "sha256": "f0ab98df225b9f2568e8f56c24a919a4e1362425fb7c02101afbf85022324add", "sha256_in_prefix": "f0ab98df225b9f2568e8f56c24a919a4e1362425fb7c02101afbf85022324add", "size_in_bytes": 5947392}], "paths_version": 1}, "requested_spec": "None", "sha256": "d3e2851b4334de61c0e4ac1812817b004914250eea0948e6f76a0438b1a67d50", "size": 5275383, "subdir": "win-64", "timestamp": 1739448452000, "url": "https://conda.anaconda.org/nvidia/win-64/cuda-nvdisasm-12.8.90-0.conda", "version": "12.8.90"}