{"build": "h0620614_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["vc >=14.2,<15.0a0", "vs2015_runtime >=14.29.30133,<15.0a0", "zlib >=1.2.13,<1.3.0a0", "libpng >=1.6.39,<1.7.0a0"], "extracted_package_dir": "", "files": ["Library/bin/freetype.dll", "Library/include/freetype/config/ftconfig.h", "Library/include/freetype/config/ftheader.h", "Library/include/freetype/config/ftmodule.h", "Library/include/freetype/config/ftoption.h", "Library/include/freetype/config/ftstdlib.h", "Library/include/freetype/config/integer-types.h", "Library/include/freetype/config/mac-support.h", "Library/include/freetype/config/public-macros.h", "Library/include/freetype/freetype.h", "Library/include/freetype/ftadvanc.h", "Library/include/freetype/ftbbox.h", "Library/include/freetype/ftbdf.h", "Library/include/freetype/ftbitmap.h", "Library/include/freetype/ftbzip2.h", "Library/include/freetype/ftcache.h", "Library/include/freetype/ftchapters.h", "Library/include/freetype/ftcid.h", "Library/include/freetype/ftcolor.h", "Library/include/freetype/ftdriver.h", "Library/include/freetype/fterrdef.h", "Library/include/freetype/fterrors.h", "Library/include/freetype/ftfntfmt.h", "Library/include/freetype/ftgasp.h", "Library/include/freetype/ftglyph.h", "Library/include/freetype/ftgxval.h", "Library/include/freetype/ftgzip.h", "Library/include/freetype/ftimage.h", "Library/include/freetype/ftincrem.h", "Library/include/freetype/ftlcdfil.h", "Library/include/freetype/ftlist.h", "Library/include/freetype/ftlogging.h", "Library/include/freetype/ftlzw.h", "Library/include/freetype/ftmac.h", "Library/include/freetype/ftmm.h", "Library/include/freetype/ftmodapi.h", "Library/include/freetype/ftmoderr.h", "Library/include/freetype/ftotval.h", "Library/include/freetype/ftoutln.h", "Library/include/freetype/ftparams.h", "Library/include/freetype/ftpfr.h", "Library/include/freetype/ftrender.h", "Library/include/freetype/ftsizes.h", "Library/include/freetype/ftsnames.h", "Library/include/freetype/ftstroke.h", "Library/include/freetype/ftsynth.h", "Library/include/freetype/ftsystem.h", "Library/include/freetype/fttrigon.h", "Library/include/freetype/fttypes.h", "Library/include/freetype/ftwinfnt.h", "Library/include/freetype/otsvg.h", "Library/include/freetype/t1tables.h", "Library/include/freetype/ttnameid.h", "Library/include/freetype/tttables.h", "Library/include/freetype/tttags.h", "Library/include/freetype2/dlg/dlg.h", "Library/include/freetype2/dlg/output.h", "Library/include/ft2build.h", "Library/lib/cmake/freetype/freetype-config-release.cmake", "Library/lib/cmake/freetype/freetype-config-version.cmake", "Library/lib/cmake/freetype/freetype-config.cmake", "Library/lib/freetype.lib", "Library/lib/pkgconfig/freetype2.pc"], "fn": "freetype-2.13.3-h0620614_0.conda", "license": "GPL-2.0-only or FTL", "link": {"source": "", "type": 1}, "md5": "dd4baf224d4ae4e834765cdfafe7ee5b", "name": "freetype", "package_tarball_full_path": "", "paths_data": {"paths": [{"_path": "Library/bin/freetype.dll", "path_type": "hardlink", "sha256": "6a3367761e22b3465f570c40a4eeb70cb035a0f81da1f9e6f276b3db89909187", "sha256_in_prefix": "6a3367761e22b3465f570c40a4eeb70cb035a0f81da1f9e6f276b3db89909187", "size_in_bytes": 682256}, {"_path": "Library/include/freetype/config/ftconfig.h", "path_type": "hardlink", "sha256": "f3c2f1d7a3321edf770584838a18d97e2129d1a4ed95bbb1777949b7165e2310", "sha256_in_prefix": "f3c2f1d7a3321edf770584838a18d97e2129d1a4ed95bbb1777949b7165e2310", "size_in_bytes": 1665}, {"_path": "Library/include/freetype/config/ftheader.h", "path_type": "hardlink", "sha256": "0e731fa6a148f64ff15274423553ec187a1aef297d130c430ff07951554ef8a1", "sha256_in_prefix": "0e731fa6a148f64ff15274423553ec187a1aef297d130c430ff07951554ef8a1", "size_in_bytes": 23919}, {"_path": "Library/include/freetype/config/ftmodule.h", "path_type": "hardlink", "sha256": "61c76e01106e7b7c80f39f8d6fd479b5323b54d757c9a2cb66d07064ef616649", "sha256_in_prefix": "61c76e01106e7b7c80f39f8d6fd479b5323b54d757c9a2cb66d07064ef616649", "size_in_bytes": 1443}, {"_path": "Library/include/freetype/config/ftoption.h", "path_type": "hardlink", "sha256": "0f40ede705187035223cd50190d57e61b3377b63a04587a72af611e47e5d99ce", "sha256_in_prefix": "0f40ede705187035223cd50190d57e61b3377b63a04587a72af611e47e5d99ce", "size_in_bytes": 41368}, {"_path": "Library/include/freetype/config/ftstdlib.h", "path_type": "hardlink", "sha256": "fafd7eb672702cb2dc20b7e7e19540d251a3d978745821bc0632ba807c8b6f46", "sha256_in_prefix": "fafd7eb672702cb2dc20b7e7e19540d251a3d978745821bc0632ba807c8b6f46", "size_in_bytes": 4576}, {"_path": "Library/include/freetype/config/integer-types.h", "path_type": "hardlink", "sha256": "8cc533596bff2f044823853f5d394bc697d6572c9c664f6beaaa67e066bfe162", "sha256_in_prefix": "8cc533596bff2f044823853f5d394bc697d6572c9c664f6beaaa67e066bfe162", "size_in_bytes": 7072}, {"_path": "Library/include/freetype/config/mac-support.h", "path_type": "hardlink", "sha256": "557701edb1bf2c98e38c7331e7522b4c35d390cb9899461d2eea4a74ab460196", "sha256_in_prefix": "557701edb1bf2c98e38c7331e7522b4c35d390cb9899461d2eea4a74ab460196", "size_in_bytes": 1597}, {"_path": "Library/include/freetype/config/public-macros.h", "path_type": "hardlink", "sha256": "eb069473a8fbd2342388fbaade8f1159428b3b7a95a070a16eb84c16cd26316c", "sha256_in_prefix": "eb069473a8fbd2342388fbaade8f1159428b3b7a95a070a16eb84c16cd26316c", "size_in_bytes": 4207}, {"_path": "Library/include/freetype/freetype.h", "path_type": "hardlink", "sha256": "b8d8b9e32b2956cce3a9c5d9b8d18041fb7451acb747805c20b62bf1b4804cdb", "sha256_in_prefix": "b8d8b9e32b2956cce3a9c5d9b8d18041fb7451acb747805c20b62bf1b4804cdb", "size_in_bytes": 177200}, {"_path": "Library/include/freetype/ftadvanc.h", "path_type": "hardlink", "sha256": "f8a70e19eac6782a5e6f5199c3cf6922c3cde46a10ffb71fa85f93cd0cf8f6db", "sha256_in_prefix": "f8a70e19eac6782a5e6f5199c3cf6922c3cde46a10ffb71fa85f93cd0cf8f6db", "size_in_bytes": 5470}, {"_path": "Library/include/freetype/ftbbox.h", "path_type": "hardlink", "sha256": "3d04ed642305afb3b2287ef0aba26577748acd581b6bf5df263770af4a3fedc6", "sha256_in_prefix": "3d04ed642305afb3b2287ef0aba26577748acd581b6bf5df263770af4a3fedc6", "size_in_bytes": 2638}, {"_path": "Library/include/freetype/ftbdf.h", "path_type": "hardlink", "sha256": "84cdb9ad4caadaee343fe2f3591dcea62aeaa5bf21d5c5595214f38906c58310", "sha256_in_prefix": "84cdb9ad4caadaee343fe2f3591dcea62aeaa5bf21d5c5595214f38906c58310", "size_in_bytes": 5322}, {"_path": "Library/include/freetype/ftbitmap.h", "path_type": "hardlink", "sha256": "aba4a5afc772eb002e2fc98ae41dc86b7cbd634a2ccc7c5fcc31861846fa9fc5", "sha256_in_prefix": "aba4a5afc772eb002e2fc98ae41dc86b7cbd634a2ccc7c5fcc31861846fa9fc5", "size_in_bytes": 9051}, {"_path": "Library/include/freetype/ftbzip2.h", "path_type": "hardlink", "sha256": "76c9e1a31ebe293af5989d18c742a33a1869b4cc1ef4dfc3a535fa10a09862cb", "sha256_in_prefix": "76c9e1a31ebe293af5989d18c742a33a1869b4cc1ef4dfc3a535fa10a09862cb", "size_in_bytes": 2786}, {"_path": "Library/include/freetype/ftcache.h", "path_type": "hardlink", "sha256": "ef9835cc69d1bb496605632817d571ecd00691fe986f5e08c552cdf8b56aabd6", "sha256_in_prefix": "ef9835cc69d1bb496605632817d571ecd00691fe986f5e08c552cdf8b56aabd6", "size_in_bytes": 34179}, {"_path": "Library/include/freetype/ftchapters.h", "path_type": "hardlink", "sha256": "c7d1c2ca13903fb6d0b3b37850344d805280856fb87c7597087ce2352573f752", "sha256_in_prefix": "c7d1c2ca13903fb6d0b3b37850344d805280856fb87c7597087ce2352573f752", "size_in_bytes": 2933}, {"_path": "Library/include/freetype/ftcid.h", "path_type": "hardlink", "sha256": "e5c70ccb41b3c50a9958eee55e10407b2190a4afa1640e8c08c251fe1c0f826a", "sha256_in_prefix": "e5c70ccb41b3c50a9958eee55e10407b2190a4afa1640e8c08c251fe1c0f826a", "size_in_bytes": 4022}, {"_path": "Library/include/freetype/ftcolor.h", "path_type": "hardlink", "sha256": "ba543bb2f608e8e1d6c97e7cd3da0a9e7ac23af4586bc3db75ca24750ef94692", "sha256_in_prefix": "ba543bb2f608e8e1d6c97e7cd3da0a9e7ac23af4586bc3db75ca24750ef94692", "size_in_bytes": 50199}, {"_path": "Library/include/freetype/ftdriver.h", "path_type": "hardlink", "sha256": "cf44747d59263798e482ae5b932732b7729b75744014849d1001c4bdd4446c11", "sha256_in_prefix": "cf44747d59263798e482ae5b932732b7729b75744014849d1001c4bdd4446c11", "size_in_bytes": 50576}, {"_path": "Library/include/freetype/fterrdef.h", "path_type": "hardlink", "sha256": "b83e0829b58949afede67e2f8a2fc9b3cafad9e3b4fd11d7ee9c87bd92c6d6c0", "sha256_in_prefix": "b83e0829b58949afede67e2f8a2fc9b3cafad9e3b4fd11d7ee9c87bd92c6d6c0", "size_in_bytes": 12559}, {"_path": "Library/include/freetype/fterrors.h", "path_type": "hardlink", "sha256": "12e955a5cb109519ee4421a03d4a5c324986c8fa01296eaa56e144e2851927ec", "sha256_in_prefix": "12e955a5cb109519ee4421a03d4a5c324986c8fa01296eaa56e144e2851927ec", "size_in_bytes": 9301}, {"_path": "Library/include/freetype/ftfntfmt.h", "path_type": "hardlink", "sha256": "70b57ca7ae6f18403c0bfd2e58ab5e566f42026f25809f7b16fe147e686d4625", "sha256_in_prefix": "70b57ca7ae6f18403c0bfd2e58ab5e566f42026f25809f7b16fe147e686d4625", "size_in_bytes": 2213}, {"_path": "Library/include/freetype/ftgasp.h", "path_type": "hardlink", "sha256": "83af54183584ad16843b3d0d54eb63e4776bd0cce2d4b8a68cd80b718d740b76", "sha256_in_prefix": "83af54183584ad16843b3d0d54eb63e4776bd0cce2d4b8a68cd80b718d740b76", "size_in_bytes": 4138}, {"_path": "Library/include/freetype/ftglyph.h", "path_type": "hardlink", "sha256": "7b13bb1d8501458cd35b7a279c01c1ee89417ceefa516b8eb416d175fe8df9e6", "sha256_in_prefix": "7b13bb1d8501458cd35b7a279c01c1ee89417ceefa516b8eb416d175fe8df9e6", "size_in_bytes": 20912}, {"_path": "Library/include/freetype/ftgxval.h", "path_type": "hardlink", "sha256": "ff9b9e6d8bac9008c3f3404db8eefbe086632ba3dcbd925e59934d87e771535c", "sha256_in_prefix": "ff9b9e6d8bac9008c3f3404db8eefbe086632ba3dcbd925e59934d87e771535c", "size_in_bytes": 10625}, {"_path": "Library/include/freetype/ftgzip.h", "path_type": "hardlink", "sha256": "56d7d349af43263a2507cbb7260669f8acd4db2dce08f7a7e7df9d701144de12", "sha256_in_prefix": "56d7d349af43263a2507cbb7260669f8acd4db2dce08f7a7e7df9d701144de12", "size_in_bytes": 4211}, {"_path": "Library/include/freetype/ftimage.h", "path_type": "hardlink", "sha256": "6245bd165b8a2f5ea6615be6cfc51aa8327ecbea45f42cbce2b8fb625f84574a", "sha256_in_prefix": "6245bd165b8a2f5ea6615be6cfc51aa8327ecbea45f42cbce2b8fb625f84574a", "size_in_bytes": 42150}, {"_path": "Library/include/freetype/ftincrem.h", "path_type": "hardlink", "sha256": "b5bbf21a6d94d500dd334a99eaf27364fd701d696d8891fd1df013aa0a223b3b", "sha256_in_prefix": "b5bbf21a6d94d500dd334a99eaf27364fd701d696d8891fd1df013aa0a223b3b", "size_in_bytes": 10696}, {"_path": "Library/include/freetype/ftlcdfil.h", "path_type": "hardlink", "sha256": "96f6c9bac843c30f3d329760e8f5ea061d958ba302682d69a50d953c193f73bf", "sha256_in_prefix": "96f6c9bac843c30f3d329760e8f5ea061d958ba302682d69a50d953c193f73bf", "size_in_bytes": 11744}, {"_path": "Library/include/freetype/ftlist.h", "path_type": "hardlink", "sha256": "47d82894f2c3e187b8659d1966fdba44348c1739a88751a5a42a5a97c7a209ce", "sha256_in_prefix": "47d82894f2c3e187b8659d1966fdba44348c1739a88751a5a42a5a97c7a209ce", "size_in_bytes": 7100}, {"_path": "Library/include/freetype/ftlogging.h", "path_type": "hardlink", "sha256": "adfcb1f6c6ff73b6bfed707c74d7c22e2aea7528de728588eb051b4ecb18f279", "sha256_in_prefix": "adfcb1f6c6ff73b6bfed707c74d7c22e2aea7528de728588eb051b4ecb18f279", "size_in_bytes": 4130}, {"_path": "Library/include/freetype/ftlzw.h", "path_type": "hardlink", "sha256": "2d5bf1d9075275776e9be7becd793ac079f52982f7db70ec87ea764f637a4fcd", "sha256_in_prefix": "2d5bf1d9075275776e9be7becd793ac079f52982f7db70ec87ea764f637a4fcd", "size_in_bytes": 2768}, {"_path": "Library/include/freetype/ftmac.h", "path_type": "hardlink", "sha256": "d39f3c3c117dea90597fc51d6bf51c6e6b3708ac60a5fb52e311d90b04da542b", "sha256_in_prefix": "d39f3c3c117dea90597fc51d6bf51c6e6b3708ac60a5fb52e311d90b04da542b", "size_in_bytes": 7771}, {"_path": "Library/include/freetype/ftmm.h", "path_type": "hardlink", "sha256": "173d1d20553eec5163d9bb18a78acb3a0059e2879618b9edc5632a3e7a6252a3", "sha256_in_prefix": "173d1d20553eec5163d9bb18a78acb3a0059e2879618b9edc5632a3e7a6252a3", "size_in_bytes": 24595}, {"_path": "Library/include/freetype/ftmodapi.h", "path_type": "hardlink", "sha256": "3b05e210b4705017d68e00def6820749fd0fca2f185ab2bb5df06d71928e29eb", "sha256_in_prefix": "3b05e210b4705017d68e00def6820749fd0fca2f185ab2bb5df06d71928e29eb", "size_in_bytes": 22544}, {"_path": "Library/include/freetype/ftmoderr.h", "path_type": "hardlink", "sha256": "5d09e4a254f2f6272ae2661720124cae7678f7b846eb536ff728ae2c62f8bde3", "sha256_in_prefix": "5d09e4a254f2f6272ae2661720124cae7678f7b846eb536ff728ae2c62f8bde3", "size_in_bytes": 6675}, {"_path": "Library/include/freetype/ftotval.h", "path_type": "hardlink", "sha256": "21b618c37fb5443b790eac4dd28ec6116240f8aeb3f24f310332bb40928f8060", "sha256_in_prefix": "21b618c37fb5443b790eac4dd28ec6116240f8aeb3f24f310332bb40928f8060", "size_in_bytes": 5346}, {"_path": "Library/include/freetype/ftoutln.h", "path_type": "hardlink", "sha256": "b88abdf1e02e5a463f24e892e69408210816b24aec44bfe2ce2a01531fc483b9", "sha256_in_prefix": "b88abdf1e02e5a463f24e892e69408210816b24aec44bfe2ce2a01531fc483b9", "size_in_bytes": 17403}, {"_path": "Library/include/freetype/ftparams.h", "path_type": "hardlink", "sha256": "8c91f1d3a6e6461b43bd42afd99cbfea9a4cdfa0f4f135c708a50e835af30b44", "sha256_in_prefix": "8c91f1d3a6e6461b43bd42afd99cbfea9a4cdfa0f4f135c708a50e835af30b44", "size_in_bytes": 6041}, {"_path": "Library/include/freetype/ftpfr.h", "path_type": "hardlink", "sha256": "e93451ece1f96472c80019dc755c68c2e7aff48b1bd6229b3c86aad41e6ccdee", "sha256_in_prefix": "e93451ece1f96472c80019dc755c68c2e7aff48b1bd6229b3c86aad41e6ccdee", "size_in_bytes": 4908}, {"_path": "Library/include/freetype/ftrender.h", "path_type": "hardlink", "sha256": "4faeb7a50ed0ce94d3cdc39de7496055c71f87c26c889784dfa934faf104a0ca", "sha256_in_prefix": "4faeb7a50ed0ce94d3cdc39de7496055c71f87c26c889784dfa934faf104a0ca", "size_in_bytes": 6625}, {"_path": "Library/include/freetype/ftsizes.h", "path_type": "hardlink", "sha256": "2f0fea779f9ebf3feea44ff217464767b0269866161d77e180104876ff64de9c", "sha256_in_prefix": "2f0fea779f9ebf3feea44ff217464767b0269866161d77e180104876ff64de9c", "size_in_bytes": 4288}, {"_path": "Library/include/freetype/ftsnames.h", "path_type": "hardlink", "sha256": "f28c45069c82b704e769d63cea39ace70ccecf959a3d10becde6c149c92ccc96", "sha256_in_prefix": "f28c45069c82b704e769d63cea39ace70ccecf959a3d10becde6c149c92ccc96", "size_in_bytes": 7730}, {"_path": "Library/include/freetype/ftstroke.h", "path_type": "hardlink", "sha256": "9fc84bf5969448b1adfac20e4920e614fc03a5d5abe53403a8e969593257c125", "sha256_in_prefix": "9fc84bf5969448b1adfac20e4920e614fc03a5d5abe53403a8e969593257c125", "size_in_bytes": 21773}, {"_path": "Library/include/freetype/ftsynth.h", "path_type": "hardlink", "sha256": "18c685e88e016b33878c29fc3ef08cf11c4a244aab2fc58782ebcbd26ee9b615", "sha256_in_prefix": "18c685e88e016b33878c29fc3ef08cf11c4a244aab2fc58782ebcbd26ee9b615", "size_in_bytes": 4483}, {"_path": "Library/include/freetype/ftsystem.h", "path_type": "hardlink", "sha256": "07371a83dafbe99e8575a839da3adb69cc2c5a1c8ebe49c3bc9d40f1baf04078", "sha256_in_prefix": "07371a83dafbe99e8575a839da3adb69cc2c5a1c8ebe49c3bc9d40f1baf04078", "size_in_bytes": 8502}, {"_path": "Library/include/freetype/fttrigon.h", "path_type": "hardlink", "sha256": "6de2b389169541a159f10af662f7ca8b5f87e2301c4c7932bbb7030e92659bf6", "sha256_in_prefix": "6de2b389169541a159f10af662f7ca8b5f87e2301c4c7932bbb7030e92659bf6", "size_in_bytes": 7411}, {"_path": "Library/include/freetype/fttypes.h", "path_type": "hardlink", "sha256": "29dd7f8b460633aa04d4eb7313893e43093643036dd025d384d7b1cc20f71375", "sha256_in_prefix": "29dd7f8b460633aa04d4eb7313893e43093643036dd025d384d7b1cc20f71375", "size_in_bytes": 14735}, {"_path": "Library/include/freetype/ftwinfnt.h", "path_type": "hardlink", "sha256": "55c4863ad6a470209512bc3f3a930af6b691a9f1529ad50227c214aac08143d2", "sha256_in_prefix": "55c4863ad6a470209512bc3f3a930af6b691a9f1529ad50227c214aac08143d2", "size_in_bytes": 7965}, {"_path": "Library/include/freetype/otsvg.h", "path_type": "hardlink", "sha256": "15e331a5e93bed1a870d2ef4628184df85a0b22922cd735b0bcdf9910f3901db", "sha256_in_prefix": "15e331a5e93bed1a870d2ef4628184df85a0b22922cd735b0bcdf9910f3901db", "size_in_bytes": 10457}, {"_path": "Library/include/freetype/t1tables.h", "path_type": "hardlink", "sha256": "cba11e1aa574916074f6cb2bd2073f437bc418c961981447b4daad297f6e8535", "sha256_in_prefix": "cba11e1aa574916074f6cb2bd2073f437bc418c961981447b4daad297f6e8535", "size_in_bytes": 21529}, {"_path": "Library/include/freetype/ttnameid.h", "path_type": "hardlink", "sha256": "2a819379237a37af33ee7e63ef8a49c958f767e375177b859b6c3f95fce098de", "sha256_in_prefix": "2a819379237a37af33ee7e63ef8a49c958f767e375177b859b6c3f95fce098de", "size_in_bytes": 58769}, {"_path": "Library/include/freetype/tttables.h", "path_type": "hardlink", "sha256": "8d8e40ed7c940464831527c171f801daf4b6eae67315d309e379ae51a53e1e6d", "sha256_in_prefix": "8d8e40ed7c940464831527c171f801daf4b6eae67315d309e379ae51a53e1e6d", "size_in_bytes": 25306}, {"_path": "Library/include/freetype/tttags.h", "path_type": "hardlink", "sha256": "1bee1d2d0b427d3003617e693f320971104a886c0c60e94e3309740b28a3df83", "sha256_in_prefix": "1bee1d2d0b427d3003617e693f320971104a886c0c60e94e3309740b28a3df83", "size_in_bytes": 5145}, {"_path": "Library/include/freetype2/dlg/dlg.h", "path_type": "hardlink", "sha256": "d4b983841fd8810f4e26912a8122956d07d48ce43f07b40e7f3042be5f706925", "sha256_in_prefix": "d4b983841fd8810f4e26912a8122956d07d48ce43f07b40e7f3042be5f706925", "size_in_bytes": 12449}, {"_path": "Library/include/freetype2/dlg/output.h", "path_type": "hardlink", "sha256": "d659aad12eea8c26d317ad2601bb9d2ceabff1f7dcb0fbf8ab3038f6d1c457d1", "sha256_in_prefix": "d659aad12eea8c26d317ad2601bb9d2ceabff1f7dcb0fbf8ab3038f6d1c457d1", "size_in_bytes": 7229}, {"_path": "Library/include/ft2build.h", "path_type": "hardlink", "sha256": "4a6bc059d8e53b189649793aafd26e737d9df0ffafd97b5d6bcbad711bf2548d", "sha256_in_prefix": "4a6bc059d8e53b189649793aafd26e737d9df0ffafd97b5d6bcbad711bf2548d", "size_in_bytes": 990}, {"_path": "Library/lib/cmake/freetype/freetype-config-release.cmake", "path_type": "hardlink", "sha256": "013ffdec1415cf01eef68a0f4c8de8e9c9720706ed5a1256b378892098c35c6f", "sha256_in_prefix": "013ffdec1415cf01eef68a0f4c8de8e9c9720706ed5a1256b378892098c35c6f", "size_in_bytes": 884}, {"_path": "Library/lib/cmake/freetype/freetype-config-version.cmake", "path_type": "hardlink", "sha256": "bd3b3e1cd4479a63a3fc9f5094160e8914190df6cd234c2d0e25050fdd39f065", "sha256_in_prefix": "bd3b3e1cd4479a63a3fc9f5094160e8914190df6cd234c2d0e25050fdd39f065", "size_in_bytes": 2830}, {"_path": "Library/lib/cmake/freetype/freetype-config.cmake", "path_type": "hardlink", "sha256": "630bbbc3919d9eeee1f472d5e76f3d4cfcf9d4efb02f901f5d51f299e69f10cb", "sha256_in_prefix": "630bbbc3919d9eeee1f472d5e76f3d4cfcf9d4efb02f901f5d51f299e69f10cb", "size_in_bytes": 4425}, {"_path": "Library/lib/freetype.lib", "path_type": "hardlink", "sha256": "e733cbc8da87faec5b885f0f7f3bd99470e888462d26e5a8d309eb83663e9a5c", "sha256_in_prefix": "e733cbc8da87faec5b885f0f7f3bd99470e888462d26e5a8d309eb83663e9a5c", "size_in_bytes": 49620}, {"_path": "Library/lib/pkgconfig/freetype2.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "C:/b/abs_d8bgeppn_i/croot/freetype_1743636637368/_h_env", "sha256": "3ae1107fccb4dd0555e849b20ee8b9982fdab24046a4084cd50a89b6e9aa8a5b", "sha256_in_prefix": "614283432253ddd386cbd256bb30c088a6ab1b8a73bd803fb26fa99022bb9fcd", "size_in_bytes": 399}], "paths_version": 1}, "requested_spec": "None", "sha256": "59d0ce2dd52326896e7a01ef709a3b4b5f8db8e38e4d7be4942dc4acfbb2b900", "size": 567641, "subdir": "win-64", "timestamp": 1743636775000, "url": "https://repo.anaconda.com/pkgs/main/win-64/freetype-2.13.3-h0620614_0.conda", "version": "2.13.3"}