{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": ["cuda-command-line-tools 12.8.1.*", "cuda-visual-tools 12.8.1.*"], "extracted_package_dir": "", "files": [], "fn": "cuda-tools-12.8.1-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "", "type": 1}, "md5": "d1ffbf2e42af3b59a56f91000c98e662", "name": "cuda-tools", "package_tarball_full_path": "", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "fc21299e02961b9b890c9a011fb25cd3417920e6142c68579fa7f287fa1b4957", "size": 16985, "subdir": "win-64", "timestamp": 1741063845000, "url": "https://conda.anaconda.org/nvidia/win-64/cuda-tools-12.8.1-0.conda", "version": "12.8.1"}