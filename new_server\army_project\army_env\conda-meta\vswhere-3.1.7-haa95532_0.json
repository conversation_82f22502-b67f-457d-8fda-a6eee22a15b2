{"build": "haa95532_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": [], "extracted_package_dir": "", "files": ["Library/bin/vswhere.exe"], "fn": "vswhere-3.1.7-haa95532_0.conda", "license": "MIT", "link": {"source": "", "type": 1}, "md5": "d212902881dbdc6eff9cc98edf166c07", "name": "vswhere", "package_tarball_full_path": "", "paths_data": {"paths": [{"_path": "Library/bin/vswhere.exe", "path_type": "hardlink", "sha256": "c54f3b7c9164ea9a0db8641e81ecdda80c2664ef5a47c4191406f848cc07c662", "sha256_in_prefix": "c54f3b7c9164ea9a0db8641e81ecdda80c2664ef5a47c4191406f848cc07c662", "size_in_bytes": 469456}], "paths_version": 1}, "requested_spec": "None", "sha256": "7cd94847dcf136e20024abc4244e5d19da56d28b0bc79dce99bec31b09d26756", "size": 246071, "subdir": "win-64", "timestamp": 1754999523000, "url": "https://repo.anaconda.com/pkgs/main/win-64/vswhere-3.1.7-haa95532_0.conda", "version": "3.1.7"}