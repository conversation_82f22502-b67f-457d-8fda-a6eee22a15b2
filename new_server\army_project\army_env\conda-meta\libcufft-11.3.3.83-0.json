{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.8,<12.9.0a0"], "extracted_package_dir": "", "files": ["Library/bin/cufft64_11.dll", "Library/bin/cufftw64_11.dll"], "fn": "libcufft-11.3.3.83-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "", "type": 1}, "md5": "19a3064cc9c082241f22e6b6534f9937", "name": "libcu<PERSON>t", "package_tarball_full_path": "", "paths_data": {"paths": [{"_path": "Library/bin/cufft64_11.dll", "path_type": "hardlink", "sha256": "f4fea9227b14843894ad5436725f9638b172171142c95291fc6ae7a493248221", "sha256_in_prefix": "f4fea9227b14843894ad5436725f9638b172171142c95291fc6ae7a493248221", "size_in_bytes": 276121600}, {"_path": "Library/bin/cufftw64_11.dll", "path_type": "hardlink", "sha256": "84c86dcc4d4b770e75766964149ef688c901dc89e380278fe399dd3c03608541", "sha256_in_prefix": "84c86dcc4d4b770e75766964149ef688c901dc89e380278fe399dd3c03608541", "size_in_bytes": 163328}], "paths_version": 1}, "requested_spec": "None", "sha256": "2101b5df3d434b75be62a3d4b1eaf9613aab5012924d581e5c74d7ba76d51723", "size": 154662655, "subdir": "win-64", "timestamp": 1741046451000, "url": "https://conda.anaconda.org/nvidia/win-64/libcufft-11.3.3.83-0.conda", "version": "11.3.3.83"}