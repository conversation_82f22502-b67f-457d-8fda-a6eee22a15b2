{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.8,<12.9.0a0", "cuda-nvrtc 12.8.93 0"], "extracted_package_dir": "", "files": ["Library/include/nvrtc.h", "Library/lib/nvrtc.lib"], "fn": "cuda-nvrtc-dev-12.8.93-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "", "type": 1}, "md5": "83629f3bbf9fce061830bfe9c790f932", "name": "cuda-nvrtc-dev", "package_tarball_full_path": "", "paths_data": {"paths": [{"_path": "Library/include/nvrtc.h", "path_type": "hardlink", "sha256": "85d79ff186392c1b710cffd871001d24261eb681439dc04f58c94074704964ca", "sha256_in_prefix": "85d79ff186392c1b710cffd871001d24261eb681439dc04f58c94074704964ca", "size_in_bytes": 45402}, {"_path": "Library/lib/nvrtc.lib", "path_type": "hardlink", "sha256": "2a02fcd7872dd2443bc873af1cb2416943c37f346d2a0bcaa4c7d0871eb8b646", "sha256_in_prefix": "2a02fcd7872dd2443bc873af1cb2416943c37f346d2a0bcaa4c7d0871eb8b646", "size_in_bytes": 7636}], "paths_version": 1}, "requested_spec": "None", "sha256": "b5a2972dd25e51958a308840c0d3216f4893469e6185c48e68cb98ad0cc03584", "size": 28247, "subdir": "win-64", "timestamp": 1740205147000, "url": "https://conda.anaconda.org/nvidia/win-64/cuda-nvrtc-dev-12.8.93-0.conda", "version": "12.8.93"}