==> 2025-08-13 16:06:48 <==
# cmd: C:\Users\<USER>\miniconda3\Scripts\conda-script.py create -n army_env python=3.9.23
# conda version: 24.11.3
+defaults/noarch::pip-25.1-pyhc872135_2
+defaults/noarch::tzdata-2025b-h04d1e81_0
+defaults/win-64::bzip2-1.0.8-h2bbff1b_6
+defaults/win-64::ca-certificates-2025.7.15-haa95532_0
+defaults/win-64::expat-2.7.1-h8ddb27b_0
+defaults/win-64::libffi-3.4.4-hd77b12b_1
+defaults/win-64::openssl-3.0.17-h35632f6_0
+defaults/win-64::python-3.9.23-h716150d_0
+defaults/win-64::setuptools-78.1.1-py39haa95532_0
+defaults/win-64::sqlite-3.50.2-hda9a48d_1
+defaults/win-64::tk-8.6.14-h5e9d12e_1
+defaults/win-64::ucrt-10.0.22621.0-haa95532_0
+defaults/win-64::vc-14.3-h2df5915_10
+defaults/win-64::vc14_runtime-14.44.35208-h4927774_10
+defaults/win-64::vs2015_runtime-14.44.35208-ha6b5a95_10
+defaults/win-64::wheel-0.45.1-py39haa95532_0
+defaults/win-64::xz-5.6.4-h4754444_1
+defaults/win-64::zlib-1.2.13-h8cc25b3_1
# update specs: ['python=3.9.23']
==> 2025-08-13 17:54:49 <==
# cmd: C:\Users\<USER>\miniconda3\Scripts\conda-script.py install cuda -c nvidia/label/cuda-12.8.0
# conda version: 24.11.3
+defaults/noarch::cuda-12.4.1-h382c6e5_0
+defaults/noarch::cuda-cccl_win-64-12.4.127-haa95532_2
+defaults/noarch::cuda-crt-dev_win-64-12.4.131-haa95532_0
+defaults/noarch::cuda-cudart-dev_win-64-12.4.127-hd77b12b_0
+defaults/noarch::cuda-cudart-static_win-64-12.4.127-hd77b12b_0
+defaults/noarch::cuda-cudart_win-64-12.4.127-hd77b12b_0
+defaults/noarch::cuda-nvcc-dev_win-64-12.4.131-haa95532_0
+defaults/noarch::cuda-nvvm-dev_win-64-12.4.131-haa95532_0
+defaults/noarch::cuda-runtime-12.4.1-h382c6e5_0
+defaults/noarch::cuda-toolkit-12.4.1-h382c6e5_0
+defaults/noarch::cuda-version-12.4-hbda6634_3
+defaults/win-64::cccl-2.3.2-h47f531a_0
+defaults/win-64::cuda-cccl-12.4.127-haa95532_2
+defaults/win-64::cuda-command-line-tools-12.4.1-haa95532_1
+defaults/win-64::cuda-compiler-12.4.1-hd77b12b_1
+defaults/win-64::cuda-crt-tools-12.4.131-haa95532_0
+defaults/win-64::cuda-cudart-12.4.127-hd77b12b_0
+defaults/win-64::cuda-cudart-dev-12.4.127-hd77b12b_0
+defaults/win-64::cuda-cudart-static-12.4.127-hd77b12b_0
+defaults/win-64::cuda-cuobjdump-12.4.127-hd77b12b_1
+defaults/win-64::cuda-cupti-12.4.127-hd77b12b_1
+defaults/win-64::cuda-cupti-dev-12.4.127-hd77b12b_1
+defaults/win-64::cuda-cuxxfilt-12.4.127-hd77b12b_1
+defaults/win-64::cuda-libraries-12.4.1-haa95532_1
+defaults/win-64::cuda-libraries-dev-12.4.1-haa95532_1
+defaults/win-64::cuda-nvcc-12.4.131-h1fd813f_0
+defaults/win-64::cuda-nvcc-impl-12.4.131-h35fed64_0
+defaults/win-64::cuda-nvcc-tools-12.4.131-hd77b12b_0
+defaults/win-64::cuda-nvcc_win-64-12.4.131-h1fd813f_0
+defaults/win-64::cuda-nvdisasm-12.4.127-hd77b12b_1
+defaults/win-64::cuda-nvml-dev-12.4.127-hd77b12b_1
+defaults/win-64::cuda-nvprof-12.4.127-hd77b12b_1
+defaults/win-64::cuda-nvprune-12.4.127-hd77b12b_1
+defaults/win-64::cuda-nvrtc-12.4.127-hd77b12b_1
+defaults/win-64::cuda-nvrtc-dev-12.4.127-hd77b12b_1
+defaults/win-64::cuda-nvvm-impl-12.4.131-hd77b12b_0
+defaults/win-64::cuda-nvvm-tools-12.4.131-hd77b12b_0
+defaults/win-64::cuda-nvvp-12.4.127-hd77b12b_1
+defaults/win-64::cuda-opencl-12.4.127-hd77b12b_0
+defaults/win-64::cuda-opencl-dev-12.4.127-hd77b12b_0
+defaults/win-64::cuda-profiler-api-12.4.127-haa95532_1
+defaults/win-64::cuda-sanitizer-api-12.4.127-hd77b12b_1
+defaults/win-64::cuda-tools-12.4.1-haa95532_1
+defaults/win-64::cuda-visual-tools-12.4.1-haa95532_1
+defaults/win-64::fontconfig-2.14.1-hb33846d_3
+defaults/win-64::freetype-2.13.3-h0620614_0
+defaults/win-64::khronos-opencl-icd-loader-2024.05.08-h8cc25b3_0
+defaults/win-64::libcublas-12.4.5.8-hd77b12b_1
+defaults/win-64::libcublas-dev-12.4.5.8-hd77b12b_1
+defaults/win-64::libcufft-11.2.1.3-hd77b12b_1
+defaults/win-64::libcufft-dev-11.2.1.3-hd77b12b_1
+defaults/win-64::libcurand-10.3.5.147-hd77b12b_1
+defaults/win-64::libcurand-dev-10.3.5.147-hd77b12b_1
+defaults/win-64::libcusolver-11.6.1.9-hd77b12b_1
+defaults/win-64::libcusolver-dev-11.6.1.9-hd77b12b_1
+defaults/win-64::libcusparse-12.3.1.170-hd77b12b_1
+defaults/win-64::libcusparse-dev-12.3.1.170-hd77b12b_1
+defaults/win-64::libglib-2.84.2-h405b238_0
+defaults/win-64::libiconv-1.16-h2bbff1b_3
+defaults/win-64::libkrb5-1.21.3-h885b0b7_4
+defaults/win-64::libnpp-12.2.5.30-hd77b12b_1
+defaults/win-64::libnpp-dev-12.2.5.30-hd77b12b_1
+defaults/win-64::libnvfatbin-12.4.127-h20ee8b7_2
+defaults/win-64::libnvfatbin-dev-12.4.127-h20ee8b7_2
+defaults/win-64::libnvjitlink-12.4.127-hd77b12b_1
+defaults/win-64::libnvjitlink-dev-12.4.127-hd77b12b_1
+defaults/win-64::libnvjpeg-**********-hd77b12b_1
+defaults/win-64::libnvjpeg-dev-**********-haa95532_1
+defaults/win-64::libpng-1.6.39-h8cc25b3_0
+defaults/win-64::libxml2-2.13.8-h866ff63_0
+defaults/win-64::nsight-compute-2024.1.1.4-h37906d1_3
+defaults/win-64::pcre2-10.42-h0ff8eda_1
+defaults/win-64::vs2017_win-64-19.16.27032.1-hb4161e2_3
+defaults/win-64::vswhere-3.1.7-haa95532_0
# update specs: ['cuda']
==> 2025-08-13 18:12:00 <==
# cmd: C:\Users\<USER>\miniconda3\Scripts\conda-script.py install cuda=12.8 -c nvidia
# conda version: 24.11.3
-defaults/noarch::cuda-12.4.1-h382c6e5_0
-defaults/noarch::cuda-cccl_win-64-12.4.127-haa95532_2
-defaults/noarch::cuda-crt-dev_win-64-12.4.131-haa95532_0
-defaults/noarch::cuda-cudart-dev_win-64-12.4.127-hd77b12b_0
-defaults/noarch::cuda-cudart-static_win-64-12.4.127-hd77b12b_0
-defaults/noarch::cuda-cudart_win-64-12.4.127-hd77b12b_0
-defaults/noarch::cuda-nvcc-dev_win-64-12.4.131-haa95532_0
-defaults/noarch::cuda-nvvm-dev_win-64-12.4.131-haa95532_0
-defaults/noarch::cuda-runtime-12.4.1-h382c6e5_0
-defaults/noarch::cuda-toolkit-12.4.1-h382c6e5_0
-defaults/noarch::cuda-version-12.4-hbda6634_3
-defaults/win-64::cuda-cccl-12.4.127-haa95532_2
-defaults/win-64::cuda-command-line-tools-12.4.1-haa95532_1
-defaults/win-64::cuda-compiler-12.4.1-hd77b12b_1
-defaults/win-64::cuda-crt-tools-12.4.131-haa95532_0
-defaults/win-64::cuda-cudart-12.4.127-hd77b12b_0
-defaults/win-64::cuda-cudart-dev-12.4.127-hd77b12b_0
-defaults/win-64::cuda-cudart-static-12.4.127-hd77b12b_0
-defaults/win-64::cuda-cuobjdump-12.4.127-hd77b12b_1
-defaults/win-64::cuda-cupti-12.4.127-hd77b12b_1
-defaults/win-64::cuda-cupti-dev-12.4.127-hd77b12b_1
-defaults/win-64::cuda-cuxxfilt-12.4.127-hd77b12b_1
-defaults/win-64::cuda-libraries-12.4.1-haa95532_1
-defaults/win-64::cuda-libraries-dev-12.4.1-haa95532_1
-defaults/win-64::cuda-nvcc-12.4.131-h1fd813f_0
-defaults/win-64::cuda-nvcc-impl-12.4.131-h35fed64_0
-defaults/win-64::cuda-nvcc-tools-12.4.131-hd77b12b_0
-defaults/win-64::cuda-nvcc_win-64-12.4.131-h1fd813f_0
-defaults/win-64::cuda-nvdisasm-12.4.127-hd77b12b_1
-defaults/win-64::cuda-nvml-dev-12.4.127-hd77b12b_1
-defaults/win-64::cuda-nvprof-12.4.127-hd77b12b_1
-defaults/win-64::cuda-nvprune-12.4.127-hd77b12b_1
-defaults/win-64::cuda-nvrtc-12.4.127-hd77b12b_1
-defaults/win-64::cuda-nvrtc-dev-12.4.127-hd77b12b_1
-defaults/win-64::cuda-nvvm-impl-12.4.131-hd77b12b_0
-defaults/win-64::cuda-nvvm-tools-12.4.131-hd77b12b_0
-defaults/win-64::cuda-nvvp-12.4.127-hd77b12b_1
-defaults/win-64::cuda-opencl-12.4.127-hd77b12b_0
-defaults/win-64::cuda-opencl-dev-12.4.127-hd77b12b_0
-defaults/win-64::cuda-profiler-api-12.4.127-haa95532_1
-defaults/win-64::cuda-sanitizer-api-12.4.127-hd77b12b_1
-defaults/win-64::cuda-tools-12.4.1-haa95532_1
-defaults/win-64::cuda-visual-tools-12.4.1-haa95532_1
-defaults/win-64::libcublas-12.4.5.8-hd77b12b_1
-defaults/win-64::libcublas-dev-12.4.5.8-hd77b12b_1
-defaults/win-64::libcufft-11.2.1.3-hd77b12b_1
-defaults/win-64::libcufft-dev-11.2.1.3-hd77b12b_1
-defaults/win-64::libcurand-10.3.5.147-hd77b12b_1
-defaults/win-64::libcurand-dev-10.3.5.147-hd77b12b_1
-defaults/win-64::libcusolver-11.6.1.9-hd77b12b_1
-defaults/win-64::libcusolver-dev-11.6.1.9-hd77b12b_1
-defaults/win-64::libcusparse-12.3.1.170-hd77b12b_1
-defaults/win-64::libcusparse-dev-12.3.1.170-hd77b12b_1
-defaults/win-64::libnpp-12.2.5.30-hd77b12b_1
-defaults/win-64::libnpp-dev-12.2.5.30-hd77b12b_1
-defaults/win-64::libnvfatbin-12.4.127-h20ee8b7_2
-defaults/win-64::libnvfatbin-dev-12.4.127-h20ee8b7_2
-defaults/win-64::libnvjitlink-12.4.127-hd77b12b_1
-defaults/win-64::libnvjitlink-dev-12.4.127-hd77b12b_1
-defaults/win-64::libnvjpeg-**********-hd77b12b_1
-defaults/win-64::libnvjpeg-dev-**********-haa95532_1
-defaults/win-64::nsight-compute-2024.1.1.4-h37906d1_3
+nvidia/noarch::cuda-12.8.1-0
+nvidia/noarch::cuda-compiler-12.8.1-0
+nvidia/noarch::cuda-crt-dev_win-64-12.8.93-0
+nvidia/noarch::cuda-cudart-dev_win-64-12.8.90-0
+nvidia/noarch::cuda-cudart-static_win-64-12.8.90-0
+nvidia/noarch::cuda-cudart_win-64-12.8.90-0
+nvidia/noarch::cuda-nvcc-dev_win-64-12.8.93-0
+nvidia/noarch::cuda-nvvm-dev_win-64-12.8.93-0
+nvidia/noarch::cuda-runtime-12.8.1-0
+nvidia/noarch::cuda-toolkit-12.8.1-0
+nvidia/noarch::cuda-version-12.8-3
+nvidia/win-64::cuda-cccl-12.8.90-0
+nvidia/win-64::cuda-cccl_win-64-12.8.90-0
+nvidia/win-64::cuda-command-line-tools-12.8.1-0
+nvidia/win-64::cuda-crt-tools-12.8.93-0
+nvidia/win-64::cuda-cudart-12.8.90-0
+nvidia/win-64::cuda-cudart-dev-12.8.90-0
+nvidia/win-64::cuda-cudart-static-12.8.90-0
+nvidia/win-64::cuda-cuobjdump-12.8.90-0
+nvidia/win-64::cuda-cupti-12.8.90-0
+nvidia/win-64::cuda-cupti-dev-12.8.90-0
+nvidia/win-64::cuda-cuxxfilt-12.8.90-0
+nvidia/win-64::cuda-libraries-12.8.1-0
+nvidia/win-64::cuda-libraries-dev-12.8.1-0
+nvidia/win-64::cuda-nvcc-12.8.93-0
+nvidia/win-64::cuda-nvcc-impl-12.8.93-0
+nvidia/win-64::cuda-nvcc-tools-12.8.93-0
+nvidia/win-64::cuda-nvcc_win-64-12.8.93-0
+nvidia/win-64::cuda-nvdisasm-12.8.90-0
+nvidia/win-64::cuda-nvml-dev-12.8.90-0
+nvidia/win-64::cuda-nvprof-12.8.90-0
+nvidia/win-64::cuda-nvprune-12.8.90-0
+nvidia/win-64::cuda-nvrtc-12.8.93-0
+nvidia/win-64::cuda-nvrtc-dev-12.8.93-0
+nvidia/win-64::cuda-nvvm-impl-12.8.93-0
+nvidia/win-64::cuda-nvvm-tools-12.8.93-0
+nvidia/win-64::cuda-nvvp-12.8.93-0
+nvidia/win-64::cuda-opencl-12.8.90-0
+nvidia/win-64::cuda-opencl-dev-12.8.90-0
+nvidia/win-64::cuda-profiler-api-12.8.90-0
+nvidia/win-64::cuda-sanitizer-api-12.8.93-0
+nvidia/win-64::cuda-tools-12.8.1-0
+nvidia/win-64::cuda-visual-tools-12.8.1-0
+nvidia/win-64::libcublas-12.8.4.1-0
+nvidia/win-64::libcublas-dev-12.8.4.1-0
+nvidia/win-64::libcufft-11.3.3.83-0
+nvidia/win-64::libcufft-dev-11.3.3.83-0
+nvidia/win-64::libcurand-10.3.9.90-0
+nvidia/win-64::libcurand-dev-10.3.9.90-0
+nvidia/win-64::libcusolver-11.7.3.90-0
+nvidia/win-64::libcusolver-dev-11.7.3.90-0
+nvidia/win-64::libcusparse-1********-0
+nvidia/win-64::libcusparse-dev-1********-0
+nvidia/win-64::libnpp-**********-0
+nvidia/win-64::libnpp-dev-**********-0
+nvidia/win-64::libnvfatbin-12.8.90-0
+nvidia/win-64::libnvfatbin-dev-12.8.90-0
+nvidia/win-64::libnvjitlink-12.8.93-1
+nvidia/win-64::libnvjitlink-dev-12.8.93-1
+nvidia/win-64::libnvjpeg-*********-0
+nvidia/win-64::libnvjpeg-dev-*********-0
+nvidia/win-64::nsight-compute-2025.1.1.2-0
# update specs: ['cuda=12.8']
==> 2025-08-13 19:16:58 <==
# cmd: C:\Users\<USER>\miniconda3\Scripts\conda-script.py install -c conda-forge conda-pack
# conda version: 24.11.3
-defaults/win-64::ca-certificates-2025.7.15-haa95532_0
-defaults/win-64::openssl-3.0.17-h35632f6_0
+conda-forge/noarch::ca-certificates-2025.8.3-h4c7d964_0
+conda-forge/noarch::conda-pack-0.8.1-pyhd8ed1ab_1
+conda-forge/win-64::openssl-3.5.2-h725018a_0
# update specs: ['conda-pack']
