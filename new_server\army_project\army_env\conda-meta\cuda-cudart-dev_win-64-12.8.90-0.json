{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/noarch", "constrains": [], "depends": ["cuda-cudart-static_win-64", "cuda-cudar<PERSON>_win-64", "cuda-cccl_win-64", "cuda-version >=12.8,<12.9.0a0"], "extracted_package_dir": "", "files": ["Library/include/builtin_types.h", "Library/include/channel_descriptor.h", "Library/include/common_functions.h", "Library/include/cooperative_groups.h", "Library/include/cooperative_groups/details/async.h", "Library/include/cooperative_groups/details/coalesced_reduce.h", "Library/include/cooperative_groups/details/coalesced_scan.h", "Library/include/cooperative_groups/details/driver_abi.h", "Library/include/cooperative_groups/details/functional.h", "Library/include/cooperative_groups/details/helpers.h", "Library/include/cooperative_groups/details/info.h", "Library/include/cooperative_groups/details/invoke.h", "Library/include/cooperative_groups/details/memory.h", "Library/include/cooperative_groups/details/partitioning.h", "Library/include/cooperative_groups/details/reduce.h", "Library/include/cooperative_groups/details/scan.h", "Library/include/cooperative_groups/details/sync.h", "Library/include/cooperative_groups/memcpy_async.h", "Library/include/cooperative_groups/reduce.h", "Library/include/cooperative_groups/scan.h", "Library/include/cuComplex.h", "Library/include/cuda.h", "Library/include/cudaD3D10.h", "Library/include/cudaD3D10Typedefs.h", "Library/include/cudaD3D11.h", "Library/include/cudaD3D11Typedefs.h", "Library/include/cudaD3D9.h", "Library/include/cudaD3D9Typedefs.h", "Library/include/cudaGL.h", "Library/include/cudaGLTypedefs.h", "Library/include/cudaProfilerTypedefs.h", "Library/include/cudaTypedefs.h", "Library/include/cuda_awbarrier.h", "Library/include/cuda_awbarrier_helpers.h", "Library/include/cuda_awbarrier_primitives.h", "Library/include/cuda_bf16.h", "Library/include/cuda_bf16.hpp", "Library/include/cuda_d3d10_interop.h", "Library/include/cuda_d3d11_interop.h", "Library/include/cuda_d3d9_interop.h", "Library/include/cuda_device_runtime_api.h", "Library/include/cuda_egl_interop.h", "Library/include/cuda_fp16.h", "Library/include/cuda_fp16.hpp", "Library/include/cuda_fp4.h", "Library/include/cuda_fp4.hpp", "Library/include/cuda_fp6.h", "Library/include/cuda_fp6.hpp", "Library/include/cuda_fp8.h", "Library/include/cuda_fp8.hpp", "Library/include/cuda_gl_interop.h", "Library/include/cuda_occupancy.h", "Library/include/cuda_pipeline.h", "Library/include/cuda_pipeline_helpers.h", "Library/include/cuda_pipeline_primitives.h", "Library/include/cuda_runtime.h", "Library/include/cuda_runtime_api.h", "Library/include/cuda_surface_types.h", "Library/include/cuda_texture_types.h", "Library/include/cudart_platform.h", "Library/include/device_atomic_functions.h", "Library/include/device_atomic_functions.hpp", "Library/include/device_double_functions.h", "Library/include/device_functions.h", "Library/include/device_launch_parameters.h", "Library/include/device_types.h", "Library/include/driver_functions.h", "Library/include/driver_types.h", "Library/include/host_config.h", "Library/include/host_defines.h", "Library/include/library_types.h", "Library/include/math_constants.h", "Library/include/math_functions.h", "Library/include/mma.h", "Library/include/nvfunctional", "Library/include/sm_20_atomic_functions.h", "Library/include/sm_20_atomic_functions.hpp", "Library/include/sm_20_intrinsics.h", "Library/include/sm_20_intrinsics.hpp", "Library/include/sm_30_intrinsics.h", "Library/include/sm_30_intrinsics.hpp", "Library/include/sm_32_atomic_functions.h", "Library/include/sm_32_atomic_functions.hpp", "Library/include/sm_32_intrinsics.h", "Library/include/sm_32_intrinsics.hpp", "Library/include/sm_35_atomic_functions.h", "Library/include/sm_35_intrinsics.h", "Library/include/sm_60_atomic_functions.h", "Library/include/sm_60_atomic_functions.hpp", "Library/include/sm_61_intrinsics.h", "Library/include/sm_61_intrinsics.hpp", "Library/include/surface_functions.h", "Library/include/surface_indirect_functions.h", "Library/include/surface_types.h", "Library/include/texture_fetch_functions.h", "Library/include/texture_indirect_functions.h", "Library/include/texture_types.h", "Library/include/vector_functions.h", "Library/include/vector_functions.hpp", "Library/include/vector_types.h", "Library/lib/cuda.lib", "Library/lib/cudadevrt.lib", "Library/lib/cudart.lib"], "fn": "cuda-cudart-dev_win-64-12.8.90-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "", "type": 1}, "md5": "51f9353106e64cf75f1151dd4f0b3f1f", "name": "cuda-cudart-dev_win-64", "noarch": "generic", "package_tarball_full_path": "", "package_type": "noarch_generic", "paths_data": {"paths": [{"_path": "Library/include/builtin_types.h", "path_type": "hardlink", "sha256": "e75d7426f7b4e8abcd6e3cdd0631fa9577a6c5f8cd31f1ff2996e79d9b5ab4a3", "sha256_in_prefix": "e75d7426f7b4e8abcd6e3cdd0631fa9577a6c5f8cd31f1ff2996e79d9b5ab4a3", "size_in_bytes": 3214}, {"_path": "Library/include/channel_descriptor.h", "path_type": "hardlink", "sha256": "cae3e4f0be2931e031ba983399754984bf3bb41255302ffbcacac47ec39bc202", "sha256_in_prefix": "cae3e4f0be2931e031ba983399754984bf3bb41255302ffbcacac47ec39bc202", "size_in_bytes": 22443}, {"_path": "Library/include/common_functions.h", "path_type": "hardlink", "sha256": "8c2f53f4d2b46fcf8adffb1403d15568ec39be63bd98c2f26fef2d670f056dd7", "sha256_in_prefix": "8c2f53f4d2b46fcf8adffb1403d15568ec39be63bd98c2f26fef2d670f056dd7", "size_in_bytes": 3475}, {"_path": "Library/include/cooperative_groups.h", "path_type": "hardlink", "sha256": "5f042da90008ce4af671926c5683669948a3eceb9d39743ecdd6881cbfa96156", "sha256_in_prefix": "5f042da90008ce4af671926c5683669948a3eceb9d39743ecdd6881cbfa96156", "size_in_bytes": 62427}, {"_path": "Library/include/cooperative_groups/details/async.h", "path_type": "hardlink", "sha256": "fa8fdafc1a55c292b45506c7276c8b8d26952e073c386c5ac52f5cfaf2cc5ef1", "sha256_in_prefix": "fa8fdafc1a55c292b45506c7276c8b8d26952e073c386c5ac52f5cfaf2cc5ef1", "size_in_bytes": 19574}, {"_path": "Library/include/cooperative_groups/details/coalesced_reduce.h", "path_type": "hardlink", "sha256": "03d1e18f77c39bdf2cf68ffd727f3837c504bd0232dad19845ac903ad8f4b842", "sha256_in_prefix": "03d1e18f77c39bdf2cf68ffd727f3837c504bd0232dad19845ac903ad8f4b842", "size_in_bytes": 4352}, {"_path": "Library/include/cooperative_groups/details/coalesced_scan.h", "path_type": "hardlink", "sha256": "3273c657f293c1cd66096a066120c5512fdd391715de323ee55ffce1ee22d6ba", "sha256_in_prefix": "3273c657f293c1cd66096a066120c5512fdd391715de323ee55ffce1ee22d6ba", "size_in_bytes": 7487}, {"_path": "Library/include/cooperative_groups/details/driver_abi.h", "path_type": "hardlink", "sha256": "234b5d0be27516cc2f34d2243f7e64469cc779d63d432abd0e6dae77303efcf8", "sha256_in_prefix": "234b5d0be27516cc2f34d2243f7e64469cc779d63d432abd0e6dae77303efcf8", "size_in_bytes": 4063}, {"_path": "Library/include/cooperative_groups/details/functional.h", "path_type": "hardlink", "sha256": "d66e968211b98265ee813ff74d2cf94cfc49a514c090c1283432ffdb565f3948", "sha256_in_prefix": "d66e968211b98265ee813ff74d2cf94cfc49a514c090c1283432ffdb565f3948", "size_in_bytes": 9117}, {"_path": "Library/include/cooperative_groups/details/helpers.h", "path_type": "hardlink", "sha256": "7e3ee03ac590af58646ebb2eb3a791880e67b21b065221a3265affcd5deb5922", "sha256_in_prefix": "7e3ee03ac590af58646ebb2eb3a791880e67b21b065221a3265affcd5deb5922", "size_in_bytes": 27169}, {"_path": "Library/include/cooperative_groups/details/info.h", "path_type": "hardlink", "sha256": "0ff5810d50abfb17d96627972b30f9f856d85f1dd82f0e841d6d3a15d3e005ee", "sha256_in_prefix": "0ff5810d50abfb17d96627972b30f9f856d85f1dd82f0e841d6d3a15d3e005ee", "size_in_bytes": 12810}, {"_path": "Library/include/cooperative_groups/details/invoke.h", "path_type": "hardlink", "sha256": "60729e6b39912e209a91d9c63710528c932278e3de9a317614ef02f76df4e2a9", "sha256_in_prefix": "60729e6b39912e209a91d9c63710528c932278e3de9a317614ef02f76df4e2a9", "size_in_bytes": 8805}, {"_path": "Library/include/cooperative_groups/details/memory.h", "path_type": "hardlink", "sha256": "7d8bafdd01a7fd6ffefc656cc2bf34c3ccd975cc464e678b42ff3676b09b7d70", "sha256_in_prefix": "7d8bafdd01a7fd6ffefc656cc2bf34c3ccd975cc464e678b42ff3676b09b7d70", "size_in_bytes": 5742}, {"_path": "Library/include/cooperative_groups/details/partitioning.h", "path_type": "hardlink", "sha256": "948bfb1ae79397473a6d5475394a67bbf91aaf8164c7a6cb9c0e944bdda9e3c2", "sha256_in_prefix": "948bfb1ae79397473a6d5475394a67bbf91aaf8164c7a6cb9c0e944bdda9e3c2", "size_in_bytes": 7313}, {"_path": "Library/include/cooperative_groups/details/reduce.h", "path_type": "hardlink", "sha256": "76b358f11ac30b45eb8df272f43c118ccb584c3d02702facecad0832b29895d5", "sha256_in_prefix": "76b358f11ac30b45eb8df272f43c118ccb584c3d02702facecad0832b29895d5", "size_in_bytes": 23720}, {"_path": "Library/include/cooperative_groups/details/scan.h", "path_type": "hardlink", "sha256": "a455988d98bc04f52cee25b37066533d9a3083e93d37eae1af17f5643164c83c", "sha256_in_prefix": "a455988d98bc04f52cee25b37066533d9a3083e93d37eae1af17f5643164c83c", "size_in_bytes": 17486}, {"_path": "Library/include/cooperative_groups/details/sync.h", "path_type": "hardlink", "sha256": "79c962c6005385a6566edbbce65d1fd1e033cf10a9abf20f68d117cd4c821148", "sha256_in_prefix": "79c962c6005385a6566edbbce65d1fd1e033cf10a9abf20f68d117cd4c821148", "size_in_bytes": 11076}, {"_path": "Library/include/cooperative_groups/memcpy_async.h", "path_type": "hardlink", "sha256": "0e05ef6e5c38acb6be4f678d23338ba12ddc43c37a9f9a29363f38b1e48c3352", "sha256_in_prefix": "0e05ef6e5c38acb6be4f678d23338ba12ddc43c37a9f9a29363f38b1e48c3352", "size_in_bytes": 3022}, {"_path": "Library/include/cooperative_groups/reduce.h", "path_type": "hardlink", "sha256": "9a86fd41b5ae66ec5abdf03a131c8f13207ae21e0a2e1e4c99fcade6748e647d", "sha256_in_prefix": "9a86fd41b5ae66ec5abdf03a131c8f13207ae21e0a2e1e4c99fcade6748e647d", "size_in_bytes": 3012}, {"_path": "Library/include/cooperative_groups/scan.h", "path_type": "hardlink", "sha256": "c1131b91109572fabf6f8d91a91f1b48c0cee960537b0381aeabb9e02bbf94b9", "sha256_in_prefix": "c1131b91109572fabf6f8d91a91f1b48c0cee960537b0381aeabb9e02bbf94b9", "size_in_bytes": 3003}, {"_path": "Library/include/cuComplex.h", "path_type": "hardlink", "sha256": "8c04f75b56a2d57ad07aa59d8cb903fcabd6366658a89dad00a4b4a00939150f", "sha256_in_prefix": "8c04f75b56a2d57ad07aa59d8cb903fcabd6366658a89dad00a4b4a00939150f", "size_in_bytes": 12534}, {"_path": "Library/include/cuda.h", "path_type": "hardlink", "sha256": "167e383a3791226c5e809dfd6a9614b29b1e11f4d6346c29a921943a71e3e566", "sha256_in_prefix": "167e383a3791226c5e809dfd6a9614b29b1e11f4d6346c29a921943a71e3e566", "size_in_bytes": 1183268}, {"_path": "Library/include/cudaD3D10.h", "path_type": "hardlink", "sha256": "5305ee1d46db529ed2aa94719c8e641a96874e86cdf3db994eb8f4b6b38c65d5", "sha256_in_prefix": "5305ee1d46db529ed2aa94719c8e641a96874e86cdf3db994eb8f4b6b38c65d5", "size_in_bytes": 32930}, {"_path": "Library/include/cudaD3D10Typedefs.h", "path_type": "hardlink", "sha256": "f4eeb57be3783e56086f3b4f7aa3349d170b27f33e7575fcb16e1d29f1d5226c", "sha256_in_prefix": "f4eeb57be3783e56086f3b4f7aa3349d170b27f33e7575fcb16e1d29f1d5226c", "size_in_bytes": 7333}, {"_path": "Library/include/cudaD3D11.h", "path_type": "hardlink", "sha256": "895853fb979ba1f086fdf706f8a13ca8eff35f613391badadfb590ecb5cdd9bb", "sha256_in_prefix": "895853fb979ba1f086fdf706f8a13ca8eff35f613391badadfb590ecb5cdd9bb", "size_in_bytes": 14444}, {"_path": "Library/include/cudaD3D11Typedefs.h", "path_type": "hardlink", "sha256": "edb21242e2ab914b8c658da0463dedb786cf34995926629669ffe39b11ee8965", "sha256_in_prefix": "edb21242e2ab914b8c658da0463dedb786cf34995926629669ffe39b11ee8965", "size_in_bytes": 4484}, {"_path": "Library/include/cudaD3D9.h", "path_type": "hardlink", "sha256": "a6f4d42a14dae5c56dfdc3abce11e14a7f84af40ec71e1c6696d4969447119fa", "sha256_in_prefix": "a6f4d42a14dae5c56dfdc3abce11e14a7f84af40ec71e1c6696d4969447119fa", "size_in_bytes": 37091}, {"_path": "Library/include/cudaD3D9Typedefs.h", "path_type": "hardlink", "sha256": "e2e94f7c2d0f4bf23ccbca2e18854899fbcc0aab34a886be748959b2a2e2908f", "sha256_in_prefix": "e2e94f7c2d0f4bf23ccbca2e18854899fbcc0aab34a886be748959b2a2e2908f", "size_in_bytes": 8533}, {"_path": "Library/include/cudaGL.h", "path_type": "hardlink", "sha256": "299bf3c583aca0eba8344263a33c596e07bf6647b7f2ed125af9040381d5f1f7", "sha256_in_prefix": "299bf3c583aca0eba8344263a33c596e07bf6647b7f2ed125af9040381d5f1f7", "size_in_bytes": 23109}, {"_path": "Library/include/cudaGLTypedefs.h", "path_type": "hardlink", "sha256": "e52f155f43813a55a6a8d361731cef9058d4563cd98a330ab4a87621704af543", "sha256_in_prefix": "e52f155f43813a55a6a8d361731cef9058d4563cd98a330ab4a87621704af543", "size_in_bytes": 6699}, {"_path": "Library/include/cudaProfilerTypedefs.h", "path_type": "hardlink", "sha256": "c8983a9a436d2ffbda06c32d6ea4fbcd6a25aa3eb20b0c4d04da8b97d4030eec", "sha256_in_prefix": "c8983a9a436d2ffbda06c32d6ea4fbcd6a25aa3eb20b0c4d04da8b97d4030eec", "size_in_bytes": 3375}, {"_path": "Library/include/cudaTypedefs.h", "path_type": "hardlink", "sha256": "d6532a6e1a9b1111aa3369733c31e8eb6d4c673a8de823891f1727dcfcde9c31", "sha256_in_prefix": "d6532a6e1a9b1111aa3369733c31e8eb6d4c673a8de823891f1727dcfcde9c31", "size_in_bytes": 116313}, {"_path": "Library/include/cuda_awbarrier.h", "path_type": "hardlink", "sha256": "425d45616992029b798ad78a8f53553a15fe76c62bf3c9ff6c72c65a855fc3af", "sha256_in_prefix": "425d45616992029b798ad78a8f53553a15fe76c62bf3c9ff6c72c65a855fc3af", "size_in_bytes": 9620}, {"_path": "Library/include/cuda_awbarrier_helpers.h", "path_type": "hardlink", "sha256": "3fb7af882e74b95c329e0be94efbbd4b164a4a1bda0b81926fde6396e5b6b65c", "sha256_in_prefix": "3fb7af882e74b95c329e0be94efbbd4b164a4a1bda0b81926fde6396e5b6b65c", "size_in_bytes": 12854}, {"_path": "Library/include/cuda_awbarrier_primitives.h", "path_type": "hardlink", "sha256": "73e548b39b2867086da71dfd2c70268ea211c0caa4d0adc801be6919d239db14", "sha256_in_prefix": "73e548b39b2867086da71dfd2c70268ea211c0caa4d0adc801be6919d239db14", "size_in_bytes": 4808}, {"_path": "Library/include/cuda_bf16.h", "path_type": "hardlink", "sha256": "694545f099c6610687d2e4b5c0bdb4cbc640083f5954e2072f610c9b240d711e", "sha256_in_prefix": "694545f099c6610687d2e4b5c0bdb4cbc640083f5954e2072f610c9b240d711e", "size_in_bytes": 209630}, {"_path": "Library/include/cuda_bf16.hpp", "path_type": "hardlink", "sha256": "f1f7ab583de61a284125d59ee70d601f3a4630972170dc385e4847aac8b3a164", "sha256_in_prefix": "f1f7ab583de61a284125d59ee70d601f3a4630972170dc385e4847aac8b3a164", "size_in_bytes": 140409}, {"_path": "Library/include/cuda_d3d10_interop.h", "path_type": "hardlink", "sha256": "55039312e63d29dde634e4e707fbd22c4e439739dadac4e0abd03a506e29d981", "sha256_in_prefix": "55039312e63d29dde634e4e707fbd22c4e439739dadac4e0abd03a506e29d981", "size_in_bytes": 29316}, {"_path": "Library/include/cuda_d3d11_interop.h", "path_type": "hardlink", "sha256": "ce04ecb5dccb3b27e48b97c1b1f8aa801bb44760ff575e60750aaac78d543d14", "sha256_in_prefix": "ce04ecb5dccb3b27e48b97c1b1f8aa801bb44760ff575e60750aaac78d543d14", "size_in_bytes": 12787}, {"_path": "Library/include/cuda_d3d9_interop.h", "path_type": "hardlink", "sha256": "50ad1133b84981533ad42e74c8a6afc73a9d83c9f6182311801a5ccf3e7c6e84", "sha256_in_prefix": "50ad1133b84981533ad42e74c8a6afc73a9d83c9f6182311801a5ccf3e7c6e84", "size_in_bytes": 31646}, {"_path": "Library/include/cuda_device_runtime_api.h", "path_type": "hardlink", "sha256": "6e873b11f541b8d08b2800c7758fc4930a5250f4b8154b5f4f820ce8cb3e0ae1", "sha256_in_prefix": "6e873b11f541b8d08b2800c7758fc4930a5250f4b8154b5f4f820ce8cb3e0ae1", "size_in_bytes": 47900}, {"_path": "Library/include/cuda_egl_interop.h", "path_type": "hardlink", "sha256": "64ae1e0a083c5ffedc2a4e39cb729c8a3e56cbc676de45e4041e582f821482b2", "sha256_in_prefix": "64ae1e0a083c5ffedc2a4e39cb729c8a3e56cbc676de45e4041e582f821482b2", "size_in_bytes": 38154}, {"_path": "Library/include/cuda_fp16.h", "path_type": "hardlink", "sha256": "2dba9ce73e70be5343d186f6306d846c8eecd58c2de694ebc9f9bd888c0021c3", "sha256_in_prefix": "2dba9ce73e70be5343d186f6306d846c8eecd58c2de694ebc9f9bd888c0021c3", "size_in_bytes": 212214}, {"_path": "Library/include/cuda_fp16.hpp", "path_type": "hardlink", "sha256": "8e472e719393c64be6a4e0a74b2489bb869431906b854be36a4c7a4703fc04aa", "sha256_in_prefix": "8e472e719393c64be6a4e0a74b2489bb869431906b854be36a4c7a4703fc04aa", "size_in_bytes": 124410}, {"_path": "Library/include/cuda_fp4.h", "path_type": "hardlink", "sha256": "9fab28f598e98de19f50950b63269b3bd72de9e20170135ee88175a999dec262", "sha256_in_prefix": "9fab28f598e98de19f50950b63269b3bd72de9e20170135ee88175a999dec262", "size_in_bytes": 14180}, {"_path": "Library/include/cuda_fp4.hpp", "path_type": "hardlink", "sha256": "b450877d1323eac844d20e0613bf276fc5e144c28d4041870288c0c70da245ee", "sha256_in_prefix": "b450877d1323eac844d20e0613bf276fc5e144c28d4041870288c0c70da245ee", "size_in_bytes": 36376}, {"_path": "Library/include/cuda_fp6.h", "path_type": "hardlink", "sha256": "870d0daaa39c028b63d0b221917b9e4ca93e0120db196f1050b3cc81c7c4a4ca", "sha256_in_prefix": "870d0daaa39c028b63d0b221917b9e4ca93e0120db196f1050b3cc81c7c4a4ca", "size_in_bytes": 14325}, {"_path": "Library/include/cuda_fp6.hpp", "path_type": "hardlink", "sha256": "f2c66f462e6c047b6d1ef5d1c852cb0c42c5e09bec1fe88f72b3a68291944f22", "sha256_in_prefix": "f2c66f462e6c047b6d1ef5d1c852cb0c42c5e09bec1fe88f72b3a68291944f22", "size_in_bytes": 58004}, {"_path": "Library/include/cuda_fp8.h", "path_type": "hardlink", "sha256": "967dc74c5a0fcf01336c685e798f1f4d3128b7d4ce8c1c1eb2740ad45c6c6025", "sha256_in_prefix": "967dc74c5a0fcf01336c685e798f1f4d3128b7d4ce8c1c1eb2740ad45c6c6025", "size_in_bytes": 18547}, {"_path": "Library/include/cuda_fp8.hpp", "path_type": "hardlink", "sha256": "eb9abad48909b4878ee721c81ee6ef2ab771ae6eb7c6649747daececaff2ca8c", "sha256_in_prefix": "eb9abad48909b4878ee721c81ee6ef2ab771ae6eb7c6649747daececaff2ca8c", "size_in_bytes": 100010}, {"_path": "Library/include/cuda_gl_interop.h", "path_type": "hardlink", "sha256": "a4a4041049b59206543d55fdc6c0720b245afd933bf3c298e9d9a2f4a8ac1de1", "sha256_in_prefix": "a4a4041049b59206543d55fdc6c0720b245afd933bf3c298e9d9a2f4a8ac1de1", "size_in_bytes": 19664}, {"_path": "Library/include/cuda_occupancy.h", "path_type": "hardlink", "sha256": "b1f203d41cd32a13905033970ae34818ac55600213f3735705741f3322209a99", "sha256_in_prefix": "b1f203d41cd32a13905033970ae34818ac55600213f3735705741f3322209a99", "size_in_bytes": 73396}, {"_path": "Library/include/cuda_pipeline.h", "path_type": "hardlink", "sha256": "aacbf316553e1a34a8f13398ec720846f41cebb70fdedb7fdc97aa0093932262", "sha256_in_prefix": "aacbf316553e1a34a8f13398ec720846f41cebb70fdedb7fdc97aa0093932262", "size_in_bytes": 8354}, {"_path": "Library/include/cuda_pipeline_helpers.h", "path_type": "hardlink", "sha256": "f59a26516dea3b56384ac636c6128b5fc920abc3493bd4bb910306157efa2713", "sha256_in_prefix": "f59a26516dea3b56384ac636c6128b5fc920abc3493bd4bb910306157efa2713", "size_in_bytes": 14225}, {"_path": "Library/include/cuda_pipeline_primitives.h", "path_type": "hardlink", "sha256": "98ac2abfed83ecfe4eddbbf12f824146ef987aedabc0fa94c87b15849feba514", "sha256_in_prefix": "98ac2abfed83ecfe4eddbbf12f824146ef987aedabc0fa94c87b15849feba514", "size_in_bytes": 8823}, {"_path": "Library/include/cuda_runtime.h", "path_type": "hardlink", "sha256": "00fafc879eda880a860c195933ce97a0b306fc82761388d1b2fcf4d2ee200e1e", "sha256_in_prefix": "00fafc879eda880a860c195933ce97a0b306fc82761388d1b2fcf4d2ee200e1e", "size_in_bytes": 101161}, {"_path": "Library/include/cuda_runtime_api.h", "path_type": "hardlink", "sha256": "6c60f63697575ff18156d397ec506ab286b147f33a5ce9e72f95bfec162fd3ea", "sha256_in_prefix": "6c60f63697575ff18156d397ec506ab286b147f33a5ce9e72f95bfec162fd3ea", "size_in_bytes": 670876}, {"_path": "Library/include/cuda_surface_types.h", "path_type": "hardlink", "sha256": "64fc83dc785b0b1ac9e6ed68d26342a4547382f7a6a97140d8aad205dad8c1e9", "sha256_in_prefix": "64fc83dc785b0b1ac9e6ed68d26342a4547382f7a6a97140d8aad205dad8c1e9", "size_in_bytes": 3764}, {"_path": "Library/include/cuda_texture_types.h", "path_type": "hardlink", "sha256": "e46d1704740d6a0fa338ce66cd9023d0fe432082114fbab9c06acbf69a5f20f9", "sha256_in_prefix": "e46d1704740d6a0fa338ce66cd9023d0fe432082114fbab9c06acbf69a5f20f9", "size_in_bytes": 3764}, {"_path": "Library/include/cudart_platform.h", "path_type": "hardlink", "sha256": "533a3dc955c7e7b3c6bd3b6f6298364c832657311f5b9e3e8e6608c84550dd03", "sha256_in_prefix": "533a3dc955c7e7b3c6bd3b6f6298364c832657311f5b9e3e8e6608c84550dd03", "size_in_bytes": 2774}, {"_path": "Library/include/device_atomic_functions.h", "path_type": "hardlink", "sha256": "873238d1f1ee42428badfddf84c6d40a175de52936e4ec90efa28ca4ad0e1c1f", "sha256_in_prefix": "873238d1f1ee42428badfddf84c6d40a175de52936e4ec90efa28ca4ad0e1c1f", "size_in_bytes": 9693}, {"_path": "Library/include/device_atomic_functions.hpp", "path_type": "hardlink", "sha256": "59f4bb142f75d1c820918187927b1041730b6d571183a69989972dc146a69f3a", "sha256_in_prefix": "59f4bb142f75d1c820918187927b1041730b6d571183a69989972dc146a69f3a", "size_in_bytes": 10740}, {"_path": "Library/include/device_double_functions.h", "path_type": "hardlink", "sha256": "6d320fba0bec4047535f35190b6ed5c3c559ee7ccc081f53a9cfdfe4fe34f97f", "sha256_in_prefix": "6d320fba0bec4047535f35190b6ed5c3c559ee7ccc081f53a9cfdfe4fe34f97f", "size_in_bytes": 3517}, {"_path": "Library/include/device_functions.h", "path_type": "hardlink", "sha256": "030f0f2c4b270b55a2c3402de9c5c7aa52fa967c02c92d38479c5388cab6c7c0", "sha256_in_prefix": "030f0f2c4b270b55a2c3402de9c5c7aa52fa967c02c92d38479c5388cab6c7c0", "size_in_bytes": 3475}, {"_path": "Library/include/device_launch_parameters.h", "path_type": "hardlink", "sha256": "32e3edf2d180dc0e3025e38779db1f10436ab8fd16069121a8e609c5fd6046e3", "sha256_in_prefix": "32e3edf2d180dc0e3025e38779db1f10436ab8fd16069121a8e609c5fd6046e3", "size_in_bytes": 3964}, {"_path": "Library/include/device_types.h", "path_type": "hardlink", "sha256": "2416b764cb308e6d9e93268826f3918f5a05e760d28e49c112ff9c732491e328", "sha256_in_prefix": "2416b764cb308e6d9e93268826f3918f5a05e760d28e49c112ff9c732491e328", "size_in_bytes": 3669}, {"_path": "Library/include/driver_functions.h", "path_type": "hardlink", "sha256": "ab08532254d6d6829ea9a650901900bf0b4588f0d4636fbc634d7ee31874c334", "sha256_in_prefix": "ab08532254d6d6829ea9a650901900bf0b4588f0d4636fbc634d7ee31874c334", "size_in_bytes": 4770}, {"_path": "Library/include/driver_types.h", "path_type": "hardlink", "sha256": "44e902d1f5381ad55fbfb9c18b6970eda8eb73baa71c3a293719fd1d4a273432", "sha256_in_prefix": "44e902d1f5381ad55fbfb9c18b6970eda8eb73baa71c3a293719fd1d4a273432", "size_in_bytes": 204193}, {"_path": "Library/include/host_config.h", "path_type": "hardlink", "sha256": "087908be5214f2483e13630aa1ee26f2cd00e0c2aee6451016c0b46fc359117e", "sha256_in_prefix": "087908be5214f2483e13630aa1ee26f2cd00e0c2aee6451016c0b46fc359117e", "size_in_bytes": 3445}, {"_path": "Library/include/host_defines.h", "path_type": "hardlink", "sha256": "28047eea24100b91fd397e96fb2755f74ac806494fdc39a8b3d15a48bfd7b5ca", "sha256_in_prefix": "28047eea24100b91fd397e96fb2755f74ac806494fdc39a8b3d15a48bfd7b5ca", "size_in_bytes": 3451}, {"_path": "Library/include/library_types.h", "path_type": "hardlink", "sha256": "38c2b1cb0292337a608a802ad0b2ab616b3ceb1a289c61417b2c00f084455bbc", "sha256_in_prefix": "38c2b1cb0292337a608a802ad0b2ab616b3ceb1a289c61417b2c00f084455bbc", "size_in_bytes": 5267}, {"_path": "Library/include/math_constants.h", "path_type": "hardlink", "sha256": "9040ea02b7486f2637998cd9bcadd8dd59ec855a5d7d74f274579fdeefa6a01a", "sha256_in_prefix": "9040ea02b7486f2637998cd9bcadd8dd59ec855a5d7d74f274579fdeefa6a01a", "size_in_bytes": 7760}, {"_path": "Library/include/math_functions.h", "path_type": "hardlink", "sha256": "e556f9d919cc7d7b0a086a6d6cafdcdb9146fb397b82940e868ebaee6746f6aa", "sha256_in_prefix": "e556f9d919cc7d7b0a086a6d6cafdcdb9146fb397b82940e868ebaee6746f6aa", "size_in_bytes": 3463}, {"_path": "Library/include/mma.h", "path_type": "hardlink", "sha256": "9749dd585be009be5aa5bdeab5f05aa3aa9483f0511aa4397746f999a8209934", "sha256_in_prefix": "9749dd585be009be5aa5bdeab5f05aa3aa9483f0511aa4397746f999a8209934", "size_in_bytes": 2992}, {"_path": "Library/include/nvfunctional", "path_type": "hardlink", "sha256": "0538a04f2586c6a26a82818c93d34c13ad226fe88b34010c38974e9c8f22fcd9", "sha256_in_prefix": "0538a04f2586c6a26a82818c93d34c13ad226fe88b34010c38974e9c8f22fcd9", "size_in_bytes": 3035}, {"_path": "Library/include/sm_20_atomic_functions.h", "path_type": "hardlink", "sha256": "91f79dcef4a18056b11365dacafcb14636caa257ea63431e0a02e1e6c9bbb0c2", "sha256_in_prefix": "91f79dcef4a18056b11365dacafcb14636caa257ea63431e0a02e1e6c9bbb0c2", "size_in_bytes": 4579}, {"_path": "Library/include/sm_20_atomic_functions.hpp", "path_type": "hardlink", "sha256": "0bbc694e3ec9c2e70a98d12e455d00ac1bbedfcd1a65c58f52f13ef75a44da9b", "sha256_in_prefix": "0bbc694e3ec9c2e70a98d12e455d00ac1bbedfcd1a65c58f52f13ef75a44da9b", "size_in_bytes": 4199}, {"_path": "Library/include/sm_20_intrinsics.h", "path_type": "hardlink", "sha256": "b46f223f215af661459e46024a5d58f8e2566f75f1e278015d88a6f2eac52736", "sha256_in_prefix": "b46f223f215af661459e46024a5d58f8e2566f75f1e278015d88a6f2eac52736", "size_in_bytes": 58858}, {"_path": "Library/include/sm_20_intrinsics.hpp", "path_type": "hardlink", "sha256": "7a6b8c10602132e9568772698193f36c3ce2dd5f26e60c5420594202bcfb4f80", "sha256_in_prefix": "7a6b8c10602132e9568772698193f36c3ce2dd5f26e60c5420594202bcfb4f80", "size_in_bytes": 8633}, {"_path": "Library/include/sm_30_intrinsics.h", "path_type": "hardlink", "sha256": "ca500ef3ffd4463fff64a86c22d946d61c806edbba0a55921c55bc7c2051e4c7", "sha256_in_prefix": "ca500ef3ffd4463fff64a86c22d946d61c806edbba0a55921c55bc7c2051e4c7", "size_in_bytes": 17214}, {"_path": "Library/include/sm_30_intrinsics.hpp", "path_type": "hardlink", "sha256": "701e63430e8c509fca2ee24eebf55697b53dd3d53128e6d8269f0edd0bc9c0f6", "sha256_in_prefix": "701e63430e8c509fca2ee24eebf55697b53dd3d53128e6d8269f0edd0bc9c0f6", "size_in_bytes": 25171}, {"_path": "Library/include/sm_32_atomic_functions.h", "path_type": "hardlink", "sha256": "2e2d73fea14f23ab168a0bca19f3f34b97da9d9d7ace35309e433ac7ef517a75", "sha256_in_prefix": "2e2d73fea14f23ab168a0bca19f3f34b97da9d9d7ace35309e433ac7ef517a75", "size_in_bytes": 5825}, {"_path": "Library/include/sm_32_atomic_functions.hpp", "path_type": "hardlink", "sha256": "d6cb7d7e7e3961b2f76a2b1bdc6c04008f27ed4e4ee4a18298e3cc3ff0da35b6", "sha256_in_prefix": "d6cb7d7e7e3961b2f76a2b1bdc6c04008f27ed4e4ee4a18298e3cc3ff0da35b6", "size_in_bytes": 6743}, {"_path": "Library/include/sm_32_intrinsics.h", "path_type": "hardlink", "sha256": "f050bcd23c7cba2c21d80ade2abe23457b617d7a9d03dd7506b5a530cb12dd82", "sha256_in_prefix": "f050bcd23c7cba2c21d80ade2abe23457b617d7a9d03dd7506b5a530cb12dd82", "size_in_bytes": 34055}, {"_path": "Library/include/sm_32_intrinsics.hpp", "path_type": "hardlink", "sha256": "74ea3633e759344643ec06bdd061e298a3ad483fcf800b609d26429d5d269057", "sha256_in_prefix": "74ea3633e759344643ec06bdd061e298a3ad483fcf800b609d26429d5d269057", "size_in_bytes": 71204}, {"_path": "Library/include/sm_35_atomic_functions.h", "path_type": "hardlink", "sha256": "7a15dc657fc4844847103e9db16390971c40f5e6db8282acd12586a85309b7b3", "sha256_in_prefix": "7a15dc657fc4844847103e9db16390971c40f5e6db8282acd12586a85309b7b3", "size_in_bytes": 2967}, {"_path": "Library/include/sm_35_intrinsics.h", "path_type": "hardlink", "sha256": "92dac7919499a3611ee4dc86cdbd9eccfa3a0ccd2c8f1d0c2328ca25807cbc3b", "sha256_in_prefix": "92dac7919499a3611ee4dc86cdbd9eccfa3a0ccd2c8f1d0c2328ca25807cbc3b", "size_in_bytes": 2770}, {"_path": "Library/include/sm_60_atomic_functions.h", "path_type": "hardlink", "sha256": "e1ca160ce1a65d42197d8ba578f4fbd00429bf05bea65eca31325e673486e55d", "sha256_in_prefix": "e1ca160ce1a65d42197d8ba578f4fbd00429bf05bea65eca31325e673486e55d", "size_in_bytes": 13508}, {"_path": "Library/include/sm_60_atomic_functions.hpp", "path_type": "hardlink", "sha256": "cad128980da64b7736f942ee50d305c1b0ef3baa04217c757dae9fbf7d7809b8", "sha256_in_prefix": "cad128980da64b7736f942ee50d305c1b0ef3baa04217c757dae9fbf7d7809b8", "size_in_bytes": 23645}, {"_path": "Library/include/sm_61_intrinsics.h", "path_type": "hardlink", "sha256": "2633fa8b29038e6ae94dc205761da8d71e1656ea8bdaf4ddc05a758888ab1a23", "sha256_in_prefix": "2633fa8b29038e6ae94dc205761da8d71e1656ea8bdaf4ddc05a758888ab1a23", "size_in_bytes": 11140}, {"_path": "Library/include/sm_61_intrinsics.hpp", "path_type": "hardlink", "sha256": "1c2b2f68dd5513ac773b42e6feef4e5b64ac16e55ad39288d51b951f7e576bf8", "sha256_in_prefix": "1c2b2f68dd5513ac773b42e6feef4e5b64ac16e55ad39288d51b951f7e576bf8", "size_in_bytes": 6948}, {"_path": "Library/include/surface_functions.h", "path_type": "hardlink", "sha256": "aa9aa51007376b766123ee8b6db105d7c20ebe8af4c50faa40bad42b635033ed", "sha256_in_prefix": "aa9aa51007376b766123ee8b6db105d7c20ebe8af4c50faa40bad42b635033ed", "size_in_bytes": 6906}, {"_path": "Library/include/surface_indirect_functions.h", "path_type": "hardlink", "sha256": "082f48c0649bebd37ceb80e81c50d82aa94e2e549d8a013daaae17995c59d790", "sha256_in_prefix": "082f48c0649bebd37ceb80e81c50d82aa94e2e549d8a013daaae17995c59d790", "size_in_bytes": 11120}, {"_path": "Library/include/surface_types.h", "path_type": "hardlink", "sha256": "863530cd094f387b4c4c0ee428055c91ea2733fb0a13c93136ceec9b33827c57", "sha256_in_prefix": "863530cd094f387b4c4c0ee428055c91ea2733fb0a13c93136ceec9b33827c57", "size_in_bytes": 4641}, {"_path": "Library/include/texture_fetch_functions.h", "path_type": "hardlink", "sha256": "f889b1a37fc1360eb5aa98ac1ee90b9bfdda6a67c949275c6b659a9114d101bf", "sha256_in_prefix": "f889b1a37fc1360eb5aa98ac1ee90b9bfdda6a67c949275c6b659a9114d101bf", "size_in_bytes": 12691}, {"_path": "Library/include/texture_indirect_functions.h", "path_type": "hardlink", "sha256": "5d3b7b635072266c4bfc22c46c88ce25dd2eb1510fc4a9608bb07e05054281af", "sha256_in_prefix": "5d3b7b635072266c4bfc22c46c88ce25dd2eb1510fc4a9608bb07e05054281af", "size_in_bytes": 21801}, {"_path": "Library/include/texture_types.h", "path_type": "hardlink", "sha256": "1887d9c33528c4d841b509f0cd586e7602d201274959c53c87ca5917b7e7cfed", "sha256_in_prefix": "1887d9c33528c4d841b509f0cd586e7602d201274959c53c87ca5917b7e7cfed", "size_in_bytes": 6540}, {"_path": "Library/include/vector_functions.h", "path_type": "hardlink", "sha256": "69c863a2b5c66654fca2be925ef94b1b66e6fbb8dfbb768314146fa23775d2ab", "sha256_in_prefix": "69c863a2b5c66654fca2be925ef94b1b66e6fbb8dfbb768314146fa23775d2ab", "size_in_bytes": 8184}, {"_path": "Library/include/vector_functions.hpp", "path_type": "hardlink", "sha256": "c7b15cc4d81a15ba93ccad66b086ea71ac22c524ecb98b6c5d94c7ddcca55757", "sha256_in_prefix": "c7b15cc4d81a15ba93ccad66b086ea71ac22c524ecb98b6c5d94c7ddcca55757", "size_in_bytes": 10376}, {"_path": "Library/include/vector_types.h", "path_type": "hardlink", "sha256": "add5b60c66a0bbdf999dd8475c2b95c713dc9f862e019864c882be8f7b7c4f6e", "sha256_in_prefix": "add5b60c66a0bbdf999dd8475c2b95c713dc9f862e019864c882be8f7b7c4f6e", "size_in_bytes": 13845}, {"_path": "Library/lib/cuda.lib", "path_type": "hardlink", "sha256": "1f2fd7eb50512a5d12d1c1ff8803cc6499a9dbbaa8bb9a8320d4ff771ab7f0b3", "sha256_in_prefix": "1f2fd7eb50512a5d12d1c1ff8803cc6499a9dbbaa8bb9a8320d4ff771ab7f0b3", "size_in_bytes": 160840}, {"_path": "Library/lib/cudadevrt.lib", "path_type": "hardlink", "sha256": "95956e73d8f7da0e490d4849a2c3cc5d98a75dfbf465d9589905f6af861458d2", "sha256_in_prefix": "95956e73d8f7da0e490d4849a2c3cc5d98a75dfbf465d9589905f6af861458d2", "size_in_bytes": 2359756}, {"_path": "Library/lib/cudart.lib", "path_type": "hardlink", "sha256": "0fc74e7cd9e3d68d1dbec554dab94c17d17bc15e489e851fb8b876586bac02b2", "sha256_in_prefix": "0fc74e7cd9e3d68d1dbec554dab94c17d17bc15e489e851fb8b876586bac02b2", "size_in_bytes": 117462}], "paths_version": 1}, "requested_spec": "None", "sha256": "cb5798f0818d76ce5f3a4b4f37c4ba1ca102b0cf77195d96654cd64cd4e0e921", "size": 1037406, "subdir": "noarch", "timestamp": 1739448588000, "url": "https://conda.anaconda.org/nvidia/noarch/cuda-cudart-dev_win-64-12.8.90-0.conda", "version": "12.8.90"}