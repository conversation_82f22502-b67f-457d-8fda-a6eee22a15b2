{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": ["cuda-cupti-static >=12.8.90"], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.8,<12.9.0a0", "cuda-cupti 12.8.90 0"], "extracted_package_dir": "", "files": ["Library/include/cuda_stdint.h", "Library/include/cupti.h", "Library/include/cupti_activity.h", "Library/include/cupti_activity_deprecated.h", "Library/include/cupti_callbacks.h", "Library/include/cupti_checkpoint.h", "Library/include/cupti_common.h", "Library/include/cupti_driver_cbid.h", "Library/include/cupti_events.h", "Library/include/cupti_metrics.h", "Library/include/cupti_nvtx_cbid.h", "Library/include/cupti_pcsampling.h", "Library/include/cupti_pcsampling_util.h", "Library/include/cupti_pmsampling.h", "Library/include/cupti_profiler_host.h", "Library/include/cupti_profiler_target.h", "Library/include/cupti_range_profiler.h", "Library/include/cupti_result.h", "Library/include/cupti_runtime_cbid.h", "Library/include/cupti_sass_metrics.h", "Library/include/cupti_target.h", "Library/include/cupti_version.h", "Library/include/generated_cudaD3D10_meta.h", "Library/include/generated_cudaD3D11_meta.h", "Library/include/generated_cudaD3D9_meta.h", "Library/include/generated_cudaGL_meta.h", "Library/include/generated_cuda_d3d10_interop_meta.h", "Library/include/generated_cuda_d3d11_interop_meta.h", "Library/include/generated_cuda_d3d9_interop_meta.h", "Library/include/generated_cuda_gl_interop_meta.h", "Library/include/generated_cuda_meta.h", "Library/include/generated_cuda_runtime_api_meta.h", "Library/include/generated_cudart_removed_meta.h", "Library/include/generated_nvtx_meta.h", "Library/include/nvperf_common.h", "Library/include/nvperf_cuda_host.h", "Library/include/nvperf_host.h", "Library/include/nvperf_target.h", "Library/lib/checkpoint.lib", "Library/lib/cupti.lib", "Library/lib/nvperf_host.lib", "Library/lib/nvperf_target.lib", "Library/lib/pcsamplingutil.lib"], "fn": "cuda-cupti-dev-12.8.90-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "", "type": 1}, "md5": "5eb5e1acdfbcc71a7b95be8f36fba70c", "name": "cuda-cupti-dev", "package_tarball_full_path": "", "paths_data": {"paths": [{"_path": "Library/include/cuda_stdint.h", "path_type": "hardlink", "sha256": "15cf80a04f152499300fc8a830e19abc1872eec803735692a91282aa07d08751", "sha256_in_prefix": "15cf80a04f152499300fc8a830e19abc1872eec803735692a91282aa07d08751", "size_in_bytes": 4205}, {"_path": "Library/include/cupti.h", "path_type": "hardlink", "sha256": "536e83ba6afddd41cb5f07beb390dd2732e2bb2fdc7cf90da68574c406d09dbc", "sha256_in_prefix": "536e83ba6afddd41cb5f07beb390dd2732e2bb2fdc7cf90da68574c406d09dbc", "size_in_bytes": 4820}, {"_path": "Library/include/cupti_activity.h", "path_type": "hardlink", "sha256": "fd7cb70cf62f6fb4d5d3eba741f30971b7ecb747b5da42c9a59ac1cbcdc728b2", "sha256_in_prefix": "fd7cb70cf62f6fb4d5d3eba741f30971b7ecb747b5da42c9a59ac1cbcdc728b2", "size_in_bytes": 237081}, {"_path": "Library/include/cupti_activity_deprecated.h", "path_type": "hardlink", "sha256": "eb01ced356237e6b1b1aef2685ba13f4cc11118ab56860e8a74d4a724392fdc8", "sha256_in_prefix": "eb01ced356237e6b1b1aef2685ba13f4cc11118ab56860e8a74d4a724392fdc8", "size_in_bytes": 142937}, {"_path": "Library/include/cupti_callbacks.h", "path_type": "hardlink", "sha256": "da47efc991e0d46c0ae1576eaa6f4c06935cf735d26c3d3566ddebfd162d5d51", "sha256_in_prefix": "da47efc991e0d46c0ae1576eaa6f4c06935cf735d26c3d3566ddebfd162d5d51", "size_in_bytes": 30552}, {"_path": "Library/include/cupti_checkpoint.h", "path_type": "hardlink", "sha256": "1c7f8b9a3ce702039bd344c5c3e58208d46526a611fff246e03ea90e36a968e9", "sha256_in_prefix": "1c7f8b9a3ce702039bd344c5c3e58208d46526a611fff246e03ea90e36a968e9", "size_in_bytes": 5391}, {"_path": "Library/include/cupti_common.h", "path_type": "hardlink", "sha256": "fd80cc339e1eb0f3a175ff93b1d0b66ef417c4d5cb730cd327dbe0086a726bc0", "sha256_in_prefix": "fd80cc339e1eb0f3a175ff93b1d0b66ef417c4d5cb730cd327dbe0086a726bc0", "size_in_bytes": 3607}, {"_path": "Library/include/cupti_driver_cbid.h", "path_type": "hardlink", "sha256": "72807af0b90c2d6d661c55419b3fe7a4b689c6291281721da6e6c62d8ed937ea", "sha256_in_prefix": "72807af0b90c2d6d661c55419b3fe7a4b689c6291281721da6e6c62d8ed937ea", "size_in_bytes": 78079}, {"_path": "Library/include/cupti_events.h", "path_type": "hardlink", "sha256": "4f88ea6928e5f14e1a0920463507277cd9561f5821d33ecc95daef5c8d8f6ab0", "sha256_in_prefix": "4f88ea6928e5f14e1a0920463507277cd9561f5821d33ecc95daef5c8d8f6ab0", "size_in_bytes": 53272}, {"_path": "Library/include/cupti_metrics.h", "path_type": "hardlink", "sha256": "fa701722f97f1fdb25957a89d0d3641cc7ec3cb500036a93e5b73fa367ca62e9", "sha256_in_prefix": "fa701722f97f1fdb25957a89d0d3641cc7ec3cb500036a93e5b73fa367ca62e9", "size_in_bytes": 32999}, {"_path": "Library/include/cupti_nvtx_cbid.h", "path_type": "hardlink", "sha256": "bfa701f28a09cb9f91e0134ecdf328400a6c2db284ce16c691de952504af4374", "sha256_in_prefix": "bfa701f28a09cb9f91e0134ecdf328400a6c2db284ce16c691de952504af4374", "size_in_bytes": 6023}, {"_path": "Library/include/cupti_pcsampling.h", "path_type": "hardlink", "sha256": "4d5b87e6cbe65fe0dcc558965c86505ef773455b39750ef94f7ff75a96fce424", "sha256_in_prefix": "4d5b87e6cbe65fe0dcc558965c86505ef773455b39750ef94f7ff75a96fce424", "size_in_bytes": 33080}, {"_path": "Library/include/cupti_pcsampling_util.h", "path_type": "hardlink", "sha256": "eaf1e3537f6b6cdf7c58cb88d17449ef01637ea70e7cdc263bc956f04f593510", "sha256_in_prefix": "eaf1e3537f6b6cdf7c58cb88d17449ef01637ea70e7cdc263bc956f04f593510", "size_in_bytes": 12832}, {"_path": "Library/include/cupti_pmsampling.h", "path_type": "hardlink", "sha256": "184e5a1dba1c9653c50d01349102716ec4be252d243f3c6cec4af2c90ff70177", "sha256_in_prefix": "184e5a1dba1c9653c50d01349102716ec4be252d243f3c6cec4af2c90ff70177", "size_in_bytes": 20930}, {"_path": "Library/include/cupti_profiler_host.h", "path_type": "hardlink", "sha256": "c832adc0e1305852ee2eddd22d2f713924acdee2369efd05e7c9318fa8cab7e4", "sha256_in_prefix": "c832adc0e1305852ee2eddd22d2f713924acdee2369efd05e7c9318fa8cab7e4", "size_in_bytes": 22695}, {"_path": "Library/include/cupti_profiler_target.h", "path_type": "hardlink", "sha256": "00bacd04e459ae43ce69828efd555199fe8af6022b2aa9c817bc992fae64d43f", "sha256_in_prefix": "00bacd04e459ae43ce69828efd555199fe8af6022b2aa9c817bc992fae64d43f", "size_in_bytes": 32896}, {"_path": "Library/include/cupti_range_profiler.h", "path_type": "hardlink", "sha256": "1df5ece839b0e4bab9863cce8ef364bf169ca0eade7521528ff4028e2e1234b9", "sha256_in_prefix": "1df5ece839b0e4bab9863cce8ef364bf169ca0eade7521528ff4028e2e1234b9", "size_in_bytes": 19244}, {"_path": "Library/include/cupti_result.h", "path_type": "hardlink", "sha256": "edac82f0cd69ff7c63b7a075ddfc9dfabfa4009df1f91fb839b031b4b9e1148c", "sha256_in_prefix": "edac82f0cd69ff7c63b7a075ddfc9dfabfa4009df1f91fb839b031b4b9e1148c", "size_in_bytes": 13474}, {"_path": "Library/include/cupti_runtime_cbid.h", "path_type": "hardlink", "sha256": "bc470df9c384f1d4e2c57b594c12ae9fb0358342fb467a32254798570c9dff79", "sha256_in_prefix": "bc470df9c384f1d4e2c57b594c12ae9fb0358342fb467a32254798570c9dff79", "size_in_bytes": 48876}, {"_path": "Library/include/cupti_sass_metrics.h", "path_type": "hardlink", "sha256": "0c71609f41bfbed405b1e05012056957e1afa47716580a83688cfb01f97f2c63", "sha256_in_prefix": "0c71609f41bfbed405b1e05012056957e1afa47716580a83688cfb01f97f2c63", "size_in_bytes": 20109}, {"_path": "Library/include/cupti_target.h", "path_type": "hardlink", "sha256": "9f1c28a369cf043de7dc441880292685ceb6d3cc60489b2e151954f517523229", "sha256_in_prefix": "9f1c28a369cf043de7dc441880292685ceb6d3cc60489b2e151954f517523229", "size_in_bytes": 1306}, {"_path": "Library/include/cupti_version.h", "path_type": "hardlink", "sha256": "b4c10f41b42f1eb04bb91a2ce9b4830ea304ffa33a0fd50d5fbca410f428fef3", "sha256_in_prefix": "b4c10f41b42f1eb04bb91a2ce9b4830ea304ffa33a0fd50d5fbca410f428fef3", "size_in_bytes": 4643}, {"_path": "Library/include/generated_cudaD3D10_meta.h", "path_type": "hardlink", "sha256": "3d21cf541fc64a3e8d111477fc94dc9ebbfed929aea5609eb69602f32745db8a", "sha256_in_prefix": "3d21cf541fc64a3e8d111477fc94dc9ebbfed929aea5609eb69602f32745db8a", "size_in_bytes": 4439}, {"_path": "Library/include/generated_cudaD3D11_meta.h", "path_type": "hardlink", "sha256": "fa87ddc95dab6f95e25d319aea7c64020f673e42e6b1ab963a3710bbc461d16d", "sha256_in_prefix": "fa87ddc95dab6f95e25d319aea7c64020f673e42e6b1ab963a3710bbc461d16d", "size_in_bytes": 1823}, {"_path": "Library/include/generated_cudaD3D9_meta.h", "path_type": "hardlink", "sha256": "89a25b6eee7cbc560ecaee18b97d9007122d1f4f62a096dae0296f30655fddd0", "sha256_in_prefix": "89a25b6eee7cbc560ecaee18b97d9007122d1f4f62a096dae0296f30655fddd0", "size_in_bytes": 5379}, {"_path": "Library/include/generated_cudaGL_meta.h", "path_type": "hardlink", "sha256": "67e4b157a27f114e8cdd3c6cffac917ac38672a6ce0931f47c640eb99b371117", "sha256_in_prefix": "67e4b157a27f114e8cdd3c6cffac917ac38672a6ce0931f47c640eb99b371117", "size_in_bytes": 3344}, {"_path": "Library/include/generated_cuda_d3d10_interop_meta.h", "path_type": "hardlink", "sha256": "76c86ea44b708215f9290a048fc4a0504e8b7c9af6b14c0deff203622548a13f", "sha256_in_prefix": "76c86ea44b708215f9290a048fc4a0504e8b7c9af6b14c0deff203622548a13f", "size_in_bytes": 3364}, {"_path": "Library/include/generated_cuda_d3d11_interop_meta.h", "path_type": "hardlink", "sha256": "9d29f4ec811d51a1625b1d7cb5643d160853e64fd77c0cbd6af988329eec1e6e", "sha256_in_prefix": "9d29f4ec811d51a1625b1d7cb5643d160853e64fd77c0cbd6af988329eec1e6e", "size_in_bytes": 1500}, {"_path": "Library/include/generated_cuda_d3d9_interop_meta.h", "path_type": "hardlink", "sha256": "928816f80222c48cabc8976f3c998288778cd55d1f465cd4d32373a7f7084c41", "sha256_in_prefix": "928816f80222c48cabc8976f3c998288778cd55d1f465cd4d32373a7f7084c41", "size_in_bytes": 4178}, {"_path": "Library/include/generated_cuda_gl_interop_meta.h", "path_type": "hardlink", "sha256": "a312356f67f7bfb4fca94967306235e4488adb5e05f2bd15edc9c1588a003d8b", "sha256_in_prefix": "a312356f67f7bfb4fca94967306235e4488adb5e05f2bd15edc9c1588a003d8b", "size_in_bytes": 2492}, {"_path": "Library/include/generated_cuda_meta.h", "path_type": "hardlink", "sha256": "2e3c215fb5620b286bf60d360a071cc2e7803bf93ba75fb169aae37dfdb2d6dd", "sha256_in_prefix": "2e3c215fb5620b286bf60d360a071cc2e7803bf93ba75fb169aae37dfdb2d6dd", "size_in_bytes": 102382}, {"_path": "Library/include/generated_cuda_runtime_api_meta.h", "path_type": "hardlink", "sha256": "b010e49d2ada8f3e6a6c716f0baa936128cf6efed9849187952f8e7ab8b7952d", "sha256_in_prefix": "b010e49d2ada8f3e6a6c716f0baa936128cf6efed9849187952f8e7ab8b7952d", "size_in_bytes": 74580}, {"_path": "Library/include/generated_cudart_removed_meta.h", "path_type": "hardlink", "sha256": "59ae12d892a86e92e22b7ccd2994f71552eeb8a9f546f8cf2710b25e524bf51c", "sha256_in_prefix": "59ae12d892a86e92e22b7ccd2994f71552eeb8a9f546f8cf2710b25e524bf51c", "size_in_bytes": 5334}, {"_path": "Library/include/generated_nvtx_meta.h", "path_type": "hardlink", "sha256": "5d657bc5eb8c75c36402ac0b0f93b5e26a54e42b440ed4a04531758d2b95285c", "sha256_in_prefix": "5d657bc5eb8c75c36402ac0b0f93b5e26a54e42b440ed4a04531758d2b95285c", "size_in_bytes": 7760}, {"_path": "Library/include/nvperf_common.h", "path_type": "hardlink", "sha256": "a55421954b6f7dc87c5299315f06801fecb565afe3ba141376d498e5c366dece", "sha256_in_prefix": "a55421954b6f7dc87c5299315f06801fecb565afe3ba141376d498e5c366dece", "size_in_bytes": 17648}, {"_path": "Library/include/nvperf_cuda_host.h", "path_type": "hardlink", "sha256": "cb6cb4c33dad31b30eaeef290d549c046db8d346e6833c5b1cd23c1a331f922c", "sha256_in_prefix": "cb6cb4c33dad31b30eaeef290d549c046db8d346e6833c5b1cd23c1a331f922c", "size_in_bytes": 7741}, {"_path": "Library/include/nvperf_host.h", "path_type": "hardlink", "sha256": "25d128f167845f91578f1d0f7b3344487d985da3903babef1083e5aa61e3dc4c", "sha256_in_prefix": "25d128f167845f91578f1d0f7b3344487d985da3903babef1083e5aa61e3dc4c", "size_in_bytes": 50375}, {"_path": "Library/include/nvperf_target.h", "path_type": "hardlink", "sha256": "c76320fffd08f69ecddd9666de70f1a8008d20345dc5b41895054d14614c36e5", "sha256_in_prefix": "c76320fffd08f69ecddd9666de70f1a8008d20345dc5b41895054d14614c36e5", "size_in_bytes": 24233}, {"_path": "Library/lib/checkpoint.lib", "path_type": "hardlink", "sha256": "30744b0c59115ef126c01f595e9ae3c09efab97009982c171712ea0648c405f8", "sha256_in_prefix": "30744b0c59115ef126c01f595e9ae3c09efab97009982c171712ea0648c405f8", "size_in_bytes": 2256}, {"_path": "Library/lib/cupti.lib", "path_type": "hardlink", "sha256": "ee46e53f0596f2750afbd1ffd59f69593ba1f5b265dba11b6547a02083ee090e", "sha256_in_prefix": "ee46e53f0596f2750afbd1ffd59f69593ba1f5b265dba11b6547a02083ee090e", "size_in_bytes": 52058}, {"_path": "Library/lib/nvperf_host.lib", "path_type": "hardlink", "sha256": "4861d54c6a4e42841248912f6296deeb371d7531d51b047978d95e3cf7b7821f", "sha256_in_prefix": "4861d54c6a4e42841248912f6296deeb371d7531d51b047978d95e3cf7b7821f", "size_in_bytes": 118754}, {"_path": "Library/lib/nvperf_target.lib", "path_type": "hardlink", "sha256": "3fb0d8e5f209a52b9b8ae04df3d284e2d7b9492ccb2b7299fca520065ce17d31", "sha256_in_prefix": "3fb0d8e5f209a52b9b8ae04df3d284e2d7b9492ccb2b7299fca520065ce17d31", "size_in_bytes": 96874}, {"_path": "Library/lib/pcsamplingutil.lib", "path_type": "hardlink", "sha256": "2e53a7cbd728171ce460df0f2f5a174a41b71b1d04aa813d0898b81f08f7d617", "sha256_in_prefix": "2e53a7cbd728171ce460df0f2f5a174a41b71b1d04aa813d0898b81f08f7d617", "size_in_bytes": 2914}], "paths_version": 1}, "requested_spec": "None", "sha256": "02cdf29fe8873850fcd822d983a373e2cb156863316458e2f867c488c9bf9f8b", "size": 155338, "subdir": "win-64", "timestamp": 1739445223000, "url": "https://conda.anaconda.org/nvidia/win-64/cuda-cupti-dev-12.8.90-0.conda", "version": "12.8.90"}