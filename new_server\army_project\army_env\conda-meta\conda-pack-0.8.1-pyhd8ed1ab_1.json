{"build": "pyhd8ed1ab_1", "build_number": 1, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["python >=3.9", "setuptools"], "extracted_package_dir": "", "files": ["Lib/site-packages/conda_pack-0.8.1.dist-info/INSTALLER", "Lib/site-packages/conda_pack-0.8.1.dist-info/LICENSE.txt", "Lib/site-packages/conda_pack-0.8.1.dist-info/METADATA", "Lib/site-packages/conda_pack-0.8.1.dist-info/RECORD", "Lib/site-packages/conda_pack-0.8.1.dist-info/REQUESTED", "Lib/site-packages/conda_pack-0.8.1.dist-info/WHEEL", "Lib/site-packages/conda_pack-0.8.1.dist-info/direct_url.json", "Lib/site-packages/conda_pack-0.8.1.dist-info/entry_points.txt", "Lib/site-packages/conda_pack-0.8.1.dist-info/top_level.txt", "Lib/site-packages/conda_pack/__init__.py", "Lib/site-packages/conda_pack/_progress.py", "Lib/site-packages/conda_pack/_version.py", "Lib/site-packages/conda_pack/cli.py", "Lib/site-packages/conda_pack/compat.py", "Lib/site-packages/conda_pack/core.py", "Lib/site-packages/conda_pack/formats.py", "Lib/site-packages/conda_pack/prefixes.py", "Lib/site-packages/conda_pack/scripts/posix/activate", "Lib/site-packages/conda_pack/scripts/posix/activate.fish", "Lib/site-packages/conda_pack/scripts/posix/deactivate", "Lib/site-packages/conda_pack/scripts/posix/parcel", "Lib/site-packages/conda_pack/scripts/windows/activate.bat", "Lib/site-packages/conda_pack/scripts/windows/deactivate.bat", "Lib/site-packages/conda_pack/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/conda_pack/__pycache__/_progress.cpython-39.pyc", "Lib/site-packages/conda_pack/__pycache__/_version.cpython-39.pyc", "Lib/site-packages/conda_pack/__pycache__/cli.cpython-39.pyc", "Lib/site-packages/conda_pack/__pycache__/compat.cpython-39.pyc", "Lib/site-packages/conda_pack/__pycache__/core.cpython-39.pyc", "Lib/site-packages/conda_pack/__pycache__/formats.cpython-39.pyc", "Lib/site-packages/conda_pack/__pycache__/prefixes.cpython-39.pyc", "Scripts/conda-pack-script.py", "Scripts/conda-pack.exe"], "fn": "conda-pack-0.8.1-pyhd8ed1ab_1.conda", "license": "BSD-3-<PERSON><PERSON>", "link": {"source": "", "type": 1}, "md5": "9b66289a559bc3503075f3ea012fd243", "name": "conda-pack", "noarch": "python", "package_tarball_full_path": "", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/conda_pack-0.8.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/conda_pack-0.8.1.dist-info/LICENSE.txt", "path_type": "hardlink", "sha256": "b0e95f386b21798f8a7a459336b8f1262a9723998d16956f582a0cff58df9270", "sha256_in_prefix": "b0e95f386b21798f8a7a459336b8f1262a9723998d16956f582a0cff58df9270", "size_in_bytes": 1509}, {"_path": "site-packages/conda_pack-0.8.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "21526c5310f09dc098fd0f763956a547826864805bbec957084f681468565d99", "sha256_in_prefix": "21526c5310f09dc098fd0f763956a547826864805bbec957084f681468565d99", "size_in_bytes": 2593}, {"_path": "site-packages/conda_pack-0.8.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "f7b888881d3d028518971fc0e4d026cfb5bf4ca04451e87ade1f05b9860e8d01", "sha256_in_prefix": "f7b888881d3d028518971fc0e4d026cfb5bf4ca04451e87ade1f05b9860e8d01", "size_in_bytes": 2452}, {"_path": "site-packages/conda_pack-0.8.1.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/conda_pack-0.8.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "a7178d5f925db427b9f0f51260ff6ea6673b8dd44f82f4f41a6f646f5487955c", "sha256_in_prefix": "a7178d5f925db427b9f0f51260ff6ea6673b8dd44f82f4f41a6f646f5487955c", "size_in_bytes": 109}, {"_path": "site-packages/conda_pack-0.8.1.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "1dcc54633f1270999691bf63a7e5bda3843917a1971791853f5d0785acb9cc3e", "sha256_in_prefix": "1dcc54633f1270999691bf63a7e5bda3843917a1971791853f5d0785acb9cc3e", "size_in_bytes": 106}, {"_path": "site-packages/conda_pack-0.8.1.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "71851810a3c3593e864028ef48f274be1c8d38cbaf2bcdc36ec451008ad1ec8e", "sha256_in_prefix": "71851810a3c3593e864028ef48f274be1c8d38cbaf2bcdc36ec451008ad1ec8e", "size_in_bytes": 51}, {"_path": "site-packages/conda_pack-0.8.1.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "cf2ce64365cce3c1c399f176f6ac2c80fce44fb8759faa8b64dcb11287dd1810", "sha256_in_prefix": "cf2ce64365cce3c1c399f176f6ac2c80fce44fb8759faa8b64dcb11287dd1810", "size_in_bytes": 11}, {"_path": "site-packages/conda_pack/__init__.py", "path_type": "hardlink", "sha256": "f8254a0c791aa3a49906372b49213cca551269a9ecf6ac2aa72475cbd75f086b", "sha256_in_prefix": "f8254a0c791aa3a49906372b49213cca551269a9ecf6ac2aa72475cbd75f086b", "size_in_bytes": 132}, {"_path": "site-packages/conda_pack/_progress.py", "path_type": "hardlink", "sha256": "f280fc40a231e8ebf0f47cb39c2c6b6d3594b118e1ddf8bc02922b06f02eb3b1", "sha256_in_prefix": "f280fc40a231e8ebf0f47cb39c2c6b6d3594b118e1ddf8bc02922b06f02eb3b1", "size_in_bytes": 2920}, {"_path": "site-packages/conda_pack/_version.py", "path_type": "hardlink", "sha256": "3bc3f7f3f5b67a6aef868cb796268d6218d7bb9331174abb5c497cbcaea9433c", "sha256_in_prefix": "3bc3f7f3f5b67a6aef868cb796268d6218d7bb9331174abb5c497cbcaea9433c", "size_in_bytes": 497}, {"_path": "site-packages/conda_pack/cli.py", "path_type": "hardlink", "sha256": "9f1448225a2f884fb3c0e39274e3f374c4fd28fadcfa689eb5129ccfbb5d6f3d", "sha256_in_prefix": "9f1448225a2f884fb3c0e39274e3f374c4fd28fadcfa689eb5129ccfbb5d6f3d", "size_in_bytes": 8966}, {"_path": "site-packages/conda_pack/compat.py", "path_type": "hardlink", "sha256": "714c7b490d32ba769a8914417bb010b68126ccdae7ff3339a369645be5b8179a", "sha256_in_prefix": "714c7b490d32ba769a8914417bb010b68126ccdae7ff3339a369645be5b8179a", "size_in_bytes": 1302}, {"_path": "site-packages/conda_pack/core.py", "path_type": "hardlink", "sha256": "0a31aade05b98fd4d072c2707a56dd84aa0e2baf37cf8f5b31f5eb51ff64162a", "sha256_in_prefix": "0a31aade05b98fd4d072c2707a56dd84aa0e2baf37cf8f5b31f5eb51ff64162a", "size_in_bytes": 47588}, {"_path": "site-packages/conda_pack/formats.py", "path_type": "hardlink", "sha256": "3746f229204d8c29ce010a0c5b8bc7d3fcd0d0148df182c049cc3b4b1dd02f91", "sha256_in_prefix": "3746f229204d8c29ce010a0c5b8bc7d3fcd0d0148df182c049cc3b4b1dd02f91", "size_in_bytes": 19634}, {"_path": "site-packages/conda_pack/prefixes.py", "path_type": "hardlink", "sha256": "88b58df57aa4f6772f8d6aeeb85fe1f91bfd31646ced75560fbe91267a60dc8f", "sha256_in_prefix": "88b58df57aa4f6772f8d6aeeb85fe1f91bfd31646ced75560fbe91267a60dc8f", "size_in_bytes": 7766}, {"_path": "site-packages/conda_pack/scripts/posix/activate", "path_type": "hardlink", "sha256": "d25c0bee6db4d0891f4cdbcdec5d62fe507ac0fe6bb7bd52030dec2279a9e1b9", "sha256_in_prefix": "d25c0bee6db4d0891f4cdbcdec5d62fe507ac0fe6bb7bd52030dec2279a9e1b9", "size_in_bytes": 2364}, {"_path": "site-packages/conda_pack/scripts/posix/activate.fish", "path_type": "hardlink", "sha256": "d30eecb5a5a9f2afebb7e00fb413f5ed5244fbf7423d1658469c943aac473a6b", "sha256_in_prefix": "d30eecb5a5a9f2afebb7e00fb413f5ed5244fbf7423d1658469c943aac473a6b", "size_in_bytes": 1830}, {"_path": "site-packages/conda_pack/scripts/posix/deactivate", "path_type": "hardlink", "sha256": "4cbd0bc760f5d4e41ddab41d8d0c3ba8fb99e4f8623e16044a05103eb85ca47c", "sha256_in_prefix": "4cbd0bc760f5d4e41ddab41d8d0c3ba8fb99e4f8623e16044a05103eb85ca47c", "size_in_bytes": 878}, {"_path": "site-packages/conda_pack/scripts/posix/parcel", "path_type": "hardlink", "sha256": "bf93a4a64e97df4fffe615fa8df9ea8df43d6633718eedee175c24fa568c24ba", "sha256_in_prefix": "bf93a4a64e97df4fffe615fa8df9ea8df43d6633718eedee175c24fa568c24ba", "size_in_bytes": 387}, {"_path": "site-packages/conda_pack/scripts/windows/activate.bat", "path_type": "hardlink", "sha256": "f030eeb933f882ae44a69cbe7ccd0746124c5fde4ec42f9f8521a853a1675f89", "sha256_in_prefix": "f030eeb933f882ae44a69cbe7ccd0746124c5fde4ec42f9f8521a853a1675f89", "size_in_bytes": 1906}, {"_path": "site-packages/conda_pack/scripts/windows/deactivate.bat", "path_type": "hardlink", "sha256": "a931e1e1e0d5ac5f340e1587b197549064b24ea243071c519641306ea2eb9d28", "sha256_in_prefix": "a931e1e1e0d5ac5f340e1587b197549064b24ea243071c519641306ea2eb9d28", "size_in_bytes": 1562}, {"_path": "Lib/site-packages/conda_pack/__pycache__/__init__.cpython-39.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/conda_pack/__pycache__/_progress.cpython-39.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/conda_pack/__pycache__/_version.cpython-39.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/conda_pack/__pycache__/cli.cpython-39.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/conda_pack/__pycache__/compat.cpython-39.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/conda_pack/__pycache__/core.cpython-39.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/conda_pack/__pycache__/formats.cpython-39.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/conda_pack/__pycache__/prefixes.cpython-39.pyc", "path_type": "pyc_file"}, {"_path": "Scripts/conda-pack-script.py", "path_type": "windows_python_entry_point_script"}, {"_path": "Scripts/conda-pack.exe", "path_type": "windows_python_entry_point_exe"}], "paths_version": 1}, "requested_spec": "conda-pack", "sha256": "b53014e3f85bf52c1c36f6a54ec48238630bec80bd55880f77a58a2d37280aa3", "size": 34637, "subdir": "noarch", "timestamp": 1734292917000, "url": "https://conda.anaconda.org/conda-forge/noarch/conda-pack-0.8.1-pyhd8ed1ab_1.conda", "version": "0.8.1"}