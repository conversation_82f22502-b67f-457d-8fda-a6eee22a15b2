{"build": "py39haa95532_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["python >=3.9,<3.10.0a0"], "extracted_package_dir": "", "files": ["Lib/site-packages/wheel-0.45.1.dist-info/LICENSE.txt", "Lib/site-packages/wheel-0.45.1.dist-info/METADATA", "Lib/site-packages/wheel-0.45.1.dist-info/RECORD", "Lib/site-packages/wheel-0.45.1.dist-info/WHEEL", "Lib/site-packages/wheel-0.45.1.dist-info/entry_points.txt", "Lib/site-packages/wheel/__init__.py", "Lib/site-packages/wheel/__main__.py", "Lib/site-packages/wheel/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/wheel/__pycache__/__main__.cpython-39.pyc", "Lib/site-packages/wheel/__pycache__/_bdist_wheel.cpython-39.pyc", "Lib/site-packages/wheel/__pycache__/_setuptools_logging.cpython-39.pyc", "Lib/site-packages/wheel/__pycache__/bdist_wheel.cpython-39.pyc", "Lib/site-packages/wheel/__pycache__/macosx_libfile.cpython-39.pyc", "Lib/site-packages/wheel/__pycache__/metadata.cpython-39.pyc", "Lib/site-packages/wheel/__pycache__/util.cpython-39.pyc", "Lib/site-packages/wheel/__pycache__/wheelfile.cpython-39.pyc", "Lib/site-packages/wheel/_bdist_wheel.py", "Lib/site-packages/wheel/_setuptools_logging.py", "Lib/site-packages/wheel/bdist_wheel.py", "Lib/site-packages/wheel/cli/__init__.py", "Lib/site-packages/wheel/cli/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/wheel/cli/__pycache__/convert.cpython-39.pyc", "Lib/site-packages/wheel/cli/__pycache__/pack.cpython-39.pyc", "Lib/site-packages/wheel/cli/__pycache__/tags.cpython-39.pyc", "Lib/site-packages/wheel/cli/__pycache__/unpack.cpython-39.pyc", "Lib/site-packages/wheel/cli/convert.py", "Lib/site-packages/wheel/cli/pack.py", "Lib/site-packages/wheel/cli/tags.py", "Lib/site-packages/wheel/cli/unpack.py", "Lib/site-packages/wheel/macosx_libfile.py", "Lib/site-packages/wheel/metadata.py", "Lib/site-packages/wheel/util.py", "Lib/site-packages/wheel/vendored/__init__.py", "Lib/site-packages/wheel/vendored/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/wheel/vendored/packaging/LICENSE", "Lib/site-packages/wheel/vendored/packaging/LICENSE.APACHE", "Lib/site-packages/wheel/vendored/packaging/LICENSE.BSD", "Lib/site-packages/wheel/vendored/packaging/__init__.py", "Lib/site-packages/wheel/vendored/packaging/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/_elffile.cpython-39.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/_manylinux.cpython-39.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/_musllinux.cpython-39.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/_parser.cpython-39.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/_structures.cpython-39.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/_tokenizer.cpython-39.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/markers.cpython-39.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/requirements.cpython-39.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/specifiers.cpython-39.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/tags.cpython-39.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/utils.cpython-39.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/version.cpython-39.pyc", "Lib/site-packages/wheel/vendored/packaging/_elffile.py", "Lib/site-packages/wheel/vendored/packaging/_manylinux.py", "Lib/site-packages/wheel/vendored/packaging/_musllinux.py", "Lib/site-packages/wheel/vendored/packaging/_parser.py", "Lib/site-packages/wheel/vendored/packaging/_structures.py", "Lib/site-packages/wheel/vendored/packaging/_tokenizer.py", "Lib/site-packages/wheel/vendored/packaging/markers.py", "Lib/site-packages/wheel/vendored/packaging/requirements.py", "Lib/site-packages/wheel/vendored/packaging/specifiers.py", "Lib/site-packages/wheel/vendored/packaging/tags.py", "Lib/site-packages/wheel/vendored/packaging/utils.py", "Lib/site-packages/wheel/vendored/packaging/version.py", "Lib/site-packages/wheel/vendored/vendor.txt", "Lib/site-packages/wheel/wheelfile.py", "Scripts/wheel-script.py", "Scripts/wheel.exe"], "fn": "wheel-0.45.1-py39haa95532_0.conda", "license": "MIT", "link": {"source": "", "type": 1}, "md5": "77878e5a74e2372b525e29dc623b55eb", "name": "wheel", "package_tarball_full_path": "", "paths_data": {"paths": [{"_path": "Lib/site-packages/wheel-0.45.1.dist-info/LICENSE.txt", "path_type": "hardlink", "sha256": "30c23618679108f3e8ea1d2a658c7ca417bdfc891c98ef1a89fa4ff0c9828654", "sha256_in_prefix": "30c23618679108f3e8ea1d2a658c7ca417bdfc891c98ef1a89fa4ff0c9828654", "size_in_bytes": 1107}, {"_path": "Lib/site-packages/wheel-0.45.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "b04e96136b82c11487b79640c093a7344af76607ec497a24f4b87b2518590a60", "sha256_in_prefix": "b04e96136b82c11487b79640c093a7344af76607ec497a24f4b87b2518590a60", "size_in_bytes": 2313}, {"_path": "Lib/site-packages/wheel-0.45.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "9a506837367672d2a8417de463a858dd839fe7bc711125d0270ce5fcae3f3222", "sha256_in_prefix": "9a506837367672d2a8417de463a858dd839fe7bc711125d0270ce5fcae3f3222", "size_in_bytes": 3188}, {"_path": "Lib/site-packages/wheel-0.45.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "sha256_in_prefix": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "size_in_bytes": 81}, {"_path": "Lib/site-packages/wheel-0.45.1.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "ad363505b90f1e1906326e10dc5d29233241cd6da4331a06d68ae27dfbc6740d", "sha256_in_prefix": "ad363505b90f1e1906326e10dc5d29233241cd6da4331a06d68ae27dfbc6740d", "size_in_bytes": 104}, {"_path": "Lib/site-packages/wheel/__init__.py", "path_type": "hardlink", "sha256": "9abc4c9ef757002babfcb59e81b51f879839cac599addeb75099fcf74c2f18d9", "sha256_in_prefix": "9abc4c9ef757002babfcb59e81b51f879839cac599addeb75099fcf74c2f18d9", "size_in_bytes": 59}, {"_path": "Lib/site-packages/wheel/__main__.py", "path_type": "hardlink", "sha256": "3643149ee4c219c3a4818d0804b8010950bf04619c58e471d8af236064b5d941", "sha256_in_prefix": "3643149ee4c219c3a4818d0804b8010950bf04619c58e471d8af236064b5d941", "size_in_bytes": 455}, {"_path": "Lib/site-packages/wheel/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "be4368823be13403507fb36d839859d3ff13cacb1f5e7237ea3753e8460fc292", "sha256_in_prefix": "be4368823be13403507fb36d839859d3ff13cacb1f5e7237ea3753e8460fc292", "size_in_bytes": 199}, {"_path": "Lib/site-packages/wheel/__pycache__/__main__.cpython-39.pyc", "path_type": "hardlink", "sha256": "d1033b10d70974b743b9ab71676881b259e17eb625a8046270c3c5dd22ca0e73", "sha256_in_prefix": "d1033b10d70974b743b9ab71676881b259e17eb625a8046270c3c5dd22ca0e73", "size_in_bytes": 591}, {"_path": "Lib/site-packages/wheel/__pycache__/_bdist_wheel.cpython-39.pyc", "path_type": "hardlink", "sha256": "ec81dc9b57d17ec15a8b7c8150c069eb7494f2aa1f1c3d07a3d1cffe5013a053", "sha256_in_prefix": "ec81dc9b57d17ec15a8b7c8150c069eb7494f2aa1f1c3d07a3d1cffe5013a053", "size_in_bytes": 14983}, {"_path": "Lib/site-packages/wheel/__pycache__/_setuptools_logging.cpython-39.pyc", "path_type": "hardlink", "sha256": "fbc05480523fead841b6e052c8260d51ba40ca61792e263746f64ceaf45104df", "sha256_in_prefix": "fbc05480523fead841b6e052c8260d51ba40ca61792e263746f64ceaf45104df", "size_in_bytes": 970}, {"_path": "Lib/site-packages/wheel/__pycache__/bdist_wheel.cpython-39.pyc", "path_type": "hardlink", "sha256": "4ecee2c08fa038487c24e86b8d41c41501469d639f59480cb07e631242e32582", "sha256_in_prefix": "4ecee2c08fa038487c24e86b8d41c41501469d639f59480cb07e631242e32582", "size_in_bytes": 655}, {"_path": "Lib/site-packages/wheel/__pycache__/macosx_libfile.cpython-39.pyc", "path_type": "hardlink", "sha256": "4c18f1c09eb240c82d991d63431834f7761c52346f1ea9f1810f33ce0e6333dc", "sha256_in_prefix": "4c18f1c09eb240c82d991d63431834f7761c52346f1ea9f1810f33ce0e6333dc", "size_in_bytes": 10450}, {"_path": "Lib/site-packages/wheel/__pycache__/metadata.cpython-39.pyc", "path_type": "hardlink", "sha256": "4aea426c2d3249a53a3a84a6de9327b339ae48aeea8bb44d433411a1d4b2f845", "sha256_in_prefix": "4aea426c2d3249a53a3a84a6de9327b339ae48aeea8bb44d433411a1d4b2f845", "size_in_bytes": 6033}, {"_path": "Lib/site-packages/wheel/__pycache__/util.cpython-39.pyc", "path_type": "hardlink", "sha256": "4774e24d075559aa2b79317d9f184ad4bed63af46a139cb4ca453463cd338178", "sha256_in_prefix": "4774e24d075559aa2b79317d9f184ad4bed63af46a139cb4ca453463cd338178", "size_in_bytes": 672}, {"_path": "Lib/site-packages/wheel/__pycache__/wheelfile.cpython-39.pyc", "path_type": "hardlink", "sha256": "9392f92aca95ed4872e55864f765a0594dc705ab63b04490da687c1135008393", "sha256_in_prefix": "9392f92aca95ed4872e55864f765a0594dc705ab63b04490da687c1135008393", "size_in_bytes": 6319}, {"_path": "Lib/site-packages/wheel/_bdist_wheel.py", "path_type": "hardlink", "sha256": "520842423487fe955f71987aa118f34b0fd342171fdda9d2c753a488b48bf363", "sha256_in_prefix": "520842423487fe955f71987aa118f34b0fd342171fdda9d2c753a488b48bf363", "size_in_bytes": 21694}, {"_path": "Lib/site-packages/wheel/_setuptools_logging.py", "path_type": "hardlink", "sha256": "fb9282fa59ded2294e5162037ce92a6a951618c15986e2980c86af219881e643", "sha256_in_prefix": "fb9282fa59ded2294e5162037ce92a6a951618c15986e2980c86af219881e643", "size_in_bytes": 781}, {"_path": "Lib/site-packages/wheel/bdist_wheel.py", "path_type": "hardlink", "sha256": "b697fd5ae7e248ed51b84320e683e121f486f0333388267fe26b82285ebd0aaa", "sha256_in_prefix": "b697fd5ae7e248ed51b84320e683e121f486f0333388267fe26b82285ebd0aaa", "size_in_bytes": 1107}, {"_path": "Lib/site-packages/wheel/cli/__init__.py", "path_type": "hardlink", "sha256": "369abafe32a2d3776121c46799bb85870be2549c703b4b5812712158cbfd709a", "sha256_in_prefix": "369abafe32a2d3776121c46799bb85870be2549c703b4b5812712158cbfd709a", "size_in_bytes": 4402}, {"_path": "Lib/site-packages/wheel/cli/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "367c6fe210a2a212cbe8cb5653055b1347f79474d6d3d8ec108e959425514f60", "sha256_in_prefix": "367c6fe210a2a212cbe8cb5653055b1347f79474d6d3d8ec108e959425514f60", "size_in_bytes": 4572}, {"_path": "Lib/site-packages/wheel/cli/__pycache__/convert.cpython-39.pyc", "path_type": "hardlink", "sha256": "b3cc415ed400f14b3676aa06df95266e228aacb0600119ab3af863a3f746c5b8", "sha256_in_prefix": "b3cc415ed400f14b3676aa06df95266e228aacb0600119ab3af863a3f746c5b8", "size_in_bytes": 9446}, {"_path": "Lib/site-packages/wheel/cli/__pycache__/pack.cpython-39.pyc", "path_type": "hardlink", "sha256": "14567021e4156c592761e3b8782f35c6b68533a37965d99f229e50868de3b2a7", "sha256_in_prefix": "14567021e4156c592761e3b8782f35c6b68533a37965d99f229e50868de3b2a7", "size_in_bytes": 3072}, {"_path": "Lib/site-packages/wheel/cli/__pycache__/tags.cpython-39.pyc", "path_type": "hardlink", "sha256": "e4d9f6908d893ae524fc553900b65aba8ba707b4226a319d5b5b7629d1d75a99", "sha256_in_prefix": "e4d9f6908d893ae524fc553900b65aba8ba707b4226a319d5b5b7629d1d75a99", "size_in_bytes": 3785}, {"_path": "Lib/site-packages/wheel/cli/__pycache__/unpack.cpython-39.pyc", "path_type": "hardlink", "sha256": "02137fd2f9fe46bf5c68a4c3771fc15f668c294278ff8fe2163236d778ef5d3e", "sha256_in_prefix": "02137fd2f9fe46bf5c68a4c3771fc15f668c294278ff8fe2163236d778ef5d3e", "size_in_bytes": 1043}, {"_path": "Lib/site-packages/wheel/cli/convert.py", "path_type": "hardlink", "sha256": "062d27b445dbf674e5942c56793450e23fa73ecdeccd64842a2a46fc68273244", "sha256_in_prefix": "062d27b445dbf674e5942c56793450e23fa73ecdeccd64842a2a46fc68273244", "size_in_bytes": 12634}, {"_path": "Lib/site-packages/wheel/cli/pack.py", "path_type": "hardlink", "sha256": "08015c1dd055ba5bec1d82659dd2953bb28c23d26a053673e628b43cac7108eb", "sha256_in_prefix": "08015c1dd055ba5bec1d82659dd2953bb28c23d26a053673e628b43cac7108eb", "size_in_bytes": 3103}, {"_path": "Lib/site-packages/wheel/cli/tags.py", "path_type": "hardlink", "sha256": "947c3e2da5ab912e49cbfa96730fbaa528de34ceb20230e7a8a2371392534c25", "sha256_in_prefix": "947c3e2da5ab912e49cbfa96730fbaa528de34ceb20230e7a8a2371392534c25", "size_in_bytes": 4760}, {"_path": "Lib/site-packages/wheel/cli/unpack.py", "path_type": "hardlink", "sha256": "63f27bca7c4f4a81454d3ec7d1f3206c195512bc320c670e6e099ee4c06ecf9f", "sha256_in_prefix": "63f27bca7c4f4a81454d3ec7d1f3206c195512bc320c670e6e099ee4c06ecf9f", "size_in_bytes": 1021}, {"_path": "Lib/site-packages/wheel/macosx_libfile.py", "path_type": "hardlink", "sha256": "935c7b084dcb3ed3951aa8fa3574359d319854f69e46b855cd41bf28fab7cc3b", "sha256_in_prefix": "935c7b084dcb3ed3951aa8fa3574359d319854f69e46b855cd41bf28fab7cc3b", "size_in_bytes": 16572}, {"_path": "Lib/site-packages/wheel/metadata.py", "path_type": "hardlink", "sha256": "242e29ee395066ed9b513010d9f7af92a2e383f5fa8273724612e7e8e50ed6d7", "sha256_in_prefix": "242e29ee395066ed9b513010d9f7af92a2e383f5fa8273724612e7e8e50ed6d7", "size_in_bytes": 6171}, {"_path": "Lib/site-packages/wheel/util.py", "path_type": "hardlink", "sha256": "68beda89b1f061481f73c5a5a252f9b577645780dab5b2716476f59301c52405", "sha256_in_prefix": "68beda89b1f061481f73c5a5a252f9b577645780dab5b2716476f59301c52405", "size_in_bytes": 423}, {"_path": "Lib/site-packages/wheel/vendored/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/wheel/vendored/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "08856a169b47b06f436a9229bedb2450ffd96d3653d477a3db3e0d67787677f6", "sha256_in_prefix": "08856a169b47b06f436a9229bedb2450ffd96d3653d477a3db3e0d67787677f6", "size_in_bytes": 135}, {"_path": "Lib/site-packages/wheel/vendored/packaging/LICENSE", "path_type": "hardlink", "sha256": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "sha256_in_prefix": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "size_in_bytes": 197}, {"_path": "Lib/site-packages/wheel/vendored/packaging/LICENSE.APACHE", "path_type": "hardlink", "sha256": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "sha256_in_prefix": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "size_in_bytes": 10174}, {"_path": "Lib/site-packages/wheel/vendored/packaging/LICENSE.BSD", "path_type": "hardlink", "sha256": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "sha256_in_prefix": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "size_in_bytes": 1344}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "2358c8a324ee232955cd93a9f3fa88ad182dc4f044804dd8294dab71a3adec48", "sha256_in_prefix": "2358c8a324ee232955cd93a9f3fa88ad182dc4f044804dd8294dab71a3adec48", "size_in_bytes": 145}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/_elffile.cpython-39.pyc", "path_type": "hardlink", "sha256": "ccfa1515d784ce3b0452bd33672c613debdf4a9bc965fc56711935d54ade9f81", "sha256_in_prefix": "ccfa1515d784ce3b0452bd33672c613debdf4a9bc965fc56711935d54ade9f81", "size_in_bytes": 3315}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/_manylinux.cpython-39.pyc", "path_type": "hardlink", "sha256": "a240661a3920b72f0221516f3c3bb6176ae240317b7781fe3c0cde2af1d63b53", "sha256_in_prefix": "a240661a3920b72f0221516f3c3bb6176ae240317b7781fe3c0cde2af1d63b53", "size_in_bytes": 6362}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/_musllinux.cpython-39.pyc", "path_type": "hardlink", "sha256": "aa8caed93004d4170d587f3790fce7b0f283febe8f2a506da9801fb85f7b86bd", "sha256_in_prefix": "aa8caed93004d4170d587f3790fce7b0f283febe8f2a506da9801fb85f7b86bd", "size_in_bytes": 3277}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/_parser.cpython-39.pyc", "path_type": "hardlink", "sha256": "0ce08d996b05b2c3d11ac1f2f5ff6b77d66291635fdc604e51178dada2a124fe", "sha256_in_prefix": "0ce08d996b05b2c3d11ac1f2f5ff6b77d66291635fdc604e51178dada2a124fe", "size_in_bytes": 8946}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/_structures.cpython-39.pyc", "path_type": "hardlink", "sha256": "802c7e3fab8db13e592a1052f9ae28fad62df8caad45583d297a7dcd92d9cd10", "sha256_in_prefix": "802c7e3fab8db13e592a1052f9ae28fad62df8caad45583d297a7dcd92d9cd10", "size_in_bytes": 2755}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/_tokenizer.cpython-39.pyc", "path_type": "hardlink", "sha256": "ea3be25391f3c91a0dab3ef4b3da4978a2c7a9e7490b34885b2b513a59258446", "sha256_in_prefix": "ea3be25391f3c91a0dab3ef4b3da4978a2c7a9e7490b34885b2b513a59258446", "size_in_bytes": 5652}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/markers.cpython-39.pyc", "path_type": "hardlink", "sha256": "6d5a437c38b797bacef49e95cd17d054bd95972d3c9faee4ed37df239f85d935", "sha256_in_prefix": "6d5a437c38b797bacef49e95cd17d054bd95972d3c9faee4ed37df239f85d935", "size_in_bytes": 6942}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/requirements.cpython-39.pyc", "path_type": "hardlink", "sha256": "e5e3c5e2af7cf5845e762b3ec6c153dff055fc00b4c7764d6432d585292afc4f", "sha256_in_prefix": "e5e3c5e2af7cf5845e762b3ec6c153dff055fc00b4c7764d6432d585292afc4f", "size_in_bytes": 2795}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/specifiers.cpython-39.pyc", "path_type": "hardlink", "sha256": "dd18c38c354d4a1817d57ac89d1c69a36088b64c7a8c60ef26dae490d23dfe3b", "sha256_in_prefix": "dd18c38c354d4a1817d57ac89d1c69a36088b64c7a8c60ef26dae490d23dfe3b", "size_in_bytes": 30996}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/tags.cpython-39.pyc", "path_type": "hardlink", "sha256": "67acc4e751dc1a9665c8f2678370bb8fed0f31a7be78a930c621b7bf2bc9b03a", "sha256_in_prefix": "67acc4e751dc1a9665c8f2678370bb8fed0f31a7be78a930c621b7bf2bc9b03a", "size_in_bytes": 13837}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/utils.cpython-39.pyc", "path_type": "hardlink", "sha256": "2d0464b93e0d7b3729fddfb89a1691413d200e3052f19a6dd692fe6332c7fc18", "sha256_in_prefix": "2d0464b93e0d7b3729fddfb89a1691413d200e3052f19a6dd692fe6332c7fc18", "size_in_bytes": 4553}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/version.cpython-39.pyc", "path_type": "hardlink", "sha256": "74c1d60e4d39fe93c9f0fd62b252e35ae7f6fe4c53e5d81189f555e36e6e1450", "sha256_in_prefix": "74c1d60e4d39fe93c9f0fd62b252e35ae7f6fe4c53e5d81189f555e36e6e1450", "size_in_bytes": 14273}, {"_path": "Lib/site-packages/wheel/vendored/packaging/_elffile.py", "path_type": "hardlink", "sha256": "85b98af0e0fa67b7d8ea1c229c7114703d5bcbb73390688d62eed28671449369", "sha256_in_prefix": "85b98af0e0fa67b7d8ea1c229c7114703d5bcbb73390688d62eed28671449369", "size_in_bytes": 3266}, {"_path": "Lib/site-packages/wheel/vendored/packaging/_manylinux.py", "path_type": "hardlink", "sha256": "3fbb1d479ffb5c1634f4b55860f8479b274c2482303d75ac878a2593be14ba3e", "sha256_in_prefix": "3fbb1d479ffb5c1634f4b55860f8479b274c2482303d75ac878a2593be14ba3e", "size_in_bytes": 9588}, {"_path": "Lib/site-packages/wheel/vendored/packaging/_musllinux.py", "path_type": "hardlink", "sha256": "cf5b3c4e8da1434be99ff77e3b68b9ab11b010af1698694bb7777fdba57b35e6", "sha256_in_prefix": "cf5b3c4e8da1434be99ff77e3b68b9ab11b010af1698694bb7777fdba57b35e6", "size_in_bytes": 2674}, {"_path": "Lib/site-packages/wheel/vendored/packaging/_parser.py", "path_type": "hardlink", "sha256": "e2d4f87a64a5daa4da53b553404d576bda358cc3c2b017b3b18071c8d31437eb", "sha256_in_prefix": "e2d4f87a64a5daa4da53b553404d576bda358cc3c2b017b3b18071c8d31437eb", "size_in_bytes": 10347}, {"_path": "Lib/site-packages/wheel/vendored/packaging/_structures.py", "path_type": "hardlink", "sha256": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "sha256_in_prefix": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "size_in_bytes": 1431}, {"_path": "Lib/site-packages/wheel/vendored/packaging/_tokenizer.py", "path_type": "hardlink", "sha256": "6a50ad6f05e138502614667a050fb0093485a11009db3fb2b087fbfff31327f9", "sha256_in_prefix": "6a50ad6f05e138502614667a050fb0093485a11009db3fb2b087fbfff31327f9", "size_in_bytes": 5292}, {"_path": "Lib/site-packages/wheel/vendored/packaging/markers.py", "path_type": "hardlink", "sha256": "fd348f2350612583bb069f40cd398743122a1c45576938e60e1f46fb0f2accf0", "sha256_in_prefix": "fd348f2350612583bb069f40cd398743122a1c45576938e60e1f46fb0f2accf0", "size_in_bytes": 8232}, {"_path": "Lib/site-packages/wheel/vendored/packaging/requirements.py", "path_type": "hardlink", "sha256": "760a01795a6b3eed9813a43c9c67f038f4e30131db45afd918bc978451259fa4", "sha256_in_prefix": "760a01795a6b3eed9813a43c9c67f038f4e30131db45afd918bc978451259fa4", "size_in_bytes": 2933}, {"_path": "Lib/site-packages/wheel/vendored/packaging/specifiers.py", "path_type": "hardlink", "sha256": "2164add12acb48fef685e5a1002f142f4786bdab3b5c84078ea8958957e63ca1", "sha256_in_prefix": "2164add12acb48fef685e5a1002f142f4786bdab3b5c84078ea8958957e63ca1", "size_in_bytes": 39778}, {"_path": "Lib/site-packages/wheel/vendored/packaging/tags.py", "path_type": "hardlink", "sha256": "7de7475e2387901c4d6535e8b57bfcb973e630553d69ef93281ba38181e281c0", "sha256_in_prefix": "7de7475e2387901c4d6535e8b57bfcb973e630553d69ef93281ba38181e281c0", "size_in_bytes": 18950}, {"_path": "Lib/site-packages/wheel/vendored/packaging/utils.py", "path_type": "hardlink", "sha256": "5e07663f7cb1f7ec101058ceecebcc8fd46311fe49951e4714547af6fed243d1", "sha256_in_prefix": "5e07663f7cb1f7ec101058ceecebcc8fd46311fe49951e4714547af6fed243d1", "size_in_bytes": 5268}, {"_path": "Lib/site-packages/wheel/vendored/packaging/version.py", "path_type": "hardlink", "sha256": "3c525a6190f1060cb191f6211f7490c38a9f13d202096ad39a2b6fab5e32ddbb", "sha256_in_prefix": "3c525a6190f1060cb191f6211f7490c38a9f13d202096ad39a2b6fab5e32ddbb", "size_in_bytes": 16234}, {"_path": "Lib/site-packages/wheel/vendored/vendor.txt", "path_type": "hardlink", "sha256": "67610d8c1d62e69adf7b3f0274cd5276bddce99c6fdab451a253292e60677001", "sha256_in_prefix": "67610d8c1d62e69adf7b3f0274cd5276bddce99c6fdab451a253292e60677001", "size_in_bytes": 16}, {"_path": "Lib/site-packages/wheel/wheelfile.py", "path_type": "hardlink", "sha256": "5120adb4d949c1a7f1b79d5860514a1bb8e7c73f1d7e16f2a8064bea331303db", "sha256_in_prefix": "5120adb4d949c1a7f1b79d5860514a1bb8e7c73f1d7e16f2a8064bea331303db", "size_in_bytes": 8411}, {"_path": "Scripts/wheel-script.py", "path_type": "hardlink", "sha256": "f81e86d226fd97f3a9fb708b803859f8922740e5b62a4577b0b6b8b1d6b8e333", "sha256_in_prefix": "f81e86d226fd97f3a9fb708b803859f8922740e5b62a4577b0b6b8b1d6b8e333", "size_in_bytes": 203}, {"_path": "Scripts/wheel.exe", "path_type": "hardlink", "sha256": "f24d102084620e54fe68d07f6e9169118b283f8d33d0c8f4b974c2e05a306059", "sha256_in_prefix": "f24d102084620e54fe68d07f6e9169118b283f8d33d0c8f4b974c2e05a306059", "size_in_bytes": 54032}], "paths_version": 1}, "requested_spec": "None", "sha256": "9f942101cc076c259ea20fb6f13a58823218e0389b060b766dc4cae37469ae92", "size": 148042, "subdir": "win-64", "timestamp": 1737990404000, "url": "https://repo.anaconda.com/pkgs/main/win-64/wheel-0.45.1-py39haa95532_0.conda", "version": "0.45.1"}