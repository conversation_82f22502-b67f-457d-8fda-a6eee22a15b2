{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.8,<12.9.0a0"], "extracted_package_dir": "", "files": ["Library/bin/curand64_10.dll"], "fn": "libcurand-10.3.9.90-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "", "type": 1}, "md5": "31e6a879736efcdaef70d7a628789922", "name": "lib<PERSON><PERSON>", "package_tarball_full_path": "", "paths_data": {"paths": [{"_path": "Library/bin/curand64_10.dll", "path_type": "hardlink", "sha256": "3465fd1b46e551339b8f44c455756a0f2cba8bd846562eb659040d48edb7aaac", "sha256_in_prefix": "3465fd1b46e551339b8f44c455756a0f2cba8bd846562eb659040d48edb7aaac", "size_in_bytes": 71955968}], "paths_version": 1}, "requested_spec": "None", "sha256": "a6267fbe0f19cc32c725e2d12889346ccfeac9f90ea64c70ca4abe6699ffc47f", "size": 46730048, "subdir": "win-64", "timestamp": 1739449586000, "url": "https://conda.anaconda.org/nvidia/win-64/libcurand-10.3.9.90-0.conda", "version": "10.3.9.90"}