{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, vModelSelect as _vModelSelect, withDirectives as _withDirectives, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, Fragment as _Fragment, createStaticVNode as _createStaticVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"detected\"\n};\nconst _hoisted_2 = {\n  class: \"content\"\n};\nconst _hoisted_3 = {\n  class: \"left\"\n};\nconst _hoisted_4 = {\n  class: \"pane top-left\"\n};\nconst _hoisted_5 = {\n  class: \"pane-title\"\n};\nconst _hoisted_6 = {\n  class: \"pane-body\"\n};\nconst _hoisted_7 = {\n  key: 0,\n  class: \"preview-container\"\n};\nconst _hoisted_8 = [\"src\"];\nconst _hoisted_9 = {\n  key: 1,\n  class: \"upload-placeholder\"\n};\nconst _hoisted_10 = {\n  key: 1,\n  class: \"preview-container\"\n};\nconst _hoisted_11 = {\n  class: \"preview-wrapper\"\n};\nconst _hoisted_12 = [\"src\"];\nconst _hoisted_13 = [\"src\"];\nconst _hoisted_14 = {\n  key: 2,\n  class: \"upload-placeholder\"\n};\nconst _hoisted_15 = {\n  key: 0\n};\nconst _hoisted_16 = {\n  key: 1\n};\nconst _hoisted_17 = {\n  key: 2,\n  class: \"preview-container\"\n};\nconst _hoisted_18 = {\n  class: \"preview-wrapper\"\n};\nconst _hoisted_19 = {\n  key: 0,\n  ref: \"rtspPlayer\",\n  autoplay: \"\",\n  class: \"preview-content\"\n};\nconst _hoisted_20 = {\n  key: 1,\n  class: \"upload-placeholder\"\n};\nconst _hoisted_21 = {\n  class: \"pane-footer\"\n};\nconst _hoisted_22 = {\n  key: 0,\n  class: \"file-info\"\n};\nconst _hoisted_23 = {\n  key: 1,\n  class: \"file-info\"\n};\nconst _hoisted_24 = {\n  class: \"action-buttons\"\n};\nconst _hoisted_25 = {\n  class: \"pane bottom-left\"\n};\nconst _hoisted_26 = {\n  class: \"pane-title\"\n};\nconst _hoisted_27 = {\n  class: \"pane-body\"\n};\nconst _hoisted_28 = {\n  class: \"result-container\"\n};\nconst _hoisted_29 = [\"src\"];\nconst _hoisted_30 = [\"src\"];\nconst _hoisted_31 = {\n  key: 2,\n  class: \"result-placeholder\"\n};\nconst _hoisted_32 = {\n  ref: \"resultCanvas\"\n};\nconst _hoisted_33 = {\n  class: \"pane-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_HeaderComponent = _resolveComponent(\"HeaderComponent\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_HeaderComponent, {\n    title: \"智能判读\",\n    showBackButton: true,\n    onBack: $options.goBack\n  }, null, 8 /* PROPS */, [\"onBack\"]), _createCommentVNode(\" 主体三分区布局：左上、左下、右侧 \"), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createCommentVNode(\" 修改 top-left 区域 \"), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_cache[14] || (_cache[14] = _createTextVNode(\" 输入区域 \")), _withDirectives(_createElementVNode(\"select\", {\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $data.inputMode = $event),\n    onChange: _cache[1] || (_cache[1] = (...args) => $options.resetInput && $options.resetInput(...args))\n  }, _cache[13] || (_cache[13] = [_createElementVNode(\"option\", {\n    value: \"image\"\n  }, \"Image\", -1 /* CACHED */), _createElementVNode(\"option\", {\n    value: \"video\"\n  }, \"Video\", -1 /* CACHED */), _createElementVNode(\"option\", {\n    value: \"files\"\n  }, \"Files\", -1 /* CACHED */), _createElementVNode(\"option\", {\n    value: \"realtime\"\n  }, \"Realtime\", -1 /* CACHED */)]), 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelSelect, $data.inputMode]]), _createElementVNode(\"button\", {\n    onClick: _cache[2] || (_cache[2] = (...args) => $options.handleInputAction && $options.handleInputAction(...args))\n  }, _toDisplayString($data.inputMode === 'realtime' ? '配置RTSP' : '上传'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_6, [_createCommentVNode(\" Image 模式 \"), $data.inputMode === 'image' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, [$data.currentFile.url ? (_openBlock(), _createElementBlock(\"img\", {\n    key: 0,\n    src: $data.currentFile.url,\n    alt: \"预览图\"\n  }, null, 8 /* PROPS */, _hoisted_8)) : (_openBlock(), _createElementBlock(\"div\", _hoisted_9, _cache[15] || (_cache[15] = [_createElementVNode(\"p\", null, \"请上传图片文件\", -1 /* CACHED */)])))])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" Video/Files 模式 \"), ['video', 'files'].includes($data.inputMode) ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, [$data.inputMode === 'video' && $data.currentFile.url ? (_openBlock(), _createElementBlock(\"video\", {\n    key: 0,\n    controls: \"\",\n    src: $data.currentFile.url,\n    class: \"preview-content\"\n  }, null, 8 /* PROPS */, _hoisted_12)) : $data.inputMode === 'files' && $data.filesList.length > 0 ? (_openBlock(), _createElementBlock(\"img\", {\n    key: 1,\n    src: $data.filesList[$data.currentFileIndex].url,\n    alt: \"文件预览\",\n    class: \"preview-content\"\n  }, null, 8 /* PROPS */, _hoisted_13)) : (_openBlock(), _createElementBlock(\"div\", _hoisted_14, [$data.inputMode === 'video' ? (_openBlock(), _createElementBlock(\"p\", _hoisted_15, \"请上传视频文件\")) : (_openBlock(), _createElementBlock(\"p\", _hoisted_16, \"请上传图片文件\"))]))])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" Realtime 模式 \"), $data.inputMode === 'realtime' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, [$data.rtspConnected ? (_openBlock(), _createElementBlock(\"video\", _hoisted_19, null, 512 /* NEED_PATCH */)) : (_openBlock(), _createElementBlock(\"div\", _hoisted_20, _cache[16] || (_cache[16] = [_createElementVNode(\"p\", null, \"请配置RTSP流地址\", -1 /* CACHED */)])))])])) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" 底部操作区 \"), _createElementVNode(\"div\", _hoisted_21, [$data.inputMode !== 'realtime' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_22, [$data.currentFile.name ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 0\n  }, [_createTextVNode(_toDisplayString($data.currentFile.name) + \" (\" + _toDisplayString($options.formatFileSize($data.currentFile.size)) + \") \", 1 /* TEXT */)], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true)])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_23, _toDisplayString($data.rtspUrl || '未配置RTSP地址'), 1 /* TEXT */)), _createElementVNode(\"div\", _hoisted_24, [_createCommentVNode(\" Files 模式特有按钮 \"), $data.inputMode === 'files' && $data.filesList.length > 0 ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 0\n  }, [_createElementVNode(\"button\", {\n    onClick: _cache[3] || (_cache[3] = (...args) => $options.prevFile && $options.prevFile(...args))\n  }, \"上一张\"), _createElementVNode(\"button\", {\n    onClick: _cache[4] || (_cache[4] = (...args) => $options.nextFile && $options.nextFile(...args))\n  }, \"下一张\"), _createElementVNode(\"button\", {\n    onClick: _cache[5] || (_cache[5] = (...args) => $options.deleteCurrent && $options.deleteCurrent(...args))\n  }, \"删除\")], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 通用按钮 \"), $data.inputMode === 'realtime' ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 1,\n    onClick: _cache[6] || (_cache[6] = (...args) => $options.toggleRtspConnection && $options.toggleRtspConnection(...args))\n  }, _toDisplayString($data.rtspConnected ? '断开连接' : '连接'), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), $options.hasFile ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 2,\n    onClick: _cache[7] || (_cache[7] = (...args) => $options.resetInput && $options.resetInput(...args))\n  }, \"重置\")) : _createCommentVNode(\"v-if\", true)])])]), _createCommentVNode(\" 修改 bottom-left 区域 \"), _createElementVNode(\"div\", _hoisted_25, [_createElementVNode(\"div\", _hoisted_26, [_cache[19] || (_cache[19] = _createTextVNode(\" 处理区域 \")), _withDirectives(_createElementVNode(\"select\", {\n    \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $data.preprocessMethod = $event)\n  }, _cache[17] || (_cache[17] = [_createStaticVNode(\"<option value=\\\"\\\" data-v-1f1178c8>无预处理</option><option value=\\\"dehaze\\\" data-v-1f1178c8>去雾</option><option value=\\\"light\\\" data-v-1f1178c8>去光照</option><option value=\\\"motion\\\" data-v-1f1178c8>去运动模糊</option><option value=\\\"denoise\\\" data-v-1f1178c8>去噪</option><option value=\\\"superres\\\" data-v-1f1178c8>超分辨</option>\", 6)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.preprocessMethod]]), _withDirectives(_createElementVNode(\"select\", {\n    \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $data.detectionMethod = $event)\n  }, _cache[18] || (_cache[18] = [_createElementVNode(\"option\", {\n    value: \"detection\"\n  }, \"检测识别\", -1 /* CACHED */), _createElementVNode(\"option\", {\n    value: \"fewshot\"\n  }, \"小样本\", -1 /* CACHED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.detectionMethod]]), _createElementVNode(\"button\", {\n    onClick: _cache[10] || (_cache[10] = (...args) => $options.processContent && $options.processContent(...args))\n  }, \"处理\"), $data.detectionMethod === 'fewshot' ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 0,\n    onClick: _cache[11] || (_cache[11] = (...args) => _ctx.preTrain && _ctx.preTrain(...args))\n  }, \"样本预训练\")) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_27, [_createCommentVNode(\" 结果展示区 \"), _createElementVNode(\"div\", _hoisted_28, [_createCommentVNode(\" 普通模式 \"), $data.inputMode !== 'realtime' ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 0\n  }, [$data.processedResult && $options.isImageType ? (_openBlock(), _createElementBlock(\"img\", {\n    key: 0,\n    src: $data.processedResult,\n    alt: \"处理结果\"\n  }, null, 8 /* PROPS */, _hoisted_29)) : $data.processedResult && $options.isVideoType ? (_openBlock(), _createElementBlock(\"video\", {\n    key: 1,\n    controls: \"\",\n    src: $data.processedResult\n  }, null, 8 /* PROPS */, _hoisted_30)) : (_openBlock(), _createElementBlock(\"div\", _hoisted_31, _cache[20] || (_cache[20] = [_createElementVNode(\"p\", null, \"处理结果将显示在此处\", -1 /* CACHED */)])))], 64 /* STABLE_FRAGMENT */)) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" 实时模式 \"), _createElementVNode(\"canvas\", _hoisted_32, null, 512 /* NEED_PATCH */)], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */))])]), _createCommentVNode(\" 底部操作区 \"), _createElementVNode(\"div\", _hoisted_33, [$data.inputMode !== 'realtime' && $data.processedResult ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 0,\n    onClick: _cache[12] || (_cache[12] = (...args) => $options.showDetailDialog && $options.showDetailDialog(...args)),\n    class: \"detail-btn\"\n  }, \" 查看详情 \")) : _createCommentVNode(\"v-if\", true)])])]), _cache[21] || (_cache[21] = _createElementVNode(\"div\", {\n    class: \"pane right\"\n  }, [_createCommentVNode(\" 右侧区域内容 \"), _createElementVNode(\"div\", {\n    class: \"pane-title\"\n  }, \"右侧区域\"), _createElementVNode(\"div\", {\n    class: \"pane-body\"\n  })], -1 /* CACHED */))])]);\n}", "map": {"version": 3, "names": ["class", "ref", "autoplay", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_HeaderComponent", "title", "showBackButton", "onBack", "$options", "goBack", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "$data", "inputMode", "$event", "onChange", "_cache", "args", "resetInput", "value", "onClick", "handleInputAction", "_hoisted_6", "_hoisted_7", "currentFile", "url", "src", "alt", "_hoisted_9", "includes", "_hoisted_10", "_hoisted_11", "controls", "filesList", "length", "currentFileIndex", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "rtspConnected", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_hoisted_22", "name", "_Fragment", "key", "_toDisplayString", "formatFileSize", "size", "_hoisted_23", "rtspUrl", "_hoisted_24", "prevFile", "nextFile", "deleteCurrent", "toggleRtspConnection", "hasFile", "_hoisted_25", "_hoisted_26", "preprocessMethod", "detectionMethod", "processContent", "_ctx", "preTrain", "_hoisted_27", "_hoisted_28", "processedResult", "isImageType", "isVideoType", "_hoisted_31", "_hoisted_32", "_hoisted_33", "showDetailDialog"], "sources": ["D:\\Projs\\lujun\\new_proj\\new_proj\\new_front\\src\\views\\detected.vue"], "sourcesContent": ["<template>\n  <div class=\"detected\">\n    <HeaderComponent title=\"智能判读\" :showBackButton=\"true\" @back=\"goBack\" />\n    <!-- 主体三分区布局：左上、左下、右侧 -->\n    <div class=\"content\">\n      <div class=\"left\">\n        <!-- 修改 top-left 区域 -->\n        <div class=\"pane top-left\">\n          <div class=\"pane-title\">\n            输入区域\n            <select v-model=\"inputMode\" @change=\"resetInput\">\n              <option value=\"image\">Image</option>\n              <option value=\"video\">Video</option>\n              <option value=\"files\">Files</option>\n              <option value=\"realtime\">Realtime</option>\n            </select>\n            <button @click=\"handleInputAction\">\n              {{ inputMode === 'realtime' ? '配置RTSP' : '上传' }}\n            </button>\n          </div>\n\n          <div class=\"pane-body\">\n            <!-- Image 模式 -->\n            <div v-if=\"inputMode === 'image'\" class=\"preview-container\">\n              <img v-if=\"currentFile.url\" :src=\"currentFile.url\" alt=\"预览图\">\n              <div v-else class=\"upload-placeholder\">\n                <p>请上传图片文件</p>\n              </div>\n            </div>\n\n            <!-- Video/Files 模式 -->\n            <div v-if=\"['video', 'files'].includes(inputMode)\" class=\"preview-container\">\n              <div class=\"preview-wrapper\">\n                <video v-if=\"inputMode === 'video' && currentFile.url\" controls :src=\"currentFile.url\"\n                  class=\"preview-content\"></video>\n                <img v-else-if=\"inputMode === 'files' && filesList.length > 0\" :src=\"filesList[currentFileIndex].url\"\n                  alt=\"文件预览\" class=\"preview-content\">\n                <div v-else class=\"upload-placeholder\">\n                  <p v-if=\"inputMode === 'video'\">请上传视频文件</p>\n                  <p v-else>请上传图片文件</p>\n                </div>\n              </div>\n            </div>\n\n            <!-- Realtime 模式 -->\n            <div v-if=\"inputMode === 'realtime'\" class=\"preview-container\">\n              <div class=\"preview-wrapper\">\n                <video v-if=\"rtspConnected\" ref=\"rtspPlayer\" autoplay class=\"preview-content\"></video>\n                <div v-else class=\"upload-placeholder\">\n                  <p>请配置RTSP流地址</p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 底部操作区 -->\n          <div class=\"pane-footer\">\n            <div v-if=\"inputMode !== 'realtime'\" class=\"file-info\">\n              <template v-if=\"currentFile.name\">\n                {{ currentFile.name }} ({{ formatFileSize(currentFile.size) }})\n              </template>\n            </div>\n            <div v-else class=\"file-info\">\n              {{ rtspUrl || '未配置RTSP地址' }}\n            </div>\n\n            <div class=\"action-buttons\">\n              <!-- Files 模式特有按钮 -->\n              <template v-if=\"inputMode === 'files' && filesList.length > 0\">\n                <button @click=\"prevFile\">上一张</button>\n                <button @click=\"nextFile\">下一张</button>\n                <button @click=\"deleteCurrent\">删除</button>\n              </template>\n\n              <!-- 通用按钮 -->\n              <button v-if=\"inputMode === 'realtime'\" @click=\"toggleRtspConnection\">\n                {{ rtspConnected ? '断开连接' : '连接' }}\n              </button>\n              <button v-if=\"hasFile\" @click=\"resetInput\">重置</button>\n            </div>\n          </div>\n        </div>\n        <!-- 修改 bottom-left 区域 -->\n        <div class=\"pane bottom-left\">\n          <div class=\"pane-title\">\n            处理区域\n            <select v-model=\"preprocessMethod\">\n              <option value=\"\">无预处理</option>\n              <option value=\"dehaze\">去雾</option>\n              <option value=\"light\">去光照</option>\n              <option value=\"motion\">去运动模糊</option>\n              <option value=\"denoise\">去噪</option>\n              <option value=\"superres\">超分辨</option>\n            </select>\n\n            <select v-model=\"detectionMethod\">\n              <option value=\"detection\">检测识别</option>\n              <option value=\"fewshot\">小样本</option>\n            </select>\n\n            <button @click=\"processContent\">处理</button>\n            <button v-if=\"detectionMethod === 'fewshot'\" @click=\"preTrain\">样本预训练</button>\n          </div>\n\n          <div class=\"pane-body\">\n            <!-- 结果展示区 -->\n            <div class=\"result-container\">\n              <!-- 普通模式 -->\n              <template v-if=\"inputMode !== 'realtime'\">\n                <img v-if=\"processedResult && isImageType\" :src=\"processedResult\" alt=\"处理结果\">\n                <video v-else-if=\"processedResult && isVideoType\" controls :src=\"processedResult\"></video>\n                <div v-else class=\"result-placeholder\">\n                  <p>处理结果将显示在此处</p>\n                </div>\n              </template>\n\n              <!-- 实时模式 -->\n              <canvas v-else ref=\"resultCanvas\"></canvas>\n            </div>\n          </div>\n\n          <!-- 底部操作区 -->\n          <div class=\"pane-footer\">\n            <button v-if=\"inputMode !== 'realtime' && processedResult\" @click=\"showDetailDialog\" class=\"detail-btn\">\n              查看详情\n            </button>\n          </div>\n        </div>\n      </div>\n      <div class=\"pane right\">\n        <!-- 右侧区域内容 -->\n        <div class=\"pane-title\">右侧区域</div>\n        <div class=\"pane-body\"></div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport HeaderComponent from '@/components/header.vue'\nimport { APIUploadData } from '@/api/api'\nimport { serverAddress } from '@/api/config';\n\nexport default {\n  name: 'DetectedView',\n  components: {\n    HeaderComponent\n  },\n  data() {\n    return {\n      // 输入相关\n      inputMode: 'image',\n      currentFile: { name: '', size: 0, url: '' },\n      filesList: [],\n      currentFileIndex: 0,\n      rtspUrl: '',\n      rtspConnected: false,\n\n      // 处理相关\n      preprocessMethod: '',\n      detectionMethod: 'detection',\n      processedResult: null,\n\n      // 弹窗控制\n      detailDialogVisible: false\n    }\n  },\n  computed: {\n    hasFile() {\n      return this.currentFile.url || this.filesList.length > 0\n    },\n    isImageType() {\n      return this.inputMode === 'image' || this.inputMode === 'files'\n    },\n    isVideoType() {\n      return this.inputMode === 'video'\n    }\n  },\n  methods: {\n    goBack() {\n      this.$router.go(-1)\n    },\n    // 添加文件大小格式化方法\n    formatFileSize(bytes) {\n      if (!bytes) return '0 Bytes';\n      const k = 1024;\n      const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n      const i = Math.floor(Math.log(bytes) / Math.log(k));\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    },\n    // 添加文件操作方法\n    prevFile() {\n      if (this.currentFileIndex > 0) {\n        this.currentFileIndex--;\n        this.currentFile = this.filesList[this.currentFileIndex];\n      }\n    },\n    nextFile() {\n      if (this.currentFileIndex < this.filesList.length - 1) {\n        this.currentFileIndex++;\n        this.currentFile = this.filesList[this.currentFileIndex];\n      }\n    },\n    deleteCurrent() {\n      if (this.filesList.length > 0) {\n        this.filesList.splice(this.currentFileIndex, 1);\n        if (this.filesList.length === 0) {\n          this.currentFile = {};\n          this.currentFileIndex = 0;\n        } else {\n          if (this.currentFileIndex >= this.filesList.length) {\n            this.currentFileIndex = this.filesList.length - 1;\n          }\n          this.currentFile = this.filesList[this.currentFileIndex];\n        }\n      }\n    },\n    // 上传文件处理\n    async handleInputAction() {\n      if (this.inputMode === 'realtime') {\n        this.showRtspDialog();\n        return;\n      }\n\n      const input = document.createElement('input');\n      input.type = 'file';\n\n      // 设置文件类型\n      if (this.inputMode === 'image') {\n        input.accept = 'image/*';\n      } else if (this.inputMode === 'video') {\n        input.accept = 'video/*';\n      } else if (this.inputMode === 'files') {\n        input.accept = 'image/*';\n        input.multiple = true;\n      }\n\n      input.onchange = async (e) => {\n        const files = Array.from(e.target.files);\n        if (files.length === 0) return;\n\n        // 重置处理结果\n        this.processedResult = null;\n\n        if (this.inputMode === 'files') {\n          this.filesList = [];\n          for (const file of files) {\n            const url = await this.uploadFile(file);\n            this.filesList.push({\n              name: file.name,\n              size: file.size,\n              url\n            });\n          }\n          this.currentFileIndex = 0;\n          this.currentFile = this.filesList[0] || {};\n        } else {\n          const file = files[0];\n          const url = await this.uploadFile(file);\n          this.currentFile = {\n            name: file.name,\n            size: file.size,\n            url\n          };\n        }\n      };\n\n      input.click();\n    },\n\n    // 文件上传API\n    async uploadFile(file) {\n      const formData = new FormData();\n      formData.append('file', file);\n\n      try {\n        const response = await APIUploadData(formData);\n\n        return serverAddress + response.url; // 后端返回的文件访问路径\n      } catch (error) {\n        console.error('上传失败:', error);\n        return null;\n      }\n    },\n\n    // 重置当前输入\n    resetInput() {\n      if (this.inputMode === 'files') {\n        this.filesList = [];\n        this.currentFileIndex = 0;\n      }\n      this.currentFile = {};\n      this.processedResult = null;\n\n      if (this.inputMode === 'realtime' && this.rtspConnected) {\n        this.toggleRtspConnection();\n      }\n    },\n\n    // RTSP配置弹窗\n    showRtspDialog() {\n      this.$prompt('请输入RTSP流地址', '配置RTSP', {\n        inputValue: this.rtspUrl,\n        inputValidator: (value) => {\n          if (!value) return '请输入RTSP地址';\n          if (!value.startsWith('rtsp://')) return '请输入有效的RTSP地址';\n          return true;\n        },\n        confirmButtonText: '确认连接',\n        cancelButtonText: '取消',\n        showTestButton: true,\n        testButtonText: '测试连接',\n        beforeTest: (action, instance, done) => {\n          // 测试连接逻辑\n          this.testRtspConnection(instance.inputValue)\n            .then(success => {\n              if (success) {\n                this.$message.success('连接测试成功');\n              } else {\n                this.$message.error('连接测试失败');\n              }\n              done();\n            });\n          return false; // 阻止默认关闭\n        }\n      }).then(({ value }) => {\n        this.rtspUrl = value;\n        this.toggleRtspConnection();\n      });\n    },\n\n    // 测试RTSP连接\n    async testRtspConnection(url) {\n      try {\n        const response = await this.$http.post('/rtsp/test', { url });\n        return response.data.success;\n      } catch (error) {\n        return false;\n      }\n    },\n\n    // 切换RTSP连接状态\n    toggleRtspConnection() {\n      if (this.rtspConnected) {\n        this.stopRtspStream();\n      } else if (this.rtspUrl) {\n        this.startRtspStream();\n      }\n      this.rtspConnected = !this.rtspConnected;\n    },\n\n    // 开始RTSP流\n    async startRtspStream() {\n      try {\n        // 创建视频元素\n        const video = this.$refs.rtspPlayer;\n\n        // 获取WebRTC流或使用MSE\n        const streamUrl = await this.getRtspStreamUrl();\n        video.src = streamUrl;\n\n        // 开始播放\n        await video.play();\n      } catch (error) {\n        console.error('RTSP连接失败:', error);\n        this.rtspConnected = false;\n      }\n    },\n\n    // 停止RTSP流\n    stopRtspStream() {\n      const video = this.$refs.rtspPlayer;\n      if (video) {\n        video.pause();\n        video.src = '';\n      }\n    },\n\n    // 处理内容\n    async processContent() {\n      if (!this.hasFile && !this.rtspConnected) {\n        this.$message.warning('请先选择输入内容');\n        return;\n      }\n\n      let processData = {};\n\n      if (this.inputMode === 'files') {\n        processData = {\n          file_urls: this.filesList.map(f => f.url),\n          current_index: this.currentFileIndex\n        };\n      } else if (this.inputMode === 'realtime') {\n        processData = { rtsp_url: this.rtspUrl };\n      } else {\n        processData = { file_url: this.currentFile.url };\n      }\n\n      // 添加处理参数\n      processData.preprocess = this.preprocessMethod;\n      processData.detection = this.detectionMethod;\n\n      try {\n        const response = await this.$http.post('/process', processData);\n        this.processedResult = response.data.result_url;\n\n        // 实时模式需要特殊处理\n        if (this.inputMode === 'realtime') {\n          this.startRealTimeProcessing();\n        }\n      } catch (error) {\n        console.error('处理失败:', error);\n      }\n    },\n\n    // 开始实时处理\n    startRealTimeProcessing() {\n      const canvas = this.$refs.resultCanvas;\n      const ctx = canvas.getContext('2d');\n\n      // 设置Canvas尺寸\n      const video = this.$refs.rtspPlayer;\n      canvas.width = video.videoWidth;\n      canvas.height = video.videoHeight;\n\n      // 处理帧的函数\n      const processFrame = () => {\n        if (!this.rtspConnected) return;\n\n        // 绘制视频帧到Canvas\n        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n\n        // 获取图像数据并发送到后端处理\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        this.sendFrameForProcessing(imageData);\n\n        // 循环处理\n        requestAnimationFrame(processFrame);\n      };\n\n      processFrame();\n    },\n\n    // 发送帧数据到后端处理\n    async sendFrameForProcessing(imageData) {\n      try {\n        // 创建Blob发送\n        const blob = new Blob([imageData.data], { type: 'application/octet-stream' });\n        const formData = new FormData();\n        formData.append('frame', blob);\n        formData.append('width', imageData.width);\n        formData.append('height', imageData.height);\n\n        const response = await this.$http.post('/process/frame', formData);\n        const result = response.data;\n\n        // 在Canvas上绘制处理结果\n        this.drawProcessingResult(result);\n      } catch (error) {\n        console.error('帧处理失败:', error);\n      }\n    },\n\n    // 查看详情弹窗\n    showDetailDialog() {\n      this.detailDialogVisible = true;\n    }\n  }\n\n}\n</script>\n\n<style scoped>\n@import '@/assets/css/variables.css';\n\n.detected {\n  overflow: hidden;\n  background-size: cover;\n  position: relative;\n  box-sizing: border-box;\n  width: 100%;\n  height: 100vh;\n  margin: 0;\n  padding: 0;\n  background-image: url(\"../assets/imgs/backimg.png\");\n  display: flex;\n  flex-direction: column;\n}\n\n/* 主体区域占满 Header 之外的空间 */\n.content {\n  flex: 1;\n  min-height: 0;\n  /* 避免子元素溢出 */\n  display: flex;\n  gap: 12px;\n  padding: 12px;\n  box-sizing: border-box;\n}\n\n/* 左侧列（包含左上/左下） */\n.left {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n  width: 50%;\n  min-width: 360px;\n}\n\n/* 右侧全高区域 */\n.right {\n  flex: 1;\n}\n\n/* 面板基础样式 */\n.pane {\n  background: rgba(255, 255, 255, 0.8);\n  border: 1px solid rgba(0, 0, 0, 0.08);\n  border-radius: 10px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);\n  backdrop-filter: blur(6px);\n  display: flex;\n  flex-direction: column;\n  min-height: 0;\n}\n\n.top-left,\n.bottom-left {\n  flex: 1;\n  /* 左上与左下均分高度 */\n}\n\n.pane-title {\n  font-weight: 600;\n  padding: 10px 12px;\n  border-bottom: 1px solid rgba(0, 0, 0, 0.06);\n  color: #333;\n  display: flex;\n  align-items: center;\n  gap: 10px\n}\n\n.pane-body {\n  flex: 1;\n  min-height: 0;\n  padding: 12px;\n  overflow: auto;\n}\n\n.preview-container,\n.result-container {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  overflow: hidden;\n}\n\n.preview-wrapper {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  overflow: hidden;\n}\n\n.preview-content {\n  max-width: 100%;\n  max-height: 100%;\n  object-fit: contain;\n  display: block;\n}\n\n.upload-placeholder,\n.result-placeholder {\n  border: 2px dashed #ccc;\n  border-radius: 8px;\n  width: 90%;\n  height: 80%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  color: #666;\n}\n\n.pane-footer {\n  display: flex;\n  justify-content: space-between;\n  padding: 10px 12px;\n  border-top: 1px solid rgba(0, 0, 0, 0.06);\n}\n\n.action-buttons {\n  display: flex;\n  gap: 8px;\n}\n\n.file-info {\n  font-size: 0.9rem;\n  color: #666;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  max-width: 60%;\n}\n\n.detail-btn {\n  margin-left: auto;\n}\n\ncanvas {\n  width: 100%;\n  height: 100%;\n  background: #000;\n}\n\n/* 响应式：窄屏下改为上下排列 */\n@media (max-width: 1200px) {\n  .content {\n    flex-direction: column;\n  }\n\n  .left,\n  .right {\n    width: 100%;\n    min-width: 0;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAU;;EAGdA,KAAK,EAAC;AAAS;;EACbA,KAAK,EAAC;AAAM;;EAEVA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAY;;EAalBA,KAAK,EAAC;AAAW;;;EAEcA,KAAK,EAAC;;;;;EAE1BA,KAAK,EAAC;;;;EAM+BA,KAAK,EAAC;;;EAClDA,KAAK,EAAC;AAAiB;;;;;EAKdA,KAAK,EAAC;;;;;;;;;;EAQeA,KAAK,EAAC;;;EACpCA,KAAK,EAAC;AAAiB;;;EACEC,GAAG,EAAC,YAAY;EAACC,QAAQ,EAAR,EAAQ;EAACF,KAAK,EAAC;;;;EAChDA,KAAK,EAAC;;;EAQnBA,KAAK,EAAC;AAAa;;;EACeA,KAAK,EAAC;;;;EAK/BA,KAAK,EAAC;;;EAIbA,KAAK,EAAC;AAAgB;;EAiB1BA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAY;;EAoBlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAkB;;;;;EAKbA,KAAK,EAAC;;;EAMLC,GAAG,EAAC;AAAc;;EAKhCD,KAAK,EAAC;AAAa;;;uBAzHhCG,mBAAA,CAsIM,OAtINC,UAsIM,GArIJC,YAAA,CAAsEC,0BAAA;IAArDC,KAAK,EAAC,MAAM;IAAEC,cAAc,EAAE,IAAI;IAAGC,MAAI,EAAEC,QAAA,CAAAC;uCAC5DC,mBAAA,sBAAyB,EACzBC,mBAAA,CAkIM,OAlINC,UAkIM,GAjIJD,mBAAA,CA2HM,OA3HNE,UA2HM,GA1HJH,mBAAA,oBAAuB,EACvBC,mBAAA,CA0EM,OA1ENG,UA0EM,GAzEJH,mBAAA,CAWM,OAXNI,UAWM,G,6CAXkB,QAEtB,I,gBAAAJ,mBAAA,CAKS;+DALQK,KAAA,CAAAC,SAAS,GAAAC,MAAA;IAAGC,QAAM,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEb,QAAA,CAAAc,UAAA,IAAAd,QAAA,CAAAc,UAAA,IAAAD,IAAA,CAAU;kCAC7CV,mBAAA,CAAoC;IAA5BY,KAAK,EAAC;EAAO,GAAC,OAAK,oBAC3BZ,mBAAA,CAAoC;IAA5BY,KAAK,EAAC;EAAO,GAAC,OAAK,oBAC3BZ,mBAAA,CAAoC;IAA5BY,KAAK,EAAC;EAAO,GAAC,OAAK,oBAC3BZ,mBAAA,CAA0C;IAAlCY,KAAK,EAAC;EAAU,GAAC,UAAQ,mB,2DAJlBP,KAAA,CAAAC,SAAS,E,GAM1BN,mBAAA,CAES;IAFAa,OAAK,EAAAJ,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEb,QAAA,CAAAiB,iBAAA,IAAAjB,QAAA,CAAAiB,iBAAA,IAAAJ,IAAA,CAAiB;sBAC5BL,KAAA,CAAAC,SAAS,kD,GAIhBN,mBAAA,CAgCM,OAhCNe,UAgCM,GA/BJhB,mBAAA,cAAiB,EACNM,KAAA,CAAAC,SAAS,gB,cAApBhB,mBAAA,CAKM,OALN0B,UAKM,GAJOX,KAAA,CAAAY,WAAW,CAACC,GAAG,I,cAA1B5B,mBAAA,CAA6D;;IAAhC6B,GAAG,EAAEd,KAAA,CAAAY,WAAW,CAACC,GAAG;IAAEE,GAAG,EAAC;wDACvD9B,mBAAA,CAEM,OAFN+B,UAEM,EAAAZ,MAAA,SAAAA,MAAA,QADJT,mBAAA,CAAc,WAAX,SAAO,mB,4CAIdD,mBAAA,oBAAuB,E,mBACOuB,QAAQ,CAACjB,KAAA,CAAAC,SAAS,K,cAAhDhB,mBAAA,CAWM,OAXNiC,WAWM,GAVJvB,mBAAA,CASM,OATNwB,WASM,GARSnB,KAAA,CAAAC,SAAS,gBAAgBD,KAAA,CAAAY,WAAW,CAACC,GAAG,I,cAArD5B,mBAAA,CACkC;;IADqBmC,QAAQ,EAAR,EAAQ;IAAEN,GAAG,EAAEd,KAAA,CAAAY,WAAW,CAACC,GAAG;IACnF/B,KAAK,EAAC;0CACQkB,KAAA,CAAAC,SAAS,gBAAgBD,KAAA,CAAAqB,SAAS,CAACC,MAAM,Q,cAAzDrC,mBAAA,CACqC;;IAD2B6B,GAAG,EAAEd,KAAA,CAAAqB,SAAS,CAACrB,KAAA,CAAAuB,gBAAgB,EAAEV,GAAG;IAClGE,GAAG,EAAC,MAAM;IAACjC,KAAK,EAAC;yDACnBG,mBAAA,CAGM,OAHNuC,WAGM,GAFKxB,KAAA,CAAAC,SAAS,gB,cAAlBhB,mBAAA,CAA2C,KAAAwC,WAAA,EAAX,SAAO,M,cACvCxC,mBAAA,CAAqB,KAAAyC,WAAA,EAAX,SAAO,G,6CAKvBhC,mBAAA,iBAAoB,EACTM,KAAA,CAAAC,SAAS,mB,cAApBhB,mBAAA,CAOM,OAPN0C,WAOM,GANJhC,mBAAA,CAKM,OALNiC,WAKM,GAJS5B,KAAA,CAAA6B,aAAa,I,cAA1B5C,mBAAA,CAAsF,SAAtF6C,WAAsF,kC,cACtF7C,mBAAA,CAEM,OAFN8C,WAEM,EAAA3B,MAAA,SAAAA,MAAA,QADJT,mBAAA,CAAiB,WAAd,YAAU,mB,gDAMrBD,mBAAA,WAAc,EACdC,mBAAA,CAwBM,OAxBNqC,WAwBM,GAvBOhC,KAAA,CAAAC,SAAS,mB,cAApBhB,mBAAA,CAIM,OAJNgD,WAIM,GAHYjC,KAAA,CAAAY,WAAW,CAACsB,IAAI,I,cAAhCjD,mBAAA,CAEWkD,SAAA;IAAAC,GAAA;EAAA,I,kCADNpC,KAAA,CAAAY,WAAW,CAACsB,IAAI,IAAG,IAAE,GAAAG,gBAAA,CAAG7C,QAAA,CAAA8C,cAAc,CAACtC,KAAA,CAAAY,WAAW,CAAC2B,IAAI,KAAI,IAChE,gB,qFAEFtD,mBAAA,CAEM,OAFNuD,WAEM,EAAAH,gBAAA,CADDrC,KAAA,CAAAyC,OAAO,kCAGZ9C,mBAAA,CAaM,OAbN+C,WAaM,GAZJhD,mBAAA,kBAAqB,EACLM,KAAA,CAAAC,SAAS,gBAAgBD,KAAA,CAAAqB,SAAS,CAACC,MAAM,Q,cAAzDrC,mBAAA,CAIWkD,SAAA;IAAAC,GAAA;EAAA,IAHTzC,mBAAA,CAAsC;IAA7Ba,OAAK,EAAAJ,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEb,QAAA,CAAAmD,QAAA,IAAAnD,QAAA,CAAAmD,QAAA,IAAAtC,IAAA,CAAQ;KAAE,KAAG,GAC7BV,mBAAA,CAAsC;IAA7Ba,OAAK,EAAAJ,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEb,QAAA,CAAAoD,QAAA,IAAApD,QAAA,CAAAoD,QAAA,IAAAvC,IAAA,CAAQ;KAAE,KAAG,GAC7BV,mBAAA,CAA0C;IAAjCa,OAAK,EAAAJ,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEb,QAAA,CAAAqD,aAAA,IAAArD,QAAA,CAAAqD,aAAA,IAAAxC,IAAA,CAAa;KAAE,IAAE,E,kEAGnCX,mBAAA,UAAa,EACCM,KAAA,CAAAC,SAAS,mB,cAAvBhB,mBAAA,CAES;;IAFgCuB,OAAK,EAAAJ,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEb,QAAA,CAAAsD,oBAAA,IAAAtD,QAAA,CAAAsD,oBAAA,IAAAzC,IAAA,CAAoB;sBAC/DL,KAAA,CAAA6B,aAAa,oC,mCAEJrC,QAAA,CAAAuD,OAAO,I,cAArB9D,mBAAA,CAAsD;;IAA9BuB,OAAK,EAAAJ,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEb,QAAA,CAAAc,UAAA,IAAAd,QAAA,CAAAc,UAAA,IAAAD,IAAA,CAAU;KAAE,IAAE,K,yCAInDX,mBAAA,uBAA0B,EAC1BC,mBAAA,CA4CM,OA5CNqD,WA4CM,GA3CJrD,mBAAA,CAkBM,OAlBNsD,WAkBM,G,6CAlBkB,QAEtB,I,gBAAAtD,mBAAA,CAOS;+DAPQK,KAAA,CAAAkD,gBAAgB,GAAAhD,MAAA;kaAAhBF,KAAA,CAAAkD,gBAAgB,E,mBASjCvD,mBAAA,CAGS;+DAHQK,KAAA,CAAAmD,eAAe,GAAAjD,MAAA;kCAC9BP,mBAAA,CAAuC;IAA/BY,KAAK,EAAC;EAAW,GAAC,MAAI,oBAC9BZ,mBAAA,CAAoC;IAA5BY,KAAK,EAAC;EAAS,GAAC,KAAG,mB,2CAFZP,KAAA,CAAAmD,eAAe,E,GAKhCxD,mBAAA,CAA2C;IAAlCa,OAAK,EAAAJ,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEb,QAAA,CAAA4D,cAAA,IAAA5D,QAAA,CAAA4D,cAAA,IAAA/C,IAAA,CAAc;KAAE,IAAE,GACpBL,KAAA,CAAAmD,eAAe,kB,cAA7BlE,mBAAA,CAA6E;;IAA/BuB,OAAK,EAAAJ,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEgD,IAAA,CAAAC,QAAA,IAAAD,IAAA,CAAAC,QAAA,IAAAjD,IAAA,CAAQ;KAAE,OAAK,K,qCAGtEV,mBAAA,CAeM,OAfN4D,WAeM,GAdJ7D,mBAAA,WAAc,EACdC,mBAAA,CAYM,OAZN6D,WAYM,GAXJ9D,mBAAA,UAAa,EACGM,KAAA,CAAAC,SAAS,mB,cAAzBhB,mBAAA,CAMWkD,SAAA;IAAAC,GAAA;EAAA,IALEpC,KAAA,CAAAyD,eAAe,IAAIjE,QAAA,CAAAkE,WAAW,I,cAAzCzE,mBAAA,CAA6E;;IAAjC6B,GAAG,EAAEd,KAAA,CAAAyD,eAAe;IAAE1C,GAAG,EAAC;0CACpDf,KAAA,CAAAyD,eAAe,IAAIjE,QAAA,CAAAmE,WAAW,I,cAAhD1E,mBAAA,CAA0F;;IAAxCmC,QAAQ,EAAR,EAAQ;IAAEN,GAAG,EAAEd,KAAA,CAAAyD;yDACjExE,mBAAA,CAEM,OAFN2E,WAEM,EAAAxD,MAAA,SAAAA,MAAA,QADJT,mBAAA,CAAiB,WAAd,YAAU,mB,kDAKjBV,mBAAA,CAA2CkD,SAAA;IAAAC,GAAA;EAAA,IAD3C1C,mBAAA,UAAa,EACbC,mBAAA,CAA2C,UAA3CkE,WAA2C,8B,uDAI/CnE,mBAAA,WAAc,EACdC,mBAAA,CAIM,OAJNmE,WAIM,GAHU9D,KAAA,CAAAC,SAAS,mBAAmBD,KAAA,CAAAyD,eAAe,I,cAAzDxE,mBAAA,CAES;;IAFmDuB,OAAK,EAAAJ,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEb,QAAA,CAAAuE,gBAAA,IAAAvE,QAAA,CAAAuE,gBAAA,IAAA1D,IAAA,CAAgB;IAAEvB,KAAK,EAAC;KAAa,QAExG,K,qEAINa,mBAAA,CAIM;IAJDb,KAAK,EAAC;EAAY,IACrBY,mBAAA,YAAe,EACfC,mBAAA,CAAkC;IAA7Bb,KAAK,EAAC;EAAY,GAAC,MAAI,GAC5Ba,mBAAA,CAA6B;IAAxBb,KAAK,EAAC;EAAW,G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}