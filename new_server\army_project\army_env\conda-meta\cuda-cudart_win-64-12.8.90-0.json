{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/noarch", "constrains": [], "depends": ["cuda-version >=12.8,<12.9.0a0"], "extracted_package_dir": "", "files": [], "fn": "cuda-cudart_win-64-12.8.90-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "", "type": 1}, "md5": "b51bf910437a50be1b5c577518e769e3", "name": "cuda-cudar<PERSON>_win-64", "noarch": "generic", "package_tarball_full_path": "", "package_type": "noarch_generic", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "42d6cda15b39ea8eaed86001f8fff2159c3843e935b12a5caac3274d8524d436", "size": 17067, "subdir": "noarch", "timestamp": 1739448563000, "url": "https://conda.anaconda.org/nvidia/noarch/cuda-cudart_win-64-12.8.90-0.conda", "version": "12.8.90"}