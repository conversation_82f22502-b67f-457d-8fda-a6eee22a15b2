{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport HeaderComponent from '@/components/header.vue';\nimport { APIUploadData } from '@/api/api';\nimport { serverAddress } from '@/api/config';\nexport default {\n  name: 'DetectedView',\n  components: {\n    HeaderComponent\n  },\n  data() {\n    return {\n      // 输入相关\n      inputMode: 'image',\n      currentFile: {\n        name: '',\n        size: 0,\n        url: ''\n      },\n      filesList: [],\n      currentFileIndex: 0,\n      rtspUrl: '',\n      rtspConnected: false,\n      // 处理相关\n      preprocessMethod: '',\n      detectionMethod: 'detection',\n      processedResult: null,\n      // 弹窗控制\n      detailDialogVisible: false\n    };\n  },\n  computed: {\n    hasFile() {\n      return this.currentFile.url || this.filesList.length > 0;\n    },\n    isImageType() {\n      return this.inputMode === 'image' || this.inputMode === 'files';\n    },\n    isVideoType() {\n      return this.inputMode === 'video';\n    }\n  },\n  methods: {\n    goBack() {\n      this.$router.go(-1);\n    },\n    // 添加文件大小格式化方法\n    formatFileSize(bytes) {\n      if (!bytes) return '0 Bytes';\n      const k = 1024;\n      const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n      const i = Math.floor(Math.log(bytes) / Math.log(k));\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    },\n    // 添加文件操作方法\n    prevFile() {\n      if (this.currentFileIndex > 0) {\n        this.currentFileIndex--;\n        this.currentFile = this.filesList[this.currentFileIndex];\n      }\n    },\n    nextFile() {\n      if (this.currentFileIndex < this.filesList.length - 1) {\n        this.currentFileIndex++;\n        this.currentFile = this.filesList[this.currentFileIndex];\n      }\n    },\n    deleteCurrent() {\n      if (this.filesList.length > 0) {\n        this.filesList.splice(this.currentFileIndex, 1);\n        if (this.filesList.length === 0) {\n          this.currentFile = {};\n          this.currentFileIndex = 0;\n        } else {\n          if (this.currentFileIndex >= this.filesList.length) {\n            this.currentFileIndex = this.filesList.length - 1;\n          }\n          this.currentFile = this.filesList[this.currentFileIndex];\n        }\n      }\n    },\n    // 上传文件处理\n    async handleInputAction() {\n      if (this.inputMode === 'realtime') {\n        this.showRtspDialog();\n        return;\n      }\n      const input = document.createElement('input');\n      input.type = 'file';\n\n      // 设置文件类型\n      if (this.inputMode === 'image') {\n        input.accept = 'image/*';\n      } else if (this.inputMode === 'video') {\n        input.accept = 'video/*';\n      } else if (this.inputMode === 'files') {\n        input.accept = 'image/*';\n        input.multiple = true;\n      }\n      input.onchange = async e => {\n        const files = Array.from(e.target.files);\n        if (files.length === 0) return;\n\n        // 重置处理结果\n        this.processedResult = null;\n        if (this.inputMode === 'files') {\n          this.filesList = [];\n          for (const file of files) {\n            const url = await this.uploadFile(file);\n            this.filesList.push({\n              name: file.name,\n              size: file.size,\n              url\n            });\n          }\n          this.currentFileIndex = 0;\n          this.currentFile = this.filesList[0] || {};\n        } else {\n          const file = files[0];\n          const url = await this.uploadFile(file);\n          this.currentFile = {\n            name: file.name,\n            size: file.size,\n            url\n          };\n        }\n      };\n      input.click();\n    },\n    // 文件上传API\n    async uploadFile(file) {\n      const formData = new FormData();\n      formData.append('file', file);\n      try {\n        const response = await APIUploadData(formData);\n        return serverAddress + response.url; // 后端返回的文件访问路径\n      } catch (error) {\n        console.error('上传失败:', error);\n        return null;\n      }\n    },\n    // 重置当前输入\n    resetInput() {\n      if (this.inputMode === 'files') {\n        this.filesList = [];\n        this.currentFileIndex = 0;\n      }\n      this.currentFile = {};\n      this.processedResult = null;\n      if (this.inputMode === 'realtime' && this.rtspConnected) {\n        this.toggleRtspConnection();\n      }\n    },\n    // RTSP配置弹窗\n    showRtspDialog() {\n      this.$prompt('请输入RTSP流地址', '配置RTSP', {\n        inputValue: this.rtspUrl,\n        inputValidator: value => {\n          if (!value) return '请输入RTSP地址';\n          if (!value.startsWith('rtsp://')) return '请输入有效的RTSP地址';\n          return true;\n        },\n        confirmButtonText: '确认连接',\n        cancelButtonText: '取消',\n        showTestButton: true,\n        testButtonText: '测试连接',\n        beforeTest: (action, instance, done) => {\n          // 测试连接逻辑\n          this.testRtspConnection(instance.inputValue).then(success => {\n            if (success) {\n              this.$message.success('连接测试成功');\n            } else {\n              this.$message.error('连接测试失败');\n            }\n            done();\n          });\n          return false; // 阻止默认关闭\n        }\n      }).then(({\n        value\n      }) => {\n        this.rtspUrl = value;\n        this.toggleRtspConnection();\n      });\n    },\n    // 测试RTSP连接\n    async testRtspConnection(url) {\n      try {\n        const response = await this.$http.post('/rtsp/test', {\n          url\n        });\n        return response.data.success;\n      } catch (error) {\n        return false;\n      }\n    },\n    // 切换RTSP连接状态\n    toggleRtspConnection() {\n      if (this.rtspConnected) {\n        this.stopRtspStream();\n      } else if (this.rtspUrl) {\n        this.startRtspStream();\n      }\n      this.rtspConnected = !this.rtspConnected;\n    },\n    // 开始RTSP流\n    async startRtspStream() {\n      try {\n        // 创建视频元素\n        const video = this.$refs.rtspPlayer;\n\n        // 获取WebRTC流或使用MSE\n        const streamUrl = await this.getRtspStreamUrl();\n        video.src = streamUrl;\n\n        // 开始播放\n        await video.play();\n      } catch (error) {\n        console.error('RTSP连接失败:', error);\n        this.rtspConnected = false;\n      }\n    },\n    // 停止RTSP流\n    stopRtspStream() {\n      const video = this.$refs.rtspPlayer;\n      if (video) {\n        video.pause();\n        video.src = '';\n      }\n    },\n    // 处理内容\n    async processContent() {\n      if (!this.hasFile && !this.rtspConnected) {\n        this.$message.warning('请先选择输入内容');\n        return;\n      }\n      let processData = {};\n      if (this.inputMode === 'files') {\n        processData = {\n          file_urls: this.filesList.map(f => f.url),\n          current_index: this.currentFileIndex\n        };\n      } else if (this.inputMode === 'realtime') {\n        processData = {\n          rtsp_url: this.rtspUrl\n        };\n      } else {\n        processData = {\n          file_url: this.currentFile.url\n        };\n      }\n\n      // 添加处理参数\n      processData.preprocess = this.preprocessMethod;\n      processData.detection = this.detectionMethod;\n      try {\n        const response = await this.$http.post('/process', processData);\n        this.processedResult = response.data.result_url;\n\n        // 实时模式需要特殊处理\n        if (this.inputMode === 'realtime') {\n          this.startRealTimeProcessing();\n        }\n      } catch (error) {\n        console.error('处理失败:', error);\n      }\n    },\n    // 开始实时处理\n    startRealTimeProcessing() {\n      const canvas = this.$refs.resultCanvas;\n      const ctx = canvas.getContext('2d');\n\n      // 设置Canvas尺寸\n      const video = this.$refs.rtspPlayer;\n      canvas.width = video.videoWidth;\n      canvas.height = video.videoHeight;\n\n      // 处理帧的函数\n      const processFrame = () => {\n        if (!this.rtspConnected) return;\n\n        // 绘制视频帧到Canvas\n        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n\n        // 获取图像数据并发送到后端处理\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        this.sendFrameForProcessing(imageData);\n\n        // 循环处理\n        requestAnimationFrame(processFrame);\n      };\n      processFrame();\n    },\n    // 发送帧数据到后端处理\n    async sendFrameForProcessing(imageData) {\n      try {\n        // 创建Blob发送\n        const blob = new Blob([imageData.data], {\n          type: 'application/octet-stream'\n        });\n        const formData = new FormData();\n        formData.append('frame', blob);\n        formData.append('width', imageData.width);\n        formData.append('height', imageData.height);\n        const response = await this.$http.post('/process/frame', formData);\n        const result = response.data;\n\n        // 在Canvas上绘制处理结果\n        this.drawProcessingResult(result);\n      } catch (error) {\n        console.error('帧处理失败:', error);\n      }\n    },\n    // 查看详情弹窗\n    showDetailDialog() {\n      this.detailDialogVisible = true;\n    }\n  }\n};", "map": {"version": 3, "names": ["HeaderComponent", "APIUploadData", "serverAddress", "name", "components", "data", "inputMode", "currentFile", "size", "url", "filesList", "currentFileIndex", "rtspUrl", "rtspConnected", "preprocessMethod", "detectionMethod", "processedResult", "detailDialogVisible", "computed", "hasFile", "length", "isImageType", "isVideoType", "methods", "goBack", "$router", "go", "formatFileSize", "bytes", "k", "sizes", "i", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "prevFile", "nextFile", "deleteCurrent", "splice", "handleInputAction", "showRtspDialog", "input", "document", "createElement", "type", "accept", "multiple", "onchange", "e", "files", "Array", "from", "target", "file", "uploadFile", "push", "click", "formData", "FormData", "append", "response", "error", "console", "resetInput", "toggleRtspConnection", "$prompt", "inputValue", "inputValidator", "value", "startsWith", "confirmButtonText", "cancelButtonText", "showTestButton", "testButtonText", "beforeTest", "action", "instance", "done", "testRtspConnection", "then", "success", "$message", "$http", "post", "stopRtspStream", "startRtspStream", "video", "$refs", "rtspPlayer", "streamUrl", "getRtspStreamUrl", "src", "play", "pause", "processContent", "warning", "processData", "file_urls", "map", "f", "current_index", "rtsp_url", "file_url", "preprocess", "detection", "result_url", "startRealTimeProcessing", "canvas", "result<PERSON><PERSON>vas", "ctx", "getContext", "width", "videoWidth", "height", "videoHeight", "processFrame", "drawImage", "imageData", "getImageData", "sendFrameForProcessing", "requestAnimationFrame", "blob", "Blob", "result", "drawProcessingResult", "showDetailDialog"], "sources": ["D:\\Projs\\lujun\\new_proj\\new_proj\\new_front\\src\\views\\detected.vue"], "sourcesContent": ["<template>\n  <div class=\"detected\">\n    <HeaderComponent title=\"智能判读\" :showBackButton=\"true\" @back=\"goBack\" />\n    <!-- 主体三分区布局：左上、左下、右侧 -->\n    <div class=\"content\">\n      <div class=\"left\">\n        <!-- 修改 top-left 区域 -->\n        <div class=\"pane top-left\">\n          <div class=\"pane-title\">\n            输入区域\n            <select v-model=\"inputMode\" @change=\"resetInput\">\n              <option value=\"image\">Image</option>\n              <option value=\"video\">Video</option>\n              <option value=\"files\">Files</option>\n              <option value=\"realtime\">Realtime</option>\n            </select>\n            <button @click=\"handleInputAction\">\n              {{ inputMode === 'realtime' ? '配置RTSP' : '上传' }}\n            </button>\n          </div>\n\n          <div class=\"pane-body\">\n            <!-- Image 模式 -->\n            <div v-if=\"inputMode === 'image'\" class=\"preview-container\">\n              <img v-if=\"currentFile.url\" :src=\"currentFile.url\" alt=\"预览图\">\n              <div v-else class=\"upload-placeholder\">\n                <p>请上传图片文件</p>\n              </div>\n            </div>\n\n            <!-- Video/Files 模式 -->\n            <div v-if=\"['video', 'files'].includes(inputMode)\" class=\"preview-container\">\n              <video v-if=\"inputMode === 'video' && currentFile.url\" controls :src=\"currentFile.url\"></video>\n              <img v-else-if=\"inputMode === 'files' && filesList.length > 0\" :src=\"filesList[currentFileIndex].url\"\n                alt=\"文件预览\">\n              <div v-else class=\"upload-placeholder\">\n                <p v-if=\"inputMode === 'video'\">请上传视频文件</p>\n                <p v-else>请上传图片文件</p>\n              </div>\n            </div>\n\n            <!-- Realtime 模式 -->\n            <div v-if=\"inputMode === 'realtime'\" class=\"preview-container\">\n              <video v-if=\"rtspConnected\" ref=\"rtspPlayer\" autoplay></video>\n              <div v-else class=\"upload-placeholder\">\n                <p>请配置RTSP流地址</p>\n              </div>\n            </div>\n          </div>\n\n          <!-- 底部操作区 -->\n          <div class=\"pane-footer\">\n            <div v-if=\"inputMode !== 'realtime'\" class=\"file-info\">\n              <template v-if=\"currentFile.name\">\n                {{ currentFile.name }} ({{ formatFileSize(currentFile.size) }})\n              </template>\n            </div>\n            <div v-else class=\"file-info\">\n              {{ rtspUrl || '未配置RTSP地址' }}\n            </div>\n\n            <div class=\"action-buttons\">\n              <!-- Files 模式特有按钮 -->\n              <template v-if=\"inputMode === 'files' && filesList.length > 0\">\n                <button @click=\"prevFile\">上一张</button>\n                <button @click=\"nextFile\">下一张</button>\n                <button @click=\"deleteCurrent\">删除</button>\n              </template>\n\n              <!-- 通用按钮 -->\n              <button v-if=\"inputMode === 'realtime'\" @click=\"toggleRtspConnection\">\n                {{ rtspConnected ? '断开连接' : '连接' }}\n              </button>\n              <button v-if=\"hasFile\" @click=\"resetInput\">重置</button>\n            </div>\n          </div>\n        </div>\n        <!-- 修改 bottom-left 区域 -->\n        <div class=\"pane bottom-left\">\n          <div class=\"pane-title\">\n            处理区域\n            <select v-model=\"preprocessMethod\">\n              <option value=\"\">无预处理</option>\n              <option value=\"dehaze\">去雾</option>\n              <option value=\"light\">去光照</option>\n              <option value=\"motion\">去运动模糊</option>\n              <option value=\"denoise\">去噪</option>\n              <option value=\"superres\">超分辨</option>\n            </select>\n\n            <select v-model=\"detectionMethod\">\n              <option value=\"detection\">检测识别</option>\n              <option value=\"fewshot\">小样本</option>\n            </select>\n\n            <button @click=\"processContent\">处理</button>\n            <button v-if=\"detectionMethod === 'fewshot'\" @click=\"preTrain\">样本预训练</button>\n          </div>\n\n          <div class=\"pane-body\">\n            <!-- 结果展示区 -->\n            <div class=\"result-container\">\n              <!-- 普通模式 -->\n              <template v-if=\"inputMode !== 'realtime'\">\n                <img v-if=\"processedResult && isImageType\" :src=\"processedResult\" alt=\"处理结果\">\n                <video v-else-if=\"processedResult && isVideoType\" controls :src=\"processedResult\"></video>\n                <div v-else class=\"result-placeholder\">\n                  <p>处理结果将显示在此处</p>\n                </div>\n              </template>\n\n              <!-- 实时模式 -->\n              <canvas v-else ref=\"resultCanvas\"></canvas>\n            </div>\n          </div>\n\n          <!-- 底部操作区 -->\n          <div class=\"pane-footer\">\n            <button v-if=\"inputMode !== 'realtime' && processedResult\" @click=\"showDetailDialog\" class=\"detail-btn\">\n              查看详情\n            </button>\n          </div>\n        </div>\n      </div>\n      <div class=\"pane right\">\n        <!-- 右侧区域内容 -->\n        <div class=\"pane-title\">右侧区域</div>\n        <div class=\"pane-body\"></div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport HeaderComponent from '@/components/header.vue'\nimport { APIUploadData } from '@/api/api'\nimport { serverAddress } from '@/api/config';\n\nexport default {\n  name: 'DetectedView',\n  components: {\n    HeaderComponent\n  },\n  data() {\n    return {\n      // 输入相关\n      inputMode: 'image',\n      currentFile: { name: '', size: 0, url: '' },\n      filesList: [],\n      currentFileIndex: 0,\n      rtspUrl: '',\n      rtspConnected: false,\n\n      // 处理相关\n      preprocessMethod: '',\n      detectionMethod: 'detection',\n      processedResult: null,\n\n      // 弹窗控制\n      detailDialogVisible: false\n    }\n  },\n  computed: {\n    hasFile() {\n      return this.currentFile.url || this.filesList.length > 0\n    },\n    isImageType() {\n      return this.inputMode === 'image' || this.inputMode === 'files'\n    },\n    isVideoType() {\n      return this.inputMode === 'video'\n    }\n  },\n  methods: {\n    goBack() {\n      this.$router.go(-1)\n    },\n    // 添加文件大小格式化方法\n    formatFileSize(bytes) {\n      if (!bytes) return '0 Bytes';\n      const k = 1024;\n      const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n      const i = Math.floor(Math.log(bytes) / Math.log(k));\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    },\n    // 添加文件操作方法\n    prevFile() {\n      if (this.currentFileIndex > 0) {\n        this.currentFileIndex--;\n        this.currentFile = this.filesList[this.currentFileIndex];\n      }\n    },\n    nextFile() {\n      if (this.currentFileIndex < this.filesList.length - 1) {\n        this.currentFileIndex++;\n        this.currentFile = this.filesList[this.currentFileIndex];\n      }\n    },\n    deleteCurrent() {\n      if (this.filesList.length > 0) {\n        this.filesList.splice(this.currentFileIndex, 1);\n        if (this.filesList.length === 0) {\n          this.currentFile = {};\n          this.currentFileIndex = 0;\n        } else {\n          if (this.currentFileIndex >= this.filesList.length) {\n            this.currentFileIndex = this.filesList.length - 1;\n          }\n          this.currentFile = this.filesList[this.currentFileIndex];\n        }\n      }\n    },\n    // 上传文件处理\n    async handleInputAction() {\n      if (this.inputMode === 'realtime') {\n        this.showRtspDialog();\n        return;\n      }\n\n      const input = document.createElement('input');\n      input.type = 'file';\n\n      // 设置文件类型\n      if (this.inputMode === 'image') {\n        input.accept = 'image/*';\n      } else if (this.inputMode === 'video') {\n        input.accept = 'video/*';\n      } else if (this.inputMode === 'files') {\n        input.accept = 'image/*';\n        input.multiple = true;\n      }\n\n      input.onchange = async (e) => {\n        const files = Array.from(e.target.files);\n        if (files.length === 0) return;\n\n        // 重置处理结果\n        this.processedResult = null;\n\n        if (this.inputMode === 'files') {\n          this.filesList = [];\n          for (const file of files) {\n            const url = await this.uploadFile(file);\n            this.filesList.push({\n              name: file.name,\n              size: file.size,\n              url\n            });\n          }\n          this.currentFileIndex = 0;\n          this.currentFile = this.filesList[0] || {};\n        } else {\n          const file = files[0];\n          const url = await this.uploadFile(file);\n          this.currentFile = {\n            name: file.name,\n            size: file.size,\n            url\n          };\n        }\n      };\n\n      input.click();\n    },\n\n    // 文件上传API\n    async uploadFile(file) {\n      const formData = new FormData();\n      formData.append('file', file);\n\n      try {\n        const response = await APIUploadData(formData);\n\n        return serverAddress + response.url; // 后端返回的文件访问路径\n      } catch (error) {\n        console.error('上传失败:', error);\n        return null;\n      }\n    },\n\n    // 重置当前输入\n    resetInput() {\n      if (this.inputMode === 'files') {\n        this.filesList = [];\n        this.currentFileIndex = 0;\n      }\n      this.currentFile = {};\n      this.processedResult = null;\n\n      if (this.inputMode === 'realtime' && this.rtspConnected) {\n        this.toggleRtspConnection();\n      }\n    },\n\n    // RTSP配置弹窗\n    showRtspDialog() {\n      this.$prompt('请输入RTSP流地址', '配置RTSP', {\n        inputValue: this.rtspUrl,\n        inputValidator: (value) => {\n          if (!value) return '请输入RTSP地址';\n          if (!value.startsWith('rtsp://')) return '请输入有效的RTSP地址';\n          return true;\n        },\n        confirmButtonText: '确认连接',\n        cancelButtonText: '取消',\n        showTestButton: true,\n        testButtonText: '测试连接',\n        beforeTest: (action, instance, done) => {\n          // 测试连接逻辑\n          this.testRtspConnection(instance.inputValue)\n            .then(success => {\n              if (success) {\n                this.$message.success('连接测试成功');\n              } else {\n                this.$message.error('连接测试失败');\n              }\n              done();\n            });\n          return false; // 阻止默认关闭\n        }\n      }).then(({ value }) => {\n        this.rtspUrl = value;\n        this.toggleRtspConnection();\n      });\n    },\n\n    // 测试RTSP连接\n    async testRtspConnection(url) {\n      try {\n        const response = await this.$http.post('/rtsp/test', { url });\n        return response.data.success;\n      } catch (error) {\n        return false;\n      }\n    },\n\n    // 切换RTSP连接状态\n    toggleRtspConnection() {\n      if (this.rtspConnected) {\n        this.stopRtspStream();\n      } else if (this.rtspUrl) {\n        this.startRtspStream();\n      }\n      this.rtspConnected = !this.rtspConnected;\n    },\n\n    // 开始RTSP流\n    async startRtspStream() {\n      try {\n        // 创建视频元素\n        const video = this.$refs.rtspPlayer;\n\n        // 获取WebRTC流或使用MSE\n        const streamUrl = await this.getRtspStreamUrl();\n        video.src = streamUrl;\n\n        // 开始播放\n        await video.play();\n      } catch (error) {\n        console.error('RTSP连接失败:', error);\n        this.rtspConnected = false;\n      }\n    },\n\n    // 停止RTSP流\n    stopRtspStream() {\n      const video = this.$refs.rtspPlayer;\n      if (video) {\n        video.pause();\n        video.src = '';\n      }\n    },\n\n    // 处理内容\n    async processContent() {\n      if (!this.hasFile && !this.rtspConnected) {\n        this.$message.warning('请先选择输入内容');\n        return;\n      }\n\n      let processData = {};\n\n      if (this.inputMode === 'files') {\n        processData = {\n          file_urls: this.filesList.map(f => f.url),\n          current_index: this.currentFileIndex\n        };\n      } else if (this.inputMode === 'realtime') {\n        processData = { rtsp_url: this.rtspUrl };\n      } else {\n        processData = { file_url: this.currentFile.url };\n      }\n\n      // 添加处理参数\n      processData.preprocess = this.preprocessMethod;\n      processData.detection = this.detectionMethod;\n\n      try {\n        const response = await this.$http.post('/process', processData);\n        this.processedResult = response.data.result_url;\n\n        // 实时模式需要特殊处理\n        if (this.inputMode === 'realtime') {\n          this.startRealTimeProcessing();\n        }\n      } catch (error) {\n        console.error('处理失败:', error);\n      }\n    },\n\n    // 开始实时处理\n    startRealTimeProcessing() {\n      const canvas = this.$refs.resultCanvas;\n      const ctx = canvas.getContext('2d');\n\n      // 设置Canvas尺寸\n      const video = this.$refs.rtspPlayer;\n      canvas.width = video.videoWidth;\n      canvas.height = video.videoHeight;\n\n      // 处理帧的函数\n      const processFrame = () => {\n        if (!this.rtspConnected) return;\n\n        // 绘制视频帧到Canvas\n        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n\n        // 获取图像数据并发送到后端处理\n        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n        this.sendFrameForProcessing(imageData);\n\n        // 循环处理\n        requestAnimationFrame(processFrame);\n      };\n\n      processFrame();\n    },\n\n    // 发送帧数据到后端处理\n    async sendFrameForProcessing(imageData) {\n      try {\n        // 创建Blob发送\n        const blob = new Blob([imageData.data], { type: 'application/octet-stream' });\n        const formData = new FormData();\n        formData.append('frame', blob);\n        formData.append('width', imageData.width);\n        formData.append('height', imageData.height);\n\n        const response = await this.$http.post('/process/frame', formData);\n        const result = response.data;\n\n        // 在Canvas上绘制处理结果\n        this.drawProcessingResult(result);\n      } catch (error) {\n        console.error('帧处理失败:', error);\n      }\n    },\n\n    // 查看详情弹窗\n    showDetailDialog() {\n      this.detailDialogVisible = true;\n    }\n  }\n\n}\n</script>\n\n<style scoped>\n@import '@/assets/css/variables.css';\n\n.detected {\n  overflow: hidden;\n  background-size: cover;\n  position: relative;\n  box-sizing: border-box;\n  width: 100%;\n  height: 100vh;\n  margin: 0;\n  padding: 0;\n  background-image: url(\"../assets/imgs/backimg.png\");\n  display: flex;\n  flex-direction: column;\n}\n\n/* 主体区域占满 Header 之外的空间 */\n.content {\n  flex: 1;\n  min-height: 0;\n  /* 避免子元素溢出 */\n  display: flex;\n  gap: 12px;\n  padding: 12px;\n  box-sizing: border-box;\n}\n\n/* 左侧列（包含左上/左下） */\n.left {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n  width: 50%;\n  min-width: 360px;\n}\n\n/* 右侧全高区域 */\n.right {\n  flex: 1;\n}\n\n/* 面板基础样式 */\n.pane {\n  background: rgba(255, 255, 255, 0.8);\n  border: 1px solid rgba(0, 0, 0, 0.08);\n  border-radius: 10px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);\n  backdrop-filter: blur(6px);\n  display: flex;\n  flex-direction: column;\n  min-height: 0;\n}\n\n.top-left,\n.bottom-left {\n  flex: 1;\n  /* 左上与左下均分高度 */\n}\n\n.pane-title {\n  font-weight: 600;\n  padding: 10px 12px;\n  border-bottom: 1px solid rgba(0, 0, 0, 0.06);\n  color: #333;\n  display: flex;\n  align-items: center;\n  gap: 10px\n}\n\n.pane-body {\n  flex: 1;\n  min-height: 0;\n  padding: 12px;\n  overflow: auto;\n}\n\n.preview-container,\n.result-container {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.upload-placeholder,\n.result-placeholder {\n  border: 2px dashed #ccc;\n  border-radius: 8px;\n  width: 90%;\n  height: 80%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  color: #666;\n}\n\n.pane-footer {\n  display: flex;\n  justify-content: space-between;\n  padding: 10px 12px;\n  border-top: 1px solid rgba(0, 0, 0, 0.06);\n}\n\n.action-buttons {\n  display: flex;\n  gap: 8px;\n}\n\n.file-info {\n  font-size: 0.9rem;\n  color: #666;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  max-width: 60%;\n}\n\n.detail-btn {\n  margin-left: auto;\n}\n\ncanvas {\n  width: 100%;\n  height: 100%;\n  background: #000;\n}\n\n/* 响应式：窄屏下改为上下排列 */\n@media (max-width: 1200px) {\n  .content {\n    flex-direction: column;\n  }\n\n  .left,\n  .right {\n    width: 100%;\n    min-width: 0;\n  }\n}\n</style>\n"], "mappings": ";;;AAsIA,OAAOA,eAAc,MAAO,yBAAwB;AACpD,SAASC,aAAY,QAAS,WAAU;AACxC,SAASC,aAAY,QAAS,cAAc;AAE5C,eAAe;EACbC,IAAI,EAAE,cAAc;EACpBC,UAAU,EAAE;IACVJ;EACF,CAAC;EACDK,IAAIA,CAAA,EAAG;IACL,OAAO;MACL;MACAC,SAAS,EAAE,OAAO;MAClBC,WAAW,EAAE;QAAEJ,IAAI,EAAE,EAAE;QAAEK,IAAI,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAG,CAAC;MAC3CC,SAAS,EAAE,EAAE;MACbC,gBAAgB,EAAE,CAAC;MACnBC,OAAO,EAAE,EAAE;MACXC,aAAa,EAAE,KAAK;MAEpB;MACAC,gBAAgB,EAAE,EAAE;MACpBC,eAAe,EAAE,WAAW;MAC5BC,eAAe,EAAE,IAAI;MAErB;MACAC,mBAAmB,EAAE;IACvB;EACF,CAAC;EACDC,QAAQ,EAAE;IACRC,OAAOA,CAAA,EAAG;MACR,OAAO,IAAI,CAACZ,WAAW,CAACE,GAAE,IAAK,IAAI,CAACC,SAAS,CAACU,MAAK,GAAI;IACzD,CAAC;IACDC,WAAWA,CAAA,EAAG;MACZ,OAAO,IAAI,CAACf,SAAQ,KAAM,OAAM,IAAK,IAAI,CAACA,SAAQ,KAAM,OAAM;IAChE,CAAC;IACDgB,WAAWA,CAAA,EAAG;MACZ,OAAO,IAAI,CAAChB,SAAQ,KAAM,OAAM;IAClC;EACF,CAAC;EACDiB,OAAO,EAAE;IACPC,MAAMA,CAAA,EAAG;MACP,IAAI,CAACC,OAAO,CAACC,EAAE,CAAC,CAAC,CAAC;IACpB,CAAC;IACD;IACAC,cAAcA,CAACC,KAAK,EAAE;MACpB,IAAI,CAACA,KAAK,EAAE,OAAO,SAAS;MAC5B,MAAMC,CAAA,GAAI,IAAI;MACd,MAAMC,KAAI,GAAI,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MACzC,MAAMC,CAAA,GAAIC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACN,KAAK,IAAII,IAAI,CAACE,GAAG,CAACL,CAAC,CAAC,CAAC;MACnD,OAAOM,UAAU,CAAC,CAACP,KAAI,GAAII,IAAI,CAACI,GAAG,CAACP,CAAC,EAAEE,CAAC,CAAC,EAAEM,OAAO,CAAC,CAAC,CAAC,IAAI,GAAE,GAAIP,KAAK,CAACC,CAAC,CAAC;IACzE,CAAC;IACD;IACAO,QAAQA,CAAA,EAAG;MACT,IAAI,IAAI,CAAC3B,gBAAe,GAAI,CAAC,EAAE;QAC7B,IAAI,CAACA,gBAAgB,EAAE;QACvB,IAAI,CAACJ,WAAU,GAAI,IAAI,CAACG,SAAS,CAAC,IAAI,CAACC,gBAAgB,CAAC;MAC1D;IACF,CAAC;IACD4B,QAAQA,CAAA,EAAG;MACT,IAAI,IAAI,CAAC5B,gBAAe,GAAI,IAAI,CAACD,SAAS,CAACU,MAAK,GAAI,CAAC,EAAE;QACrD,IAAI,CAACT,gBAAgB,EAAE;QACvB,IAAI,CAACJ,WAAU,GAAI,IAAI,CAACG,SAAS,CAAC,IAAI,CAACC,gBAAgB,CAAC;MAC1D;IACF,CAAC;IACD6B,aAAaA,CAAA,EAAG;MACd,IAAI,IAAI,CAAC9B,SAAS,CAACU,MAAK,GAAI,CAAC,EAAE;QAC7B,IAAI,CAACV,SAAS,CAAC+B,MAAM,CAAC,IAAI,CAAC9B,gBAAgB,EAAE,CAAC,CAAC;QAC/C,IAAI,IAAI,CAACD,SAAS,CAACU,MAAK,KAAM,CAAC,EAAE;UAC/B,IAAI,CAACb,WAAU,GAAI,CAAC,CAAC;UACrB,IAAI,CAACI,gBAAe,GAAI,CAAC;QAC3B,OAAO;UACL,IAAI,IAAI,CAACA,gBAAe,IAAK,IAAI,CAACD,SAAS,CAACU,MAAM,EAAE;YAClD,IAAI,CAACT,gBAAe,GAAI,IAAI,CAACD,SAAS,CAACU,MAAK,GAAI,CAAC;UACnD;UACA,IAAI,CAACb,WAAU,GAAI,IAAI,CAACG,SAAS,CAAC,IAAI,CAACC,gBAAgB,CAAC;QAC1D;MACF;IACF,CAAC;IACD;IACA,MAAM+B,iBAAiBA,CAAA,EAAG;MACxB,IAAI,IAAI,CAACpC,SAAQ,KAAM,UAAU,EAAE;QACjC,IAAI,CAACqC,cAAc,CAAC,CAAC;QACrB;MACF;MAEA,MAAMC,KAAI,GAAIC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;MAC7CF,KAAK,CAACG,IAAG,GAAI,MAAM;;MAEnB;MACA,IAAI,IAAI,CAACzC,SAAQ,KAAM,OAAO,EAAE;QAC9BsC,KAAK,CAACI,MAAK,GAAI,SAAS;MAC1B,OAAO,IAAI,IAAI,CAAC1C,SAAQ,KAAM,OAAO,EAAE;QACrCsC,KAAK,CAACI,MAAK,GAAI,SAAS;MAC1B,OAAO,IAAI,IAAI,CAAC1C,SAAQ,KAAM,OAAO,EAAE;QACrCsC,KAAK,CAACI,MAAK,GAAI,SAAS;QACxBJ,KAAK,CAACK,QAAO,GAAI,IAAI;MACvB;MAEAL,KAAK,CAACM,QAAO,GAAI,MAAOC,CAAC,IAAK;QAC5B,MAAMC,KAAI,GAAIC,KAAK,CAACC,IAAI,CAACH,CAAC,CAACI,MAAM,CAACH,KAAK,CAAC;QACxC,IAAIA,KAAK,CAAChC,MAAK,KAAM,CAAC,EAAE;;QAExB;QACA,IAAI,CAACJ,eAAc,GAAI,IAAI;QAE3B,IAAI,IAAI,CAACV,SAAQ,KAAM,OAAO,EAAE;UAC9B,IAAI,CAACI,SAAQ,GAAI,EAAE;UACnB,KAAK,MAAM8C,IAAG,IAAKJ,KAAK,EAAE;YACxB,MAAM3C,GAAE,GAAI,MAAM,IAAI,CAACgD,UAAU,CAACD,IAAI,CAAC;YACvC,IAAI,CAAC9C,SAAS,CAACgD,IAAI,CAAC;cAClBvD,IAAI,EAAEqD,IAAI,CAACrD,IAAI;cACfK,IAAI,EAAEgD,IAAI,CAAChD,IAAI;cACfC;YACF,CAAC,CAAC;UACJ;UACA,IAAI,CAACE,gBAAe,GAAI,CAAC;UACzB,IAAI,CAACJ,WAAU,GAAI,IAAI,CAACG,SAAS,CAAC,CAAC,KAAK,CAAC,CAAC;QAC5C,OAAO;UACL,MAAM8C,IAAG,GAAIJ,KAAK,CAAC,CAAC,CAAC;UACrB,MAAM3C,GAAE,GAAI,MAAM,IAAI,CAACgD,UAAU,CAACD,IAAI,CAAC;UACvC,IAAI,CAACjD,WAAU,GAAI;YACjBJ,IAAI,EAAEqD,IAAI,CAACrD,IAAI;YACfK,IAAI,EAAEgD,IAAI,CAAChD,IAAI;YACfC;UACF,CAAC;QACH;MACF,CAAC;MAEDmC,KAAK,CAACe,KAAK,CAAC,CAAC;IACf,CAAC;IAED;IACA,MAAMF,UAAUA,CAACD,IAAI,EAAE;MACrB,MAAMI,QAAO,GAAI,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEN,IAAI,CAAC;MAE7B,IAAI;QACF,MAAMO,QAAO,GAAI,MAAM9D,aAAa,CAAC2D,QAAQ,CAAC;QAE9C,OAAO1D,aAAY,GAAI6D,QAAQ,CAACtD,GAAG,EAAE;MACvC,EAAE,OAAOuD,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;QAC7B,OAAO,IAAI;MACb;IACF,CAAC;IAED;IACAE,UAAUA,CAAA,EAAG;MACX,IAAI,IAAI,CAAC5D,SAAQ,KAAM,OAAO,EAAE;QAC9B,IAAI,CAACI,SAAQ,GAAI,EAAE;QACnB,IAAI,CAACC,gBAAe,GAAI,CAAC;MAC3B;MACA,IAAI,CAACJ,WAAU,GAAI,CAAC,CAAC;MACrB,IAAI,CAACS,eAAc,GAAI,IAAI;MAE3B,IAAI,IAAI,CAACV,SAAQ,KAAM,UAAS,IAAK,IAAI,CAACO,aAAa,EAAE;QACvD,IAAI,CAACsD,oBAAoB,CAAC,CAAC;MAC7B;IACF,CAAC;IAED;IACAxB,cAAcA,CAAA,EAAG;MACf,IAAI,CAACyB,OAAO,CAAC,YAAY,EAAE,QAAQ,EAAE;QACnCC,UAAU,EAAE,IAAI,CAACzD,OAAO;QACxB0D,cAAc,EAAGC,KAAK,IAAK;UACzB,IAAI,CAACA,KAAK,EAAE,OAAO,WAAW;UAC9B,IAAI,CAACA,KAAK,CAACC,UAAU,CAAC,SAAS,CAAC,EAAE,OAAO,cAAc;UACvD,OAAO,IAAI;QACb,CAAC;QACDC,iBAAiB,EAAE,MAAM;QACzBC,gBAAgB,EAAE,IAAI;QACtBC,cAAc,EAAE,IAAI;QACpBC,cAAc,EAAE,MAAM;QACtBC,UAAU,EAAEA,CAACC,MAAM,EAAEC,QAAQ,EAAEC,IAAI,KAAK;UACtC;UACA,IAAI,CAACC,kBAAkB,CAACF,QAAQ,CAACV,UAAU,EACxCa,IAAI,CAACC,OAAM,IAAK;YACf,IAAIA,OAAO,EAAE;cACX,IAAI,CAACC,QAAQ,CAACD,OAAO,CAAC,QAAQ,CAAC;YACjC,OAAO;cACL,IAAI,CAACC,QAAQ,CAACpB,KAAK,CAAC,QAAQ,CAAC;YAC/B;YACAgB,IAAI,CAAC,CAAC;UACR,CAAC,CAAC;UACJ,OAAO,KAAK,EAAE;QAChB;MACF,CAAC,CAAC,CAACE,IAAI,CAAC,CAAC;QAAEX;MAAM,CAAC,KAAK;QACrB,IAAI,CAAC3D,OAAM,GAAI2D,KAAK;QACpB,IAAI,CAACJ,oBAAoB,CAAC,CAAC;MAC7B,CAAC,CAAC;IACJ,CAAC;IAED;IACA,MAAMc,kBAAkBA,CAACxE,GAAG,EAAE;MAC5B,IAAI;QACF,MAAMsD,QAAO,GAAI,MAAM,IAAI,CAACsB,KAAK,CAACC,IAAI,CAAC,YAAY,EAAE;UAAE7E;QAAI,CAAC,CAAC;QAC7D,OAAOsD,QAAQ,CAAC1D,IAAI,CAAC8E,OAAO;MAC9B,EAAE,OAAOnB,KAAK,EAAE;QACd,OAAO,KAAK;MACd;IACF,CAAC;IAED;IACAG,oBAAoBA,CAAA,EAAG;MACrB,IAAI,IAAI,CAACtD,aAAa,EAAE;QACtB,IAAI,CAAC0E,cAAc,CAAC,CAAC;MACvB,OAAO,IAAI,IAAI,CAAC3E,OAAO,EAAE;QACvB,IAAI,CAAC4E,eAAe,CAAC,CAAC;MACxB;MACA,IAAI,CAAC3E,aAAY,GAAI,CAAC,IAAI,CAACA,aAAa;IAC1C,CAAC;IAED;IACA,MAAM2E,eAAeA,CAAA,EAAG;MACtB,IAAI;QACF;QACA,MAAMC,KAAI,GAAI,IAAI,CAACC,KAAK,CAACC,UAAU;;QAEnC;QACA,MAAMC,SAAQ,GAAI,MAAM,IAAI,CAACC,gBAAgB,CAAC,CAAC;QAC/CJ,KAAK,CAACK,GAAE,GAAIF,SAAS;;QAErB;QACA,MAAMH,KAAK,CAACM,IAAI,CAAC,CAAC;MACpB,EAAE,OAAO/B,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC,IAAI,CAACnD,aAAY,GAAI,KAAK;MAC5B;IACF,CAAC;IAED;IACA0E,cAAcA,CAAA,EAAG;MACf,MAAME,KAAI,GAAI,IAAI,CAACC,KAAK,CAACC,UAAU;MACnC,IAAIF,KAAK,EAAE;QACTA,KAAK,CAACO,KAAK,CAAC,CAAC;QACbP,KAAK,CAACK,GAAE,GAAI,EAAE;MAChB;IACF,CAAC;IAED;IACA,MAAMG,cAAcA,CAAA,EAAG;MACrB,IAAI,CAAC,IAAI,CAAC9E,OAAM,IAAK,CAAC,IAAI,CAACN,aAAa,EAAE;QACxC,IAAI,CAACuE,QAAQ,CAACc,OAAO,CAAC,UAAU,CAAC;QACjC;MACF;MAEA,IAAIC,WAAU,GAAI,CAAC,CAAC;MAEpB,IAAI,IAAI,CAAC7F,SAAQ,KAAM,OAAO,EAAE;QAC9B6F,WAAU,GAAI;UACZC,SAAS,EAAE,IAAI,CAAC1F,SAAS,CAAC2F,GAAG,CAACC,CAAA,IAAKA,CAAC,CAAC7F,GAAG,CAAC;UACzC8F,aAAa,EAAE,IAAI,CAAC5F;QACtB,CAAC;MACH,OAAO,IAAI,IAAI,CAACL,SAAQ,KAAM,UAAU,EAAE;QACxC6F,WAAU,GAAI;UAAEK,QAAQ,EAAE,IAAI,CAAC5F;QAAQ,CAAC;MAC1C,OAAO;QACLuF,WAAU,GAAI;UAAEM,QAAQ,EAAE,IAAI,CAAClG,WAAW,CAACE;QAAI,CAAC;MAClD;;MAEA;MACA0F,WAAW,CAACO,UAAS,GAAI,IAAI,CAAC5F,gBAAgB;MAC9CqF,WAAW,CAACQ,SAAQ,GAAI,IAAI,CAAC5F,eAAe;MAE5C,IAAI;QACF,MAAMgD,QAAO,GAAI,MAAM,IAAI,CAACsB,KAAK,CAACC,IAAI,CAAC,UAAU,EAAEa,WAAW,CAAC;QAC/D,IAAI,CAACnF,eAAc,GAAI+C,QAAQ,CAAC1D,IAAI,CAACuG,UAAU;;QAE/C;QACA,IAAI,IAAI,CAACtG,SAAQ,KAAM,UAAU,EAAE;UACjC,IAAI,CAACuG,uBAAuB,CAAC,CAAC;QAChC;MACF,EAAE,OAAO7C,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC/B;IACF,CAAC;IAED;IACA6C,uBAAuBA,CAAA,EAAG;MACxB,MAAMC,MAAK,GAAI,IAAI,CAACpB,KAAK,CAACqB,YAAY;MACtC,MAAMC,GAAE,GAAIF,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC;;MAEnC;MACA,MAAMxB,KAAI,GAAI,IAAI,CAACC,KAAK,CAACC,UAAU;MACnCmB,MAAM,CAACI,KAAI,GAAIzB,KAAK,CAAC0B,UAAU;MAC/BL,MAAM,CAACM,MAAK,GAAI3B,KAAK,CAAC4B,WAAW;;MAEjC;MACA,MAAMC,YAAW,GAAIA,CAAA,KAAM;QACzB,IAAI,CAAC,IAAI,CAACzG,aAAa,EAAE;;QAEzB;QACAmG,GAAG,CAACO,SAAS,CAAC9B,KAAK,EAAE,CAAC,EAAE,CAAC,EAAEqB,MAAM,CAACI,KAAK,EAAEJ,MAAM,CAACM,MAAM,CAAC;;QAEvD;QACA,MAAMI,SAAQ,GAAIR,GAAG,CAACS,YAAY,CAAC,CAAC,EAAE,CAAC,EAAEX,MAAM,CAACI,KAAK,EAAEJ,MAAM,CAACM,MAAM,CAAC;QACrE,IAAI,CAACM,sBAAsB,CAACF,SAAS,CAAC;;QAEtC;QACAG,qBAAqB,CAACL,YAAY,CAAC;MACrC,CAAC;MAEDA,YAAY,CAAC,CAAC;IAChB,CAAC;IAED;IACA,MAAMI,sBAAsBA,CAACF,SAAS,EAAE;MACtC,IAAI;QACF;QACA,MAAMI,IAAG,GAAI,IAAIC,IAAI,CAAC,CAACL,SAAS,CAACnH,IAAI,CAAC,EAAE;UAAE0C,IAAI,EAAE;QAA2B,CAAC,CAAC;QAC7E,MAAMa,QAAO,GAAI,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAE8D,IAAI,CAAC;QAC9BhE,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAE0D,SAAS,CAACN,KAAK,CAAC;QACzCtD,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAE0D,SAAS,CAACJ,MAAM,CAAC;QAE3C,MAAMrD,QAAO,GAAI,MAAM,IAAI,CAACsB,KAAK,CAACC,IAAI,CAAC,gBAAgB,EAAE1B,QAAQ,CAAC;QAClE,MAAMkE,MAAK,GAAI/D,QAAQ,CAAC1D,IAAI;;QAE5B;QACA,IAAI,CAAC0H,oBAAoB,CAACD,MAAM,CAAC;MACnC,EAAE,OAAO9D,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;MAChC;IACF,CAAC;IAED;IACAgE,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAAC/G,mBAAkB,GAAI,IAAI;IACjC;EACF;AAEF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}