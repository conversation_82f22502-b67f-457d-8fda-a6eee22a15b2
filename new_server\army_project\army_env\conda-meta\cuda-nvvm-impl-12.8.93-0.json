{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.8,<12.9.0a0"], "extracted_package_dir": "", "files": ["Library/nvvm/include/nvvm.h", "Library/nvvm/lib/x64/nvvm.lib"], "fn": "cuda-nvvm-impl-12.8.93-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "", "type": 1}, "md5": "28593a4d901c393191f28367acca8e61", "name": "cuda-nvvm-impl", "package_tarball_full_path": "", "paths_data": {"paths": [{"_path": "Library/nvvm/include/nvvm.h", "path_type": "hardlink", "sha256": "34486460bd57eef91adbea1f6e5ba1f49163ac397981bba5034a320c210d987d", "sha256_in_prefix": "34486460bd57eef91adbea1f6e5ba1f49163ac397981bba5034a320c210d987d", "size_in_bytes": 12060}, {"_path": "Library/nvvm/lib/x64/nvvm.lib", "path_type": "hardlink", "sha256": "2a7a7f330906b123aa9328cfcbdf29ec6d09701cf70b3ba1dd36697ed78a74c1", "sha256_in_prefix": "2a7a7f330906b123aa9328cfcbdf29ec6d09701cf70b3ba1dd36697ed78a74c1", "size_in_bytes": 4922}], "paths_version": 1}, "requested_spec": "None", "sha256": "d2f3ccad305f703139d970a9baff2ff5554f82c31cb004553d37fcae205fe41e", "size": 20802, "subdir": "win-64", "timestamp": 1740205166000, "url": "https://conda.anaconda.org/nvidia/win-64/cuda-nvvm-impl-12.8.93-0.conda", "version": "12.8.93"}