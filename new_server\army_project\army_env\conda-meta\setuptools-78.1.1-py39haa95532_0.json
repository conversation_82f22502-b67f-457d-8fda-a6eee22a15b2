{"build": "py39haa95532_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["python >=3.9,<3.10.0a0"], "extracted_package_dir": "", "files": ["Lib/site-packages/_distutils_hack/__init__.py", "Lib/site-packages/_distutils_hack/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/_distutils_hack/__pycache__/override.cpython-39.pyc", "Lib/site-packages/_distutils_hack/override.py", "Lib/site-packages/distutils-precedence.pth", "Lib/site-packages/pkg_resources/__init__.py", "Lib/site-packages/pkg_resources/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/pkg_resources/api_tests.txt", "Lib/site-packages/pkg_resources/py.typed", "Lib/site-packages/pkg_resources/tests/__init__.py", "Lib/site-packages/pkg_resources/tests/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/pkg_resources/tests/__pycache__/test_find_distributions.cpython-39.pyc", "Lib/site-packages/pkg_resources/tests/__pycache__/test_integration_zope_interface.cpython-39.pyc", "Lib/site-packages/pkg_resources/tests/__pycache__/test_markers.cpython-39.pyc", "Lib/site-packages/pkg_resources/tests/__pycache__/test_pkg_resources.cpython-39.pyc", "Lib/site-packages/pkg_resources/tests/__pycache__/test_resources.cpython-39.pyc", "Lib/site-packages/pkg_resources/tests/__pycache__/test_working_set.cpython-39.pyc", "Lib/site-packages/pkg_resources/tests/data/my-test-package-source/__pycache__/setup.cpython-39.pyc", "Lib/site-packages/pkg_resources/tests/data/my-test-package-source/setup.cfg", "Lib/site-packages/pkg_resources/tests/data/my-test-package-source/setup.py", "Lib/site-packages/pkg_resources/tests/data/my-test-package-zip/my-test-package.zip", "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/PKG-INFO", "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/SOURCES.txt", "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/dependency_links.txt", "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/top_level.txt", "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/zip-safe", "Lib/site-packages/pkg_resources/tests/data/my-test-package_zipped-egg/my_test_package-1.0-py3.7.egg", "Lib/site-packages/pkg_resources/tests/test_find_distributions.py", "Lib/site-packages/pkg_resources/tests/test_integration_zope_interface.py", "Lib/site-packages/pkg_resources/tests/test_markers.py", "Lib/site-packages/pkg_resources/tests/test_pkg_resources.py", "Lib/site-packages/pkg_resources/tests/test_resources.py", "Lib/site-packages/pkg_resources/tests/test_working_set.py", "Lib/site-packages/setuptools-78.1.1-py3.9.egg-info/PKG-INFO", "Lib/site-packages/setuptools-78.1.1-py3.9.egg-info/SOURCES.txt", "Lib/site-packages/setuptools-78.1.1-py3.9.egg-info/dependency_links.txt", "Lib/site-packages/setuptools-78.1.1-py3.9.egg-info/entry_points.txt", "Lib/site-packages/setuptools-78.1.1-py3.9.egg-info/requires.txt", "Lib/site-packages/setuptools-78.1.1-py3.9.egg-info/top_level.txt", "Lib/site-packages/setuptools/__init__.py", "Lib/site-packages/setuptools/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/setuptools/__pycache__/_core_metadata.cpython-39.pyc", "Lib/site-packages/setuptools/__pycache__/_entry_points.cpython-39.pyc", "Lib/site-packages/setuptools/__pycache__/_imp.cpython-39.pyc", "Lib/site-packages/setuptools/__pycache__/_importlib.cpython-39.pyc", "Lib/site-packages/setuptools/__pycache__/_itertools.cpython-39.pyc", "Lib/site-packages/setuptools/__pycache__/_normalization.cpython-39.pyc", "Lib/site-packages/setuptools/__pycache__/_path.cpython-39.pyc", "Lib/site-packages/setuptools/__pycache__/_reqs.cpython-39.pyc", "Lib/site-packages/setuptools/__pycache__/_shutil.cpython-39.pyc", "Lib/site-packages/setuptools/__pycache__/_static.cpython-39.pyc", "Lib/site-packages/setuptools/__pycache__/archive_util.cpython-39.pyc", "Lib/site-packages/setuptools/__pycache__/build_meta.cpython-39.pyc", "Lib/site-packages/setuptools/__pycache__/depends.cpython-39.pyc", "Lib/site-packages/setuptools/__pycache__/discovery.cpython-39.pyc", "Lib/site-packages/setuptools/__pycache__/dist.cpython-39.pyc", "Lib/site-packages/setuptools/__pycache__/errors.cpython-39.pyc", "Lib/site-packages/setuptools/__pycache__/extension.cpython-39.pyc", "Lib/site-packages/setuptools/__pycache__/glob.cpython-39.pyc", "Lib/site-packages/setuptools/__pycache__/installer.cpython-39.pyc", "Lib/site-packages/setuptools/__pycache__/launch.cpython-39.pyc", "Lib/site-packages/setuptools/__pycache__/logging.cpython-39.pyc", "Lib/site-packages/setuptools/__pycache__/modified.cpython-39.pyc", "Lib/site-packages/setuptools/__pycache__/monkey.cpython-39.pyc", "Lib/site-packages/setuptools/__pycache__/msvc.cpython-39.pyc", "Lib/site-packages/setuptools/__pycache__/namespaces.cpython-39.pyc", "Lib/site-packages/setuptools/__pycache__/package_index.cpython-39.pyc", "Lib/site-packages/setuptools/__pycache__/sandbox.cpython-39.pyc", "Lib/site-packages/setuptools/__pycache__/unicode_utils.cpython-39.pyc", "Lib/site-packages/setuptools/__pycache__/version.cpython-39.pyc", "Lib/site-packages/setuptools/__pycache__/warnings.cpython-39.pyc", "Lib/site-packages/setuptools/__pycache__/wheel.cpython-39.pyc", "Lib/site-packages/setuptools/__pycache__/windows_support.cpython-39.pyc", "Lib/site-packages/setuptools/_core_metadata.py", "Lib/site-packages/setuptools/_distutils/__init__.py", "Lib/site-packages/setuptools/_distutils/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/_log.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/_macos_compat.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/_modified.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/_msvccompiler.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/archive_util.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/ccompiler.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/cmd.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/core.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/cygwinccompiler.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/debug.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/dep_util.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/dir_util.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/dist.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/errors.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/extension.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/fancy_getopt.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/file_util.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/filelist.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/log.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/spawn.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/sysconfig.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/text_file.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/unixccompiler.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/util.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/version.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/versionpredicate.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/zosccompiler.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/_log.py", "Lib/site-packages/setuptools/_distutils/_macos_compat.py", "Lib/site-packages/setuptools/_distutils/_modified.py", "Lib/site-packages/setuptools/_distutils/_msvccompiler.py", "Lib/site-packages/setuptools/_distutils/archive_util.py", "Lib/site-packages/setuptools/_distutils/ccompiler.py", "Lib/site-packages/setuptools/_distutils/cmd.py", "Lib/site-packages/setuptools/_distutils/command/__init__.py", "Lib/site-packages/setuptools/_distutils/command/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/_framework_compat.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/bdist.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/bdist_dumb.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/bdist_rpm.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/build.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_clib.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_ext.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_py.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_scripts.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/check.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/clean.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/config.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/install.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_data.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_egg_info.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_headers.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_lib.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_scripts.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/sdist.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/command/_framework_compat.py", "Lib/site-packages/setuptools/_distutils/command/bdist.py", "Lib/site-packages/setuptools/_distutils/command/bdist_dumb.py", "Lib/site-packages/setuptools/_distutils/command/bdist_rpm.py", "Lib/site-packages/setuptools/_distutils/command/build.py", "Lib/site-packages/setuptools/_distutils/command/build_clib.py", "Lib/site-packages/setuptools/_distutils/command/build_ext.py", "Lib/site-packages/setuptools/_distutils/command/build_py.py", "Lib/site-packages/setuptools/_distutils/command/build_scripts.py", "Lib/site-packages/setuptools/_distutils/command/check.py", "Lib/site-packages/setuptools/_distutils/command/clean.py", "Lib/site-packages/setuptools/_distutils/command/config.py", "Lib/site-packages/setuptools/_distutils/command/install.py", "Lib/site-packages/setuptools/_distutils/command/install_data.py", "Lib/site-packages/setuptools/_distutils/command/install_egg_info.py", "Lib/site-packages/setuptools/_distutils/command/install_headers.py", "Lib/site-packages/setuptools/_distutils/command/install_lib.py", "Lib/site-packages/setuptools/_distutils/command/install_scripts.py", "Lib/site-packages/setuptools/_distutils/command/sdist.py", "Lib/site-packages/setuptools/_distutils/compat/__init__.py", "Lib/site-packages/setuptools/_distutils/compat/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/compat/__pycache__/numpy.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/compat/__pycache__/py39.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/compat/numpy.py", "Lib/site-packages/setuptools/_distutils/compat/py39.py", "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/base.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/cygwin.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/errors.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/msvc.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/unix.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/zos.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/base.py", "Lib/site-packages/setuptools/_distutils/compilers/C/cygwin.py", "Lib/site-packages/setuptools/_distutils/compilers/C/errors.py", "Lib/site-packages/setuptools/_distutils/compilers/C/msvc.py", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_base.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_cygwin.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_mingw.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_msvc.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_unix.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_base.py", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_cygwin.py", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_mingw.py", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_msvc.py", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_unix.py", "Lib/site-packages/setuptools/_distutils/compilers/C/unix.py", "Lib/site-packages/setuptools/_distutils/compilers/C/zos.py", "Lib/site-packages/setuptools/_distutils/core.py", "Lib/site-packages/setuptools/_distutils/cygwinccompiler.py", "Lib/site-packages/setuptools/_distutils/debug.py", "Lib/site-packages/setuptools/_distutils/dep_util.py", "Lib/site-packages/setuptools/_distutils/dir_util.py", "Lib/site-packages/setuptools/_distutils/dist.py", "Lib/site-packages/setuptools/_distutils/errors.py", "Lib/site-packages/setuptools/_distutils/extension.py", "Lib/site-packages/setuptools/_distutils/fancy_getopt.py", "Lib/site-packages/setuptools/_distutils/file_util.py", "Lib/site-packages/setuptools/_distutils/filelist.py", "Lib/site-packages/setuptools/_distutils/log.py", "Lib/site-packages/setuptools/_distutils/spawn.py", "Lib/site-packages/setuptools/_distutils/sysconfig.py", "Lib/site-packages/setuptools/_distutils/tests/__init__.py", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/support.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_archive_util.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist_dumb.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist_rpm.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_clib.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_ext.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_py.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_scripts.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_check.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_clean.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_cmd.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_config_cmd.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_core.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_dir_util.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_dist.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_extension.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_file_util.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_filelist.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_data.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_headers.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_lib.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_scripts.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_log.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_modified.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_sdist.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_spawn.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_sysconfig.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_text_file.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_util.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_version.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_versionpredicate.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/unix_compat.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/tests/compat/__init__.py", "Lib/site-packages/setuptools/_distutils/tests/compat/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/tests/compat/__pycache__/py39.cpython-39.pyc", "Lib/site-packages/setuptools/_distutils/tests/compat/py39.py", "Lib/site-packages/setuptools/_distutils/tests/support.py", "Lib/site-packages/setuptools/_distutils/tests/test_archive_util.py", "Lib/site-packages/setuptools/_distutils/tests/test_bdist.py", "Lib/site-packages/setuptools/_distutils/tests/test_bdist_dumb.py", "Lib/site-packages/setuptools/_distutils/tests/test_bdist_rpm.py", "Lib/site-packages/setuptools/_distutils/tests/test_build.py", "Lib/site-packages/setuptools/_distutils/tests/test_build_clib.py", "Lib/site-packages/setuptools/_distutils/tests/test_build_ext.py", "Lib/site-packages/setuptools/_distutils/tests/test_build_py.py", "Lib/site-packages/setuptools/_distutils/tests/test_build_scripts.py", "Lib/site-packages/setuptools/_distutils/tests/test_check.py", "Lib/site-packages/setuptools/_distutils/tests/test_clean.py", "Lib/site-packages/setuptools/_distutils/tests/test_cmd.py", "Lib/site-packages/setuptools/_distutils/tests/test_config_cmd.py", "Lib/site-packages/setuptools/_distutils/tests/test_core.py", "Lib/site-packages/setuptools/_distutils/tests/test_dir_util.py", "Lib/site-packages/setuptools/_distutils/tests/test_dist.py", "Lib/site-packages/setuptools/_distutils/tests/test_extension.py", "Lib/site-packages/setuptools/_distutils/tests/test_file_util.py", "Lib/site-packages/setuptools/_distutils/tests/test_filelist.py", "Lib/site-packages/setuptools/_distutils/tests/test_install.py", "Lib/site-packages/setuptools/_distutils/tests/test_install_data.py", "Lib/site-packages/setuptools/_distutils/tests/test_install_headers.py", "Lib/site-packages/setuptools/_distutils/tests/test_install_lib.py", "Lib/site-packages/setuptools/_distutils/tests/test_install_scripts.py", "Lib/site-packages/setuptools/_distutils/tests/test_log.py", "Lib/site-packages/setuptools/_distutils/tests/test_modified.py", "Lib/site-packages/setuptools/_distutils/tests/test_sdist.py", "Lib/site-packages/setuptools/_distutils/tests/test_spawn.py", "Lib/site-packages/setuptools/_distutils/tests/test_sysconfig.py", "Lib/site-packages/setuptools/_distutils/tests/test_text_file.py", "Lib/site-packages/setuptools/_distutils/tests/test_util.py", "Lib/site-packages/setuptools/_distutils/tests/test_version.py", "Lib/site-packages/setuptools/_distutils/tests/test_versionpredicate.py", "Lib/site-packages/setuptools/_distutils/tests/unix_compat.py", "Lib/site-packages/setuptools/_distutils/text_file.py", "Lib/site-packages/setuptools/_distutils/unixccompiler.py", "Lib/site-packages/setuptools/_distutils/util.py", "Lib/site-packages/setuptools/_distutils/version.py", "Lib/site-packages/setuptools/_distutils/versionpredicate.py", "Lib/site-packages/setuptools/_distutils/zosccompiler.py", "Lib/site-packages/setuptools/_entry_points.py", "Lib/site-packages/setuptools/_imp.py", "Lib/site-packages/setuptools/_importlib.py", "Lib/site-packages/setuptools/_itertools.py", "Lib/site-packages/setuptools/_normalization.py", "Lib/site-packages/setuptools/_path.py", "Lib/site-packages/setuptools/_reqs.py", "Lib/site-packages/setuptools/_shutil.py", "Lib/site-packages/setuptools/_static.py", "Lib/site-packages/setuptools/_vendor/__pycache__/typing_extensions.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/autocommand/__init__.py", "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/autoasync.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/autocommand.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/automain.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/autoparse.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/errors.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/autocommand/autoasync.py", "Lib/site-packages/setuptools/_vendor/autocommand/autocommand.py", "Lib/site-packages/setuptools/_vendor/autocommand/automain.py", "Lib/site-packages/setuptools/_vendor/autocommand/autoparse.py", "Lib/site-packages/setuptools/_vendor/autocommand/errors.py", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/backports/__init__.py", "Lib/site-packages/setuptools/_vendor/backports/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/backports/tarfile/__init__.py", "Lib/site-packages/setuptools/_vendor/backports/tarfile/__main__.py", "Lib/site-packages/setuptools/_vendor/backports/tarfile/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/backports/tarfile/__pycache__/__main__.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/__init__.py", "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/__pycache__/py38.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/py38.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__init__.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_adapters.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_collections.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_compat.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_functools.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_itertools.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_meta.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_text.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/diagnose.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_adapters.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_collections.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_compat.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_functools.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_itertools.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_meta.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_text.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__init__.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/py311.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/py39.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/py311.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/py39.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/diagnose.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/py.typed", "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/inflect/__init__.py", "Lib/site-packages/setuptools/_vendor/inflect/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/inflect/compat/__init__.py", "Lib/site-packages/setuptools/_vendor/inflect/compat/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/inflect/compat/__pycache__/py38.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/inflect/compat/py38.py", "Lib/site-packages/setuptools/_vendor/inflect/py.typed", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/jaraco/__pycache__/context.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/collections/__init__.py", "Lib/site-packages/setuptools/_vendor/jaraco/collections/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/collections/py.typed", "Lib/site-packages/setuptools/_vendor/jaraco/context.py", "Lib/site-packages/setuptools/_vendor/jaraco/functools/__init__.py", "Lib/site-packages/setuptools/_vendor/jaraco/functools/__init__.pyi", "Lib/site-packages/setuptools/_vendor/jaraco/functools/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/functools/py.typed", "Lib/site-packages/setuptools/_vendor/jaraco/text/Lorem ipsum.txt", "Lib/site-packages/setuptools/_vendor/jaraco/text/__init__.py", "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/layouts.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/show-newlines.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/strip-prefix.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/to-dvorak.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/to-qwerty.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/text/layouts.py", "Lib/site-packages/setuptools/_vendor/jaraco/text/show-newlines.py", "Lib/site-packages/setuptools/_vendor/jaraco/text/strip-prefix.py", "Lib/site-packages/setuptools/_vendor/jaraco/text/to-dvorak.py", "Lib/site-packages/setuptools/_vendor/jaraco/text/to-qwerty.py", "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/more_itertools/__init__.py", "Lib/site-packages/setuptools/_vendor/more_itertools/__init__.pyi", "Lib/site-packages/setuptools/_vendor/more_itertools/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/more_itertools/__pycache__/more.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/more_itertools/__pycache__/recipes.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/more_itertools/more.py", "Lib/site-packages/setuptools/_vendor/more_itertools/more.pyi", "Lib/site-packages/setuptools/_vendor/more_itertools/py.typed", "Lib/site-packages/setuptools/_vendor/more_itertools/recipes.py", "Lib/site-packages/setuptools/_vendor/more_itertools/recipes.pyi", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE.APACHE", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE.BSD", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/packaging/__init__.py", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_elffile.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_manylinux.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_musllinux.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_parser.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_structures.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_tokenizer.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/markers.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/metadata.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/requirements.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/specifiers.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/tags.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/utils.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/version.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/packaging/_elffile.py", "Lib/site-packages/setuptools/_vendor/packaging/_manylinux.py", "Lib/site-packages/setuptools/_vendor/packaging/_musllinux.py", "Lib/site-packages/setuptools/_vendor/packaging/_parser.py", "Lib/site-packages/setuptools/_vendor/packaging/_structures.py", "Lib/site-packages/setuptools/_vendor/packaging/_tokenizer.py", "Lib/site-packages/setuptools/_vendor/packaging/licenses/__init__.py", "Lib/site-packages/setuptools/_vendor/packaging/licenses/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/packaging/licenses/__pycache__/_spdx.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/packaging/licenses/_spdx.py", "Lib/site-packages/setuptools/_vendor/packaging/markers.py", "Lib/site-packages/setuptools/_vendor/packaging/metadata.py", "Lib/site-packages/setuptools/_vendor/packaging/py.typed", "Lib/site-packages/setuptools/_vendor/packaging/requirements.py", "Lib/site-packages/setuptools/_vendor/packaging/specifiers.py", "Lib/site-packages/setuptools/_vendor/packaging/tags.py", "Lib/site-packages/setuptools/_vendor/packaging/utils.py", "Lib/site-packages/setuptools/_vendor/packaging/version.py", "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/licenses/LICENSE", "Lib/site-packages/setuptools/_vendor/platformdirs/__init__.py", "Lib/site-packages/setuptools/_vendor/platformdirs/__main__.py", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/__main__.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/android.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/api.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/macos.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/unix.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/version.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/windows.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/android.py", "Lib/site-packages/setuptools/_vendor/platformdirs/api.py", "Lib/site-packages/setuptools/_vendor/platformdirs/macos.py", "Lib/site-packages/setuptools/_vendor/platformdirs/py.typed", "Lib/site-packages/setuptools/_vendor/platformdirs/unix.py", "Lib/site-packages/setuptools/_vendor/platformdirs/version.py", "Lib/site-packages/setuptools/_vendor/platformdirs/windows.py", "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/tomli/__init__.py", "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/_parser.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/_re.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/_types.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/tomli/_parser.py", "Lib/site-packages/setuptools/_vendor/tomli/_re.py", "Lib/site-packages/setuptools/_vendor/tomli/_types.py", "Lib/site-packages/setuptools/_vendor/tomli/py.typed", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/entry_points.txt", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/typeguard/__init__.py", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_checkers.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_config.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_decorators.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_exceptions.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_functions.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_importhook.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_memo.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_pytest_plugin.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_suppression.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_transformer.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_union_transformer.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_utils.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/_checkers.py", "Lib/site-packages/setuptools/_vendor/typeguard/_config.py", "Lib/site-packages/setuptools/_vendor/typeguard/_decorators.py", "Lib/site-packages/setuptools/_vendor/typeguard/_exceptions.py", "Lib/site-packages/setuptools/_vendor/typeguard/_functions.py", "Lib/site-packages/setuptools/_vendor/typeguard/_importhook.py", "Lib/site-packages/setuptools/_vendor/typeguard/_memo.py", "Lib/site-packages/setuptools/_vendor/typeguard/_pytest_plugin.py", "Lib/site-packages/setuptools/_vendor/typeguard/_suppression.py", "Lib/site-packages/setuptools/_vendor/typeguard/_transformer.py", "Lib/site-packages/setuptools/_vendor/typeguard/_union_transformer.py", "Lib/site-packages/setuptools/_vendor/typeguard/_utils.py", "Lib/site-packages/setuptools/_vendor/typeguard/py.typed", "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/typing_extensions.py", "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/LICENSE.txt", "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/entry_points.txt", "Lib/site-packages/setuptools/_vendor/wheel/__init__.py", "Lib/site-packages/setuptools/_vendor/wheel/__main__.py", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/__main__.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/_bdist_wheel.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/_setuptools_logging.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/bdist_wheel.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/macosx_libfile.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/metadata.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/util.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/wheelfile.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/wheel/_bdist_wheel.py", "Lib/site-packages/setuptools/_vendor/wheel/_setuptools_logging.py", "Lib/site-packages/setuptools/_vendor/wheel/bdist_wheel.py", "Lib/site-packages/setuptools/_vendor/wheel/cli/__init__.py", "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/convert.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/pack.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/tags.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/unpack.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/wheel/cli/convert.py", "Lib/site-packages/setuptools/_vendor/wheel/cli/pack.py", "Lib/site-packages/setuptools/_vendor/wheel/cli/tags.py", "Lib/site-packages/setuptools/_vendor/wheel/cli/unpack.py", "Lib/site-packages/setuptools/_vendor/wheel/macosx_libfile.py", "Lib/site-packages/setuptools/_vendor/wheel/metadata.py", "Lib/site-packages/setuptools/_vendor/wheel/util.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/__init__.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/LICENSE", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/LICENSE.APACHE", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/LICENSE.BSD", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__init__.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_elffile.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_manylinux.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_musllinux.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_parser.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_structures.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_tokenizer.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/markers.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/requirements.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/specifiers.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/tags.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/utils.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/version.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_elffile.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_manylinux.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_musllinux.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_parser.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_structures.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_tokenizer.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/markers.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/requirements.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/specifiers.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/tags.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/utils.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/version.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/vendor.txt", "Lib/site-packages/setuptools/_vendor/wheel/wheelfile.py", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/zipp/__init__.py", "Lib/site-packages/setuptools/_vendor/zipp/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/zipp/__pycache__/glob.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/zipp/compat/__init__.py", "Lib/site-packages/setuptools/_vendor/zipp/compat/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/zipp/compat/__pycache__/py310.cpython-39.pyc", "Lib/site-packages/setuptools/_vendor/zipp/compat/py310.py", "Lib/site-packages/setuptools/_vendor/zipp/glob.py", "Lib/site-packages/setuptools/archive_util.py", "Lib/site-packages/setuptools/build_meta.py", "Lib/site-packages/setuptools/cli-32.exe", "Lib/site-packages/setuptools/cli-64.exe", "Lib/site-packages/setuptools/cli-arm64.exe", "Lib/site-packages/setuptools/cli.exe", "Lib/site-packages/setuptools/command/__init__.py", "Lib/site-packages/setuptools/command/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/setuptools/command/__pycache__/_requirestxt.cpython-39.pyc", "Lib/site-packages/setuptools/command/__pycache__/alias.cpython-39.pyc", "Lib/site-packages/setuptools/command/__pycache__/bdist_egg.cpython-39.pyc", "Lib/site-packages/setuptools/command/__pycache__/bdist_rpm.cpython-39.pyc", "Lib/site-packages/setuptools/command/__pycache__/bdist_wheel.cpython-39.pyc", "Lib/site-packages/setuptools/command/__pycache__/build.cpython-39.pyc", "Lib/site-packages/setuptools/command/__pycache__/build_clib.cpython-39.pyc", "Lib/site-packages/setuptools/command/__pycache__/build_ext.cpython-39.pyc", "Lib/site-packages/setuptools/command/__pycache__/build_py.cpython-39.pyc", "Lib/site-packages/setuptools/command/__pycache__/develop.cpython-39.pyc", "Lib/site-packages/setuptools/command/__pycache__/dist_info.cpython-39.pyc", "Lib/site-packages/setuptools/command/__pycache__/easy_install.cpython-39.pyc", "Lib/site-packages/setuptools/command/__pycache__/editable_wheel.cpython-39.pyc", "Lib/site-packages/setuptools/command/__pycache__/egg_info.cpython-39.pyc", "Lib/site-packages/setuptools/command/__pycache__/install.cpython-39.pyc", "Lib/site-packages/setuptools/command/__pycache__/install_egg_info.cpython-39.pyc", "Lib/site-packages/setuptools/command/__pycache__/install_lib.cpython-39.pyc", "Lib/site-packages/setuptools/command/__pycache__/install_scripts.cpython-39.pyc", "Lib/site-packages/setuptools/command/__pycache__/rotate.cpython-39.pyc", "Lib/site-packages/setuptools/command/__pycache__/saveopts.cpython-39.pyc", "Lib/site-packages/setuptools/command/__pycache__/sdist.cpython-39.pyc", "Lib/site-packages/setuptools/command/__pycache__/setopt.cpython-39.pyc", "Lib/site-packages/setuptools/command/__pycache__/test.cpython-39.pyc", "Lib/site-packages/setuptools/command/_requirestxt.py", "Lib/site-packages/setuptools/command/alias.py", "Lib/site-packages/setuptools/command/bdist_egg.py", "Lib/site-packages/setuptools/command/bdist_rpm.py", "Lib/site-packages/setuptools/command/bdist_wheel.py", "Lib/site-packages/setuptools/command/build.py", "Lib/site-packages/setuptools/command/build_clib.py", "Lib/site-packages/setuptools/command/build_ext.py", "Lib/site-packages/setuptools/command/build_py.py", "Lib/site-packages/setuptools/command/develop.py", "Lib/site-packages/setuptools/command/dist_info.py", "Lib/site-packages/setuptools/command/easy_install.py", "Lib/site-packages/setuptools/command/editable_wheel.py", "Lib/site-packages/setuptools/command/egg_info.py", "Lib/site-packages/setuptools/command/install.py", "Lib/site-packages/setuptools/command/install_egg_info.py", "Lib/site-packages/setuptools/command/install_lib.py", "Lib/site-packages/setuptools/command/install_scripts.py", "Lib/site-packages/setuptools/command/launcher manifest.xml", "Lib/site-packages/setuptools/command/rotate.py", "Lib/site-packages/setuptools/command/saveopts.py", "Lib/site-packages/setuptools/command/sdist.py", "Lib/site-packages/setuptools/command/setopt.py", "Lib/site-packages/setuptools/command/test.py", "Lib/site-packages/setuptools/compat/__init__.py", "Lib/site-packages/setuptools/compat/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/setuptools/compat/__pycache__/py310.cpython-39.pyc", "Lib/site-packages/setuptools/compat/__pycache__/py311.cpython-39.pyc", "Lib/site-packages/setuptools/compat/__pycache__/py312.cpython-39.pyc", "Lib/site-packages/setuptools/compat/__pycache__/py39.cpython-39.pyc", "Lib/site-packages/setuptools/compat/py310.py", "Lib/site-packages/setuptools/compat/py311.py", "Lib/site-packages/setuptools/compat/py312.py", "Lib/site-packages/setuptools/compat/py39.py", "Lib/site-packages/setuptools/config/NOTICE", "Lib/site-packages/setuptools/config/__init__.py", "Lib/site-packages/setuptools/config/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/setuptools/config/__pycache__/_apply_pyprojecttoml.cpython-39.pyc", "Lib/site-packages/setuptools/config/__pycache__/expand.cpython-39.pyc", "Lib/site-packages/setuptools/config/__pycache__/pyprojecttoml.cpython-39.pyc", "Lib/site-packages/setuptools/config/__pycache__/setupcfg.cpython-39.pyc", "Lib/site-packages/setuptools/config/_apply_pyprojecttoml.py", "Lib/site-packages/setuptools/config/_validate_pyproject/NOTICE", "Lib/site-packages/setuptools/config/_validate_pyproject/__init__.py", "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/error_reporting.cpython-39.pyc", "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/extra_validations.cpython-39.pyc", "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/fastjsonschema_exceptions.cpython-39.pyc", "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/fastjsonschema_validations.cpython-39.pyc", "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/formats.cpython-39.pyc", "Lib/site-packages/setuptools/config/_validate_pyproject/error_reporting.py", "Lib/site-packages/setuptools/config/_validate_pyproject/extra_validations.py", "Lib/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_exceptions.py", "Lib/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_validations.py", "Lib/site-packages/setuptools/config/_validate_pyproject/formats.py", "Lib/site-packages/setuptools/config/distutils.schema.json", "Lib/site-packages/setuptools/config/expand.py", "Lib/site-packages/setuptools/config/pyprojecttoml.py", "Lib/site-packages/setuptools/config/setupcfg.py", "Lib/site-packages/setuptools/config/setuptools.schema.json", "Lib/site-packages/setuptools/depends.py", "Lib/site-packages/setuptools/discovery.py", "Lib/site-packages/setuptools/dist.py", "Lib/site-packages/setuptools/errors.py", "Lib/site-packages/setuptools/extension.py", "Lib/site-packages/setuptools/glob.py", "Lib/site-packages/setuptools/gui-32.exe", "Lib/site-packages/setuptools/gui-64.exe", "Lib/site-packages/setuptools/gui-arm64.exe", "Lib/site-packages/setuptools/gui.exe", "Lib/site-packages/setuptools/installer.py", "Lib/site-packages/setuptools/launch.py", "Lib/site-packages/setuptools/logging.py", "Lib/site-packages/setuptools/modified.py", "Lib/site-packages/setuptools/monkey.py", "Lib/site-packages/setuptools/msvc.py", "Lib/site-packages/setuptools/namespaces.py", "Lib/site-packages/setuptools/package_index.py", "Lib/site-packages/setuptools/sandbox.py", "Lib/site-packages/setuptools/script (dev).tmpl", "Lib/site-packages/setuptools/script.tmpl", "Lib/site-packages/setuptools/tests/__init__.py", "Lib/site-packages/setuptools/tests/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/contexts.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/environment.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/fixtures.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/mod_with_constant.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/namespaces.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/script-with-bom.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/server.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_archive_util.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_bdist_deprecations.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_bdist_egg.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_bdist_wheel.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_build.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_build_clib.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_build_ext.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_build_meta.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_build_py.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_config_discovery.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_core_metadata.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_depends.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_develop.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_dist.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_dist_info.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_distutils_adoption.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_easy_install.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_editable_install.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_egg_info.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_extern.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_find_packages.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_find_py_modules.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_glob.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_install_scripts.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_logging.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_manifest.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_namespaces.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_packageindex.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_sandbox.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_sdist.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_setopt.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_setuptools.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_shutil_wrapper.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_unicode_utils.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_virtualenv.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_warnings.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_wheel.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_windows_wrappers.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/text.cpython-39.pyc", "Lib/site-packages/setuptools/tests/__pycache__/textwrap.cpython-39.pyc", "Lib/site-packages/setuptools/tests/compat/__init__.py", "Lib/site-packages/setuptools/tests/compat/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/setuptools/tests/compat/__pycache__/py39.cpython-39.pyc", "Lib/site-packages/setuptools/tests/compat/py39.py", "Lib/site-packages/setuptools/tests/config/__init__.py", "Lib/site-packages/setuptools/tests/config/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/setuptools/tests/config/__pycache__/test_apply_pyprojecttoml.cpython-39.pyc", "Lib/site-packages/setuptools/tests/config/__pycache__/test_expand.cpython-39.pyc", "Lib/site-packages/setuptools/tests/config/__pycache__/test_pyprojecttoml.cpython-39.pyc", "Lib/site-packages/setuptools/tests/config/__pycache__/test_pyprojecttoml_dynamic_deps.cpython-39.pyc", "Lib/site-packages/setuptools/tests/config/__pycache__/test_setupcfg.cpython-39.pyc", "Lib/site-packages/setuptools/tests/config/downloads/__init__.py", "Lib/site-packages/setuptools/tests/config/downloads/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/setuptools/tests/config/downloads/__pycache__/preload.cpython-39.pyc", "Lib/site-packages/setuptools/tests/config/downloads/preload.py", "Lib/site-packages/setuptools/tests/config/setupcfg_examples.txt", "Lib/site-packages/setuptools/tests/config/test_apply_pyprojecttoml.py", "Lib/site-packages/setuptools/tests/config/test_expand.py", "Lib/site-packages/setuptools/tests/config/test_pyprojecttoml.py", "Lib/site-packages/setuptools/tests/config/test_pyprojecttoml_dynamic_deps.py", "Lib/site-packages/setuptools/tests/config/test_setupcfg.py", "Lib/site-packages/setuptools/tests/contexts.py", "Lib/site-packages/setuptools/tests/environment.py", "Lib/site-packages/setuptools/tests/fixtures.py", "Lib/site-packages/setuptools/tests/indexes/test_links_priority/external.html", "Lib/site-packages/setuptools/tests/indexes/test_links_priority/simple/foobar/index.html", "Lib/site-packages/setuptools/tests/integration/__init__.py", "Lib/site-packages/setuptools/tests/integration/__pycache__/__init__.cpython-39.pyc", "Lib/site-packages/setuptools/tests/integration/__pycache__/helpers.cpython-39.pyc", "Lib/site-packages/setuptools/tests/integration/__pycache__/test_pip_install_sdist.cpython-39.pyc", "Lib/site-packages/setuptools/tests/integration/helpers.py", "Lib/site-packages/setuptools/tests/integration/test_pip_install_sdist.py", "Lib/site-packages/setuptools/tests/mod_with_constant.py", "Lib/site-packages/setuptools/tests/namespaces.py", "Lib/site-packages/setuptools/tests/script-with-bom.py", "Lib/site-packages/setuptools/tests/server.py", "Lib/site-packages/setuptools/tests/test_archive_util.py", "Lib/site-packages/setuptools/tests/test_bdist_deprecations.py", "Lib/site-packages/setuptools/tests/test_bdist_egg.py", "Lib/site-packages/setuptools/tests/test_bdist_wheel.py", "Lib/site-packages/setuptools/tests/test_build.py", "Lib/site-packages/setuptools/tests/test_build_clib.py", "Lib/site-packages/setuptools/tests/test_build_ext.py", "Lib/site-packages/setuptools/tests/test_build_meta.py", "Lib/site-packages/setuptools/tests/test_build_py.py", "Lib/site-packages/setuptools/tests/test_config_discovery.py", "Lib/site-packages/setuptools/tests/test_core_metadata.py", "Lib/site-packages/setuptools/tests/test_depends.py", "Lib/site-packages/setuptools/tests/test_develop.py", "Lib/site-packages/setuptools/tests/test_dist.py", "Lib/site-packages/setuptools/tests/test_dist_info.py", "Lib/site-packages/setuptools/tests/test_distutils_adoption.py", "Lib/site-packages/setuptools/tests/test_easy_install.py", "Lib/site-packages/setuptools/tests/test_editable_install.py", "Lib/site-packages/setuptools/tests/test_egg_info.py", "Lib/site-packages/setuptools/tests/test_extern.py", "Lib/site-packages/setuptools/tests/test_find_packages.py", "Lib/site-packages/setuptools/tests/test_find_py_modules.py", "Lib/site-packages/setuptools/tests/test_glob.py", "Lib/site-packages/setuptools/tests/test_install_scripts.py", "Lib/site-packages/setuptools/tests/test_logging.py", "Lib/site-packages/setuptools/tests/test_manifest.py", "Lib/site-packages/setuptools/tests/test_namespaces.py", "Lib/site-packages/setuptools/tests/test_packageindex.py", "Lib/site-packages/setuptools/tests/test_sandbox.py", "Lib/site-packages/setuptools/tests/test_sdist.py", "Lib/site-packages/setuptools/tests/test_setopt.py", "Lib/site-packages/setuptools/tests/test_setuptools.py", "Lib/site-packages/setuptools/tests/test_shutil_wrapper.py", "Lib/site-packages/setuptools/tests/test_unicode_utils.py", "Lib/site-packages/setuptools/tests/test_virtualenv.py", "Lib/site-packages/setuptools/tests/test_warnings.py", "Lib/site-packages/setuptools/tests/test_wheel.py", "Lib/site-packages/setuptools/tests/test_windows_wrappers.py", "Lib/site-packages/setuptools/tests/text.py", "Lib/site-packages/setuptools/tests/textwrap.py", "Lib/site-packages/setuptools/unicode_utils.py", "Lib/site-packages/setuptools/version.py", "Lib/site-packages/setuptools/warnings.py", "Lib/site-packages/setuptools/wheel.py", "Lib/site-packages/setuptools/windows_support.py"], "fn": "setuptools-78.1.1-py39haa95532_0.conda", "license": "MIT", "link": {"source": "", "type": 1}, "md5": "e7d3b8bb45ddf3a99023f4e8c78ac358", "name": "setuptools", "package_tarball_full_path": "", "paths_data": {"paths": [{"_path": "Lib/site-packages/_distutils_hack/__init__.py", "path_type": "hardlink", "sha256": "df81e6bcba34ee3e3952f776551fb669143b9490fdd6c4caeb32609f97e985b4", "sha256_in_prefix": "df81e6bcba34ee3e3952f776551fb669143b9490fdd6c4caeb32609f97e985b4", "size_in_bytes": 6755}, {"_path": "Lib/site-packages/_distutils_hack/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "4831615c387b5b77bc6d9a54332e05b1632e1588f22d5a5008c3419d945c6a88", "sha256_in_prefix": "4831615c387b5b77bc6d9a54332e05b1632e1588f22d5a5008c3419d945c6a88", "size_in_bytes": 8150}, {"_path": "Lib/site-packages/_distutils_hack/__pycache__/override.cpython-39.pyc", "path_type": "hardlink", "sha256": "a6ae5aff50366f6b9ce2d95943c2b7e8b76e7964b7749a71c2a36dc389217cc3", "sha256_in_prefix": "a6ae5aff50366f6b9ce2d95943c2b7e8b76e7964b7749a71c2a36dc389217cc3", "size_in_bytes": 187}, {"_path": "Lib/site-packages/_distutils_hack/override.py", "path_type": "hardlink", "sha256": "12efecf8d17a5486780aa774b5b6c0e70b56932d8864f35df1eb7a18bb759b3a", "sha256_in_prefix": "12efecf8d17a5486780aa774b5b6c0e70b56932d8864f35df1eb7a18bb759b3a", "size_in_bytes": 44}, {"_path": "Lib/site-packages/distutils-precedence.pth", "path_type": "hardlink", "sha256": "ab406aa05439fe87070cde36180433193568432f11d04f0f762f374b8a9302f5", "sha256_in_prefix": "ab406aa05439fe87070cde36180433193568432f11d04f0f762f374b8a9302f5", "size_in_bytes": 152}, {"_path": "Lib/site-packages/pkg_resources/__init__.py", "path_type": "hardlink", "sha256": "fab87b5ce9d3c5d1ae0beffd140caee43eacf012f552c05e87152d8fb6be215a", "sha256_in_prefix": "fab87b5ce9d3c5d1ae0beffd140caee43eacf012f552c05e87152d8fb6be215a", "size_in_bytes": 126203}, {"_path": "Lib/site-packages/pkg_resources/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "a5f19b8fc7cc7558209da072c9351a8303ffe43b1b95ae77d45fcaeecc1f069e", "sha256_in_prefix": "a5f19b8fc7cc7558209da072c9351a8303ffe43b1b95ae77d45fcaeecc1f069e", "size_in_bytes": 113849}, {"_path": "Lib/site-packages/pkg_resources/api_tests.txt", "path_type": "hardlink", "sha256": "5c476fcb88a01c7aeadaa34734c1e795f3ba5d240a36a3b22c76e5e907297c02", "sha256_in_prefix": "5c476fcb88a01c7aeadaa34734c1e795f3ba5d240a36a3b22c76e5e907297c02", "size_in_bytes": 12595}, {"_path": "Lib/site-packages/pkg_resources/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/pkg_resources/tests/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "093e4d49bee46c54fb7280c77f265f00d587f99d06f6902ff63088ac3e6a1847", "sha256_in_prefix": "093e4d49bee46c54fb7280c77f265f00d587f99d06f6902ff63088ac3e6a1847", "size_in_bytes": 140}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/test_find_distributions.cpython-39.pyc", "path_type": "hardlink", "sha256": "69ea6a7f288754f9203f2d182e4f54bb4d4dd91f6174fa7f1d18ca7267431ad7", "sha256_in_prefix": "69ea6a7f288754f9203f2d182e4f54bb4d4dd91f6174fa7f1d18ca7267431ad7", "size_in_bytes": 2483}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/test_integration_zope_interface.cpython-39.pyc", "path_type": "hardlink", "sha256": "caff222ceff039e4823c2829452e09f687fa544c26751e68035c2bbc9a8cfa6b", "sha256_in_prefix": "caff222ceff039e4823c2829452e09f687fa544c26751e68035c2bbc9a8cfa6b", "size_in_bytes": 1542}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/test_markers.cpython-39.pyc", "path_type": "hardlink", "sha256": "68d48e92736efbc02abd0b46deb172a36538f1cdb18430fa88852f991d4a1069", "sha256_in_prefix": "68d48e92736efbc02abd0b46deb172a36538f1cdb18430fa88852f991d4a1069", "size_in_bytes": 480}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/test_pkg_resources.cpython-39.pyc", "path_type": "hardlink", "sha256": "44d4f10b965eafaca7e0dc921a256a18fc0e293d37b1c4dd5831916f62cff0fb", "sha256_in_prefix": "44d4f10b965eafaca7e0dc921a256a18fc0e293d37b1c4dd5831916f62cff0fb", "size_in_bytes": 15242}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/test_resources.cpython-39.pyc", "path_type": "hardlink", "sha256": "d0f67433c87a833b13741d9314550822c942f3f6339c180c80f7df31bb87ea8d", "sha256_in_prefix": "d0f67433c87a833b13741d9314550822c942f3f6339c180c80f7df31bb87ea8d", "size_in_bytes": 28260}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/test_working_set.cpython-39.pyc", "path_type": "hardlink", "sha256": "ae688559b3cd765bdc44463f9a14888b32ff91aebe938232c5f764caa4924500", "sha256_in_prefix": "ae688559b3cd765bdc44463f9a14888b32ff91aebe938232c5f764caa4924500", "size_in_bytes": 8284}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package-source/__pycache__/setup.cpython-39.pyc", "path_type": "hardlink", "sha256": "6e720c938a3d8fc56e280b7179a45ad724368e34402e69bd78d1575a6eafa602", "sha256_in_prefix": "6e720c938a3d8fc56e280b7179a45ad724368e34402e69bd78d1575a6eafa602", "size_in_bytes": 270}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package-source/setup.cfg", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package-source/setup.py", "path_type": "hardlink", "sha256": "d55a1b84065b31beccf667e16ff344f0fc03b2fba4a162ecf5a5004b4a5885ef", "sha256_in_prefix": "d55a1b84065b31beccf667e16ff344f0fc03b2fba4a162ecf5a5004b4a5885ef", "size_in_bytes": 105}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package-zip/my-test-package.zip", "path_type": "hardlink", "sha256": "01845c437f4655e3cf9cc4fc4e49cfd607431f22675e1b611129a90239f34822", "sha256_in_prefix": "01845c437f4655e3cf9cc4fc4e49cfd607431f22675e1b611129a90239f34822", "size_in_bytes": 1809}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/PKG-INFO", "path_type": "hardlink", "sha256": "26f5aff48a363c0b98c04130d9f056e1073962f75b92c729297d6498bceca079", "sha256_in_prefix": "26f5aff48a363c0b98c04130d9f056e1073962f75b92c729297d6498bceca079", "size_in_bytes": 187}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/SOURCES.txt", "path_type": "hardlink", "sha256": "e029641fc793a2f66b755ac916c56ec5d6cc105fbe941552b8aa270c03c4e497", "sha256_in_prefix": "e029641fc793a2f66b755ac916c56ec5d6cc105fbe941552b8aa270c03c4e497", "size_in_bytes": 208}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/dependency_links.txt", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/top_level.txt", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/zip-safe", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package_zipped-egg/my_test_package-1.0-py3.7.egg", "path_type": "hardlink", "sha256": "65394c1b18d11a2283364880d9cef98db407d93588b5e3f4d22ac5f60bdccdba", "sha256_in_prefix": "65394c1b18d11a2283364880d9cef98db407d93588b5e3f4d22ac5f60bdccdba", "size_in_bytes": 843}, {"_path": "Lib/site-packages/pkg_resources/tests/test_find_distributions.py", "path_type": "hardlink", "sha256": "53dd5ca2fe4bd423802162cdab75f2e29954eff327384d56b5732eea2576c1a3", "sha256_in_prefix": "53dd5ca2fe4bd423802162cdab75f2e29954eff327384d56b5732eea2576c1a3", "size_in_bytes": 1972}, {"_path": "Lib/site-packages/pkg_resources/tests/test_integration_zope_interface.py", "path_type": "hardlink", "sha256": "9f35682b9e7b29940dd15dc3210d6c55e6823a0b782a997e08e0c05ac3bba667", "sha256_in_prefix": "9f35682b9e7b29940dd15dc3210d6c55e6823a0b782a997e08e0c05ac3bba667", "size_in_bytes": 1652}, {"_path": "Lib/site-packages/pkg_resources/tests/test_markers.py", "path_type": "hardlink", "sha256": "d28aca83b50c0dfedf9ee350bd130e73e105f4400ffc94d09e4e26b4681b5b9d", "sha256_in_prefix": "d28aca83b50c0dfedf9ee350bd130e73e105f4400ffc94d09e4e26b4681b5b9d", "size_in_bytes": 241}, {"_path": "Lib/site-packages/pkg_resources/tests/test_pkg_resources.py", "path_type": "hardlink", "sha256": "e4cb786c94212c22fc8fc702e3a52fdf6369d977354d3c4b19ac087c44f9e459", "sha256_in_prefix": "e4cb786c94212c22fc8fc702e3a52fdf6369d977354d3c4b19ac087c44f9e459", "size_in_bytes": 17111}, {"_path": "Lib/site-packages/pkg_resources/tests/test_resources.py", "path_type": "hardlink", "sha256": "2b42ea300506a5143da546fd2b4bf223b19eb2fb6542f4c7d3be26f84d95425a", "sha256_in_prefix": "2b42ea300506a5143da546fd2b4bf223b19eb2fb6542f4c7d3be26f84d95425a", "size_in_bytes": 31252}, {"_path": "Lib/site-packages/pkg_resources/tests/test_working_set.py", "path_type": "hardlink", "sha256": "951b46256222c52c123126e31e047178911088b3115dccf7c7324bdaa2fb7976", "sha256_in_prefix": "951b46256222c52c123126e31e047178911088b3115dccf7c7324bdaa2fb7976", "size_in_bytes": 8602}, {"_path": "Lib/site-packages/setuptools-78.1.1-py3.9.egg-info/PKG-INFO", "path_type": "hardlink", "sha256": "b8abe3cc375d9768729904d2c5da6985061c34977a9507dfda1cd32bba8190c2", "sha256_in_prefix": "b8abe3cc375d9768729904d2c5da6985061c34977a9507dfda1cd32bba8190c2", "size_in_bytes": 6688}, {"_path": "Lib/site-packages/setuptools-78.1.1-py3.9.egg-info/SOURCES.txt", "path_type": "hardlink", "sha256": "cfded7934597a4a900d7a0f110a44dff6486e4f7b4c8450b08ce803d31a09433", "sha256_in_prefix": "cfded7934597a4a900d7a0f110a44dff6486e4f7b4c8450b08ce803d31a09433", "size_in_bytes": 24294}, {"_path": "Lib/site-packages/setuptools-78.1.1-py3.9.egg-info/dependency_links.txt", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "Lib/site-packages/setuptools-78.1.1-py3.9.egg-info/entry_points.txt", "path_type": "hardlink", "sha256": "ce482d8697ff15af4d544f69e85293dd793d0d1d5f680711538728820b15ee30", "sha256_in_prefix": "ce482d8697ff15af4d544f69e85293dd793d0d1d5f680711538728820b15ee30", "size_in_bytes": 2449}, {"_path": "Lib/site-packages/setuptools-78.1.1-py3.9.egg-info/requires.txt", "path_type": "hardlink", "sha256": "cd186b9559d56bb22f53a7730a6ffdd8b0c2a342bbf97705c98b547553d584e3", "sha256_in_prefix": "cd186b9559d56bb22f53a7730a6ffdd8b0c2a342bbf97705c98b547553d584e3", "size_in_bytes": 1231}, {"_path": "Lib/site-packages/setuptools-78.1.1-py3.9.egg-info/top_level.txt", "path_type": "hardlink", "sha256": "77dc8bdfdbff5bbaa62830d21fab13e1b1348ff2ecd4cdcfd7ad4e1a076c9b88", "sha256_in_prefix": "77dc8bdfdbff5bbaa62830d21fab13e1b1348ff2ecd4cdcfd7ad4e1a076c9b88", "size_in_bytes": 41}, {"_path": "Lib/site-packages/setuptools/__init__.py", "path_type": "hardlink", "sha256": "010b0c791156cfd090f5a06d71291b0780e7f2ddb0f3af863eb8a4969a008dec", "sha256_in_prefix": "010b0c791156cfd090f5a06d71291b0780e7f2ddb0f3af863eb8a4969a008dec", "size_in_bytes": 10406}, {"_path": "Lib/site-packages/setuptools/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "99f16eef078ba49a0cd3df5ffbb7800e63af4a3f86360f4ed1b39500a67a1d49", "sha256_in_prefix": "99f16eef078ba49a0cd3df5ffbb7800e63af4a3f86360f4ed1b39500a67a1d49", "size_in_bytes": 10944}, {"_path": "Lib/site-packages/setuptools/__pycache__/_core_metadata.cpython-39.pyc", "path_type": "hardlink", "sha256": "b082fee9740164234574c39808db11b53bb512c792d8577d5bfd749975e35a19", "sha256_in_prefix": "b082fee9740164234574c39808db11b53bb512c792d8577d5bfd749975e35a19", "size_in_bytes": 9199}, {"_path": "Lib/site-packages/setuptools/__pycache__/_entry_points.cpython-39.pyc", "path_type": "hardlink", "sha256": "17e4d61065b3afc2863368a7ff7672edd5941e9bc4398542c1d666d1ce020593", "sha256_in_prefix": "17e4d61065b3afc2863368a7ff7672edd5941e9bc4398542c1d666d1ce020593", "size_in_bytes": 3204}, {"_path": "Lib/site-packages/setuptools/__pycache__/_imp.cpython-39.pyc", "path_type": "hardlink", "sha256": "0566478ff915d26514f804293a4947ba7fda6752ed8503ea5fb8806864e22368", "sha256_in_prefix": "0566478ff915d26514f804293a4947ba7fda6752ed8503ea5fb8806864e22368", "size_in_bytes": 2063}, {"_path": "Lib/site-packages/setuptools/__pycache__/_importlib.cpython-39.pyc", "path_type": "hardlink", "sha256": "e1810e33eea2d90711a73968fab873c01cf191602044efb1b0cca5cac805fa02", "sha256_in_prefix": "e1810e33eea2d90711a73968fab873c01cf191602044efb1b0cca5cac805fa02", "size_in_bytes": 308}, {"_path": "Lib/site-packages/setuptools/__pycache__/_itertools.cpython-39.pyc", "path_type": "hardlink", "sha256": "6d54f1e338c7b3814f3cff6fd97066b1228ef3bc2b0ca505ac459b534f4baa5f", "sha256_in_prefix": "6d54f1e338c7b3814f3cff6fd97066b1228ef3bc2b0ca505ac459b534f4baa5f", "size_in_bytes": 841}, {"_path": "Lib/site-packages/setuptools/__pycache__/_normalization.cpython-39.pyc", "path_type": "hardlink", "sha256": "2b13221b6ab9ff876558bfbc862dd9359831049703c199237e181799875bdba0", "sha256_in_prefix": "2b13221b6ab9ff876558bfbc862dd9359831049703c199237e181799875bdba0", "size_in_bytes": 5553}, {"_path": "Lib/site-packages/setuptools/__pycache__/_path.cpython-39.pyc", "path_type": "hardlink", "sha256": "7146bf594d4dea9caf0d22c5700c420ade57c529183ca9a6bfac915615b1dd3d", "sha256_in_prefix": "7146bf594d4dea9caf0d22c5700c420ade57c529183ca9a6bfac915615b1dd3d", "size_in_bytes": 2837}, {"_path": "Lib/site-packages/setuptools/__pycache__/_reqs.cpython-39.pyc", "path_type": "hardlink", "sha256": "b16dc562a67545856626d3c4ce74f690b683dcec14580e6e832cca633c29dece", "sha256_in_prefix": "b16dc562a67545856626d3c4ce74f690b683dcec14580e6e832cca633c29dece", "size_in_bytes": 1580}, {"_path": "Lib/site-packages/setuptools/__pycache__/_shutil.cpython-39.pyc", "path_type": "hardlink", "sha256": "26c29be90faa845404cd2e8346d9191e0190a3098abbfcadc5b51f3fbfe869e4", "sha256_in_prefix": "26c29be90faa845404cd2e8346d9191e0190a3098abbfcadc5b51f3fbfe869e4", "size_in_bytes": 1678}, {"_path": "Lib/site-packages/setuptools/__pycache__/_static.cpython-39.pyc", "path_type": "hardlink", "sha256": "433a7fa1d391df53adb8617a49ad49254117950f662e6aed275a221f0c9b0c07", "sha256_in_prefix": "433a7fa1d391df53adb8617a49ad49254117950f662e6aed275a221f0c9b0c07", "size_in_bytes": 5182}, {"_path": "Lib/site-packages/setuptools/__pycache__/archive_util.cpython-39.pyc", "path_type": "hardlink", "sha256": "e0f08b84ec90dc62a52a15843eeb3760049495c20bc18adf2c03f06d384fb1c1", "sha256_in_prefix": "e0f08b84ec90dc62a52a15843eeb3760049495c20bc18adf2c03f06d384fb1c1", "size_in_bytes": 6136}, {"_path": "Lib/site-packages/setuptools/__pycache__/build_meta.cpython-39.pyc", "path_type": "hardlink", "sha256": "6696cd312db20c4a8aa150c54cea2025f66623e6ddb5e36467e48c9bb617925c", "sha256_in_prefix": "6696cd312db20c4a8aa150c54cea2025f66623e6ddb5e36467e48c9bb617925c", "size_in_bytes": 18115}, {"_path": "Lib/site-packages/setuptools/__pycache__/depends.cpython-39.pyc", "path_type": "hardlink", "sha256": "cbf140f820d1ebdf1b6fc71a0e555cd121069901e9b50d8e6ccdd57527acb623", "sha256_in_prefix": "cbf140f820d1ebdf1b6fc71a0e555cd121069901e9b50d8e6ccdd57527acb623", "size_in_bytes": 5327}, {"_path": "Lib/site-packages/setuptools/__pycache__/discovery.cpython-39.pyc", "path_type": "hardlink", "sha256": "c406dc47043761639e0ad5f43351215185de242819653568dce97ad7bc9e2baa", "sha256_in_prefix": "c406dc47043761639e0ad5f43351215185de242819653568dce97ad7bc9e2baa", "size_in_bytes": 20766}, {"_path": "Lib/site-packages/setuptools/__pycache__/dist.cpython-39.pyc", "path_type": "hardlink", "sha256": "f1c7c502863fbd104aa82ed030e25a4195dbc00188ad672049746d751b687527", "sha256_in_prefix": "f1c7c502863fbd104aa82ed030e25a4195dbc00188ad672049746d751b687527", "size_in_bytes": 37167}, {"_path": "Lib/site-packages/setuptools/__pycache__/errors.cpython-39.pyc", "path_type": "hardlink", "sha256": "48fceda2d415d1f207a500b6846c40abd3269b7673b24452263874d2b316f5f3", "sha256_in_prefix": "48fceda2d415d1f207a500b6846c40abd3269b7673b24452263874d2b316f5f3", "size_in_bytes": 2880}, {"_path": "Lib/site-packages/setuptools/__pycache__/extension.cpython-39.pyc", "path_type": "hardlink", "sha256": "6e0b8e1dec8d3493e7127ebb90e27efe1ec38067c655adaa9b6189ac10bdd6f6", "sha256_in_prefix": "6e0b8e1dec8d3493e7127ebb90e27efe1ec38067c655adaa9b6189ac10bdd6f6", "size_in_bytes": 6232}, {"_path": "Lib/site-packages/setuptools/__pycache__/glob.cpython-39.pyc", "path_type": "hardlink", "sha256": "7be945516826ee100d65470a7ff0a3630f65ac71a50202ec66dd74bddf1413ac", "sha256_in_prefix": "7be945516826ee100d65470a7ff0a3630f65ac71a50202ec66dd74bddf1413ac", "size_in_bytes": 4867}, {"_path": "Lib/site-packages/setuptools/__pycache__/installer.cpython-39.pyc", "path_type": "hardlink", "sha256": "704ee01c69e7a40eded2859b71f81950a76eba177f242cffa066e5366074e9ca", "sha256_in_prefix": "704ee01c69e7a40eded2859b71f81950a76eba177f242cffa066e5366074e9ca", "size_in_bytes": 4128}, {"_path": "Lib/site-packages/setuptools/__pycache__/launch.cpython-39.pyc", "path_type": "hardlink", "sha256": "43b3ccf39d67924416d1330ff30e054994640a6480c08d0f266a5f892554ef85", "sha256_in_prefix": "43b3ccf39d67924416d1330ff30e054994640a6480c08d0f266a5f892554ef85", "size_in_bytes": 868}, {"_path": "Lib/site-packages/setuptools/__pycache__/logging.cpython-39.pyc", "path_type": "hardlink", "sha256": "f7f500cc84efd0fa06187fe6fcc994ab765b2798c1576bd8a4797cd75ef4fc02", "sha256_in_prefix": "f7f500cc84efd0fa06187fe6fcc994ab765b2798c1576bd8a4797cd75ef4fc02", "size_in_bytes": 1254}, {"_path": "Lib/site-packages/setuptools/__pycache__/modified.cpython-39.pyc", "path_type": "hardlink", "sha256": "81d4beb4b2d73bf6ef5e2cb9a0c9c3d94601387e8e86a3302bc9bfa69b2a0b05", "sha256_in_prefix": "81d4beb4b2d73bf6ef5e2cb9a0c9c3d94601387e8e86a3302bc9bfa69b2a0b05", "size_in_bytes": 393}, {"_path": "Lib/site-packages/setuptools/__pycache__/monkey.cpython-39.pyc", "path_type": "hardlink", "sha256": "5260ab305f4e73584c24acd0940185d52f73d0f39c41d44bffdca5ab59c0349f", "sha256_in_prefix": "5260ab305f4e73584c24acd0940185d52f73d0f39c41d44bffdca5ab59c0349f", "size_in_bytes": 3534}, {"_path": "Lib/site-packages/setuptools/__pycache__/msvc.cpython-39.pyc", "path_type": "hardlink", "sha256": "8666bc49985ba8ee25ff50fbc59437f4036208f6b6d9dc1403533b990c3ba3d4", "sha256_in_prefix": "8666bc49985ba8ee25ff50fbc59437f4036208f6b6d9dc1403533b990c3ba3d4", "size_in_bytes": 36738}, {"_path": "Lib/site-packages/setuptools/__pycache__/namespaces.cpython-39.pyc", "path_type": "hardlink", "sha256": "fc1c015f47f9994f61cba952507998efde42908bc730a0cb45898f081d344297", "sha256_in_prefix": "fc1c015f47f9994f61cba952507998efde42908bc730a0cb45898f081d344297", "size_in_bytes": 3693}, {"_path": "Lib/site-packages/setuptools/__pycache__/package_index.cpython-39.pyc", "path_type": "hardlink", "sha256": "7bf102d85e490f9ce2fe65c62002dd195f8811a5a4241fea5fa74daa71aa4746", "sha256_in_prefix": "7bf102d85e490f9ce2fe65c62002dd195f8811a5a4241fea5fa74daa71aa4746", "size_in_bytes": 34719}, {"_path": "Lib/site-packages/setuptools/__pycache__/sandbox.cpython-39.pyc", "path_type": "hardlink", "sha256": "bfd85bae0fafa5096a753ed9372b6661674cefd78bbf3d1b71858d29bccc9be1", "sha256_in_prefix": "bfd85bae0fafa5096a753ed9372b6661674cefd78bbf3d1b71858d29bccc9be1", "size_in_bytes": 16173}, {"_path": "Lib/site-packages/setuptools/__pycache__/unicode_utils.cpython-39.pyc", "path_type": "hardlink", "sha256": "64af5b8d48029ffdea35bb72fa9c4ca9537d9e8e6cad7795cdce5e7404d9ec14", "sha256_in_prefix": "64af5b8d48029ffdea35bb72fa9c4ca9537d9e8e6cad7795cdce5e7404d9ec14", "size_in_bytes": 3128}, {"_path": "Lib/site-packages/setuptools/__pycache__/version.cpython-39.pyc", "path_type": "hardlink", "sha256": "048721c0dfb6dfc23abf312e45dd7ef4947e2170233f3e3c3ccc1c7a92dff408", "sha256_in_prefix": "048721c0dfb6dfc23abf312e45dd7ef4947e2170233f3e3c3ccc1c7a92dff408", "size_in_bytes": 284}, {"_path": "Lib/site-packages/setuptools/__pycache__/warnings.cpython-39.pyc", "path_type": "hardlink", "sha256": "95a54c59abf0e883f6979dcfae4d1382c6f5277ae62449a655fc2ace1f983328", "sha256_in_prefix": "95a54c59abf0e883f6979dcfae4d1382c6f5277ae62449a655fc2ace1f983328", "size_in_bytes": 3869}, {"_path": "Lib/site-packages/setuptools/__pycache__/wheel.cpython-39.pyc", "path_type": "hardlink", "sha256": "84a03f93dd810556ae3b898cccf83df71c2f849a4b8005e3a1b2e0cca9cb828f", "sha256_in_prefix": "84a03f93dd810556ae3b898cccf83df71c2f849a4b8005e3a1b2e0cca9cb828f", "size_in_bytes": 7689}, {"_path": "Lib/site-packages/setuptools/__pycache__/windows_support.cpython-39.pyc", "path_type": "hardlink", "sha256": "bca08400310e5cebeb00268d5f115e86121d1a105d31e73511cca8c52c086179", "sha256_in_prefix": "bca08400310e5cebeb00268d5f115e86121d1a105d31e73511cca8c52c086179", "size_in_bytes": 996}, {"_path": "Lib/site-packages/setuptools/_core_metadata.py", "path_type": "hardlink", "sha256": "4fb4e3a7e592a0df3cd5a75ebf7475c335c23e79031ea6c2d8c83294dd728d2f", "sha256_in_prefix": "4fb4e3a7e592a0df3cd5a75ebf7475c335c23e79031ea6c2d8c83294dd728d2f", "size_in_bytes": 11978}, {"_path": "Lib/site-packages/setuptools/_distutils/__init__.py", "path_type": "hardlink", "sha256": "c4662e856c0b1b4ec9d10e3d0559c48cfcbac320dc77abde24c0c95fb9639723", "sha256_in_prefix": "c4662e856c0b1b4ec9d10e3d0559c48cfcbac320dc77abde24c0c95fb9639723", "size_in_bytes": 359}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "d2c9a33c22a3a0d3279547819d891ff313e509e7a8cf14db1b57fee79d9a1b0e", "sha256_in_prefix": "d2c9a33c22a3a0d3279547819d891ff313e509e7a8cf14db1b57fee79d9a1b0e", "size_in_bytes": 330}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/_log.cpython-39.pyc", "path_type": "hardlink", "sha256": "9fd636c283caa34b060095fc6ac3af52c4c363aa67467bce98b841c782bb7ac9", "sha256_in_prefix": "9fd636c283caa34b060095fc6ac3af52c4c363aa67467bce98b841c782bb7ac9", "size_in_bytes": 183}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/_macos_compat.cpython-39.pyc", "path_type": "hardlink", "sha256": "7661d64e205c80cb6742f359fb9f8a0846dfa6eca71f20e463cfc3e6879aa421", "sha256_in_prefix": "7661d64e205c80cb6742f359fb9f8a0846dfa6eca71f20e463cfc3e6879aa421", "size_in_bytes": 404}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/_modified.cpython-39.pyc", "path_type": "hardlink", "sha256": "6de903eab7aa71847cdc93fc1f5684fc9bfcefa8d468bb149938da8aaccbd510", "sha256_in_prefix": "6de903eab7aa71847cdc93fc1f5684fc9bfcefa8d468bb149938da8aaccbd510", "size_in_bytes": 3557}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/_msvccompiler.cpython-39.pyc", "path_type": "hardlink", "sha256": "befecf20f90677fced47ec712eec384a480cd78ef32e0b337822a93db072e9b7", "sha256_in_prefix": "befecf20f90677fced47ec712eec384a480cd78ef32e0b337822a93db072e9b7", "size_in_bytes": 530}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/archive_util.cpython-39.pyc", "path_type": "hardlink", "sha256": "f40e22f58ea24e5d241cb9c9c2747e443681bcab46b38884d2df639c8d59cf8f", "sha256_in_prefix": "f40e22f58ea24e5d241cb9c9c2747e443681bcab46b38884d2df639c8d59cf8f", "size_in_bytes": 6948}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/ccompiler.cpython-39.pyc", "path_type": "hardlink", "sha256": "54efef73dcb069e244ce0e67a46d25f0afc08228d9fd6a61dd6ccf6099934195", "sha256_in_prefix": "54efef73dcb069e244ce0e67a46d25f0afc08228d9fd6a61dd6ccf6099934195", "size_in_bytes": 591}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/cmd.cpython-39.pyc", "path_type": "hardlink", "sha256": "afe19442db797d3fbb77ad7db706fa8e2f81650925bac7c1d884c1da48710168", "sha256_in_prefix": "afe19442db797d3fbb77ad7db706fa8e2f81650925bac7c1d884c1da48710168", "size_in_bytes": 16821}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/core.cpython-39.pyc", "path_type": "hardlink", "sha256": "7b2974c3300a309403f006c6d32b0e2cb8159422afb3a10a5d10884346f93873", "sha256_in_prefix": "7b2974c3300a309403f006c6d32b0e2cb8159422afb3a10a5d10884346f93873", "size_in_bytes": 7213}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/cygwinccompiler.cpython-39.pyc", "path_type": "hardlink", "sha256": "c67e0c88491f96f8c71c2e10980729dae95a9ceb553869f980fa68b41c290c58", "sha256_in_prefix": "c67e0c88491f96f8c71c2e10980729dae95a9ceb553869f980fa68b41c290c58", "size_in_bytes": 527}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/debug.cpython-39.pyc", "path_type": "hardlink", "sha256": "17651de65a6054f559f75cc488882b7967ed1c041b78e41220340c9b030c48b8", "sha256_in_prefix": "17651de65a6054f559f75cc488882b7967ed1c041b78e41220340c9b030c48b8", "size_in_bytes": 205}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/dep_util.cpython-39.pyc", "path_type": "hardlink", "sha256": "59565297618d64ed191a7b21c3d87ab80778c691ccad47af244e41690841e85f", "sha256_in_prefix": "59565297618d64ed191a7b21c3d87ab80778c691ccad47af244e41690841e85f", "size_in_bytes": 541}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/dir_util.cpython-39.pyc", "path_type": "hardlink", "sha256": "a9890eb104ab34dc69e8d1d011e6d1aff2676d25fb6cb2f983e15287a08dc1ad", "sha256_in_prefix": "a9890eb104ab34dc69e8d1d011e6d1aff2676d25fb6cb2f983e15287a08dc1ad", "size_in_bytes": 7191}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/dist.cpython-39.pyc", "path_type": "hardlink", "sha256": "82e348c262f053b24776ef454a2bc78ad4d57a10205a2e81ce1b25c692d75968", "sha256_in_prefix": "82e348c262f053b24776ef454a2bc78ad4d57a10205a2e81ce1b25c692d75968", "size_in_bytes": 39064}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/errors.cpython-39.pyc", "path_type": "hardlink", "sha256": "74b85e458e90942504932e678e9484dda734a04e47e75d63ffe7cb1778cb0d73", "sha256_in_prefix": "74b85e458e90942504932e678e9484dda734a04e47e75d63ffe7cb1778cb0d73", "size_in_bytes": 4148}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/extension.cpython-39.pyc", "path_type": "hardlink", "sha256": "ae9bd1b1b207d6232f9d9bbb4c61567f48248e4ec5ae77112bcf42d8b77ad386", "sha256_in_prefix": "ae9bd1b1b207d6232f9d9bbb4c61567f48248e4ec5ae77112bcf42d8b77ad386", "size_in_bytes": 7478}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/fancy_getopt.cpython-39.pyc", "path_type": "hardlink", "sha256": "3b73b681a50c1adfbfeccf096de7890f11c54ca145abbe8a8db9b915fbb7a884", "sha256_in_prefix": "3b73b681a50c1adfbfeccf096de7890f11c54ca145abbe8a8db9b915fbb7a884", "size_in_bytes": 10885}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/file_util.cpython-39.pyc", "path_type": "hardlink", "sha256": "407d1f10648a9af5bf396da0ddb4987d1942c56888e2394ea2fc3b455b1b39b8", "sha256_in_prefix": "407d1f10648a9af5bf396da0ddb4987d1942c56888e2394ea2fc3b455b1b39b8", "size_in_bytes": 6046}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/filelist.cpython-39.pyc", "path_type": "hardlink", "sha256": "8054439dcdd2fc0f6e0205e320e512429c751ce16745a0d9418684d24f27c20c", "sha256_in_prefix": "8054439dcdd2fc0f6e0205e320e512429c751ce16745a0d9418684d24f27c20c", "size_in_bytes": 12022}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/log.cpython-39.pyc", "path_type": "hardlink", "sha256": "8737db260a87bb97b52b04b817bb559c3e4431fd8d060d19593f2a147530e3ca", "sha256_in_prefix": "8737db260a87bb97b52b04b817bb559c3e4431fd8d060d19593f2a147530e3ca", "size_in_bytes": 1640}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/spawn.cpython-39.pyc", "path_type": "hardlink", "sha256": "f42ced35643a322a9790a9c25059ff7e820092ea87c18c78a6524060b0740780", "sha256_in_prefix": "f42ced35643a322a9790a9c25059ff7e820092ea87c18c78a6524060b0740780", "size_in_bytes": 4071}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/sysconfig.cpython-39.pyc", "path_type": "hardlink", "sha256": "c91c4961a20098e06c97ede0285a388cc54ca66aaa2bd83d9c15e56996c3787e", "sha256_in_prefix": "c91c4961a20098e06c97ede0285a388cc54ca66aaa2bd83d9c15e56996c3787e", "size_in_bytes": 15603}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/text_file.cpython-39.pyc", "path_type": "hardlink", "sha256": "ea5519889a0b46620993ce41ea57b2987e0c33df847b848c69d9845356daef3a", "sha256_in_prefix": "ea5519889a0b46620993ce41ea57b2987e0c33df847b848c69d9845356daef3a", "size_in_bytes": 8254}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/unixccompiler.cpython-39.pyc", "path_type": "hardlink", "sha256": "eb03f09ef4a5de05b193eddfd0a3667497f8cc6c02b7b8bd0d1d790005490ef8", "sha256_in_prefix": "eb03f09ef4a5de05b193eddfd0a3667497f8cc6c02b7b8bd0d1d790005490ef8", "size_in_bytes": 294}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/util.cpython-39.pyc", "path_type": "hardlink", "sha256": "3f49aab88fc974b4f3df249143011d2bab37622f228de746572a89545986fee0", "sha256_in_prefix": "3f49aab88fc974b4f3df249143011d2bab37622f228de746572a89545986fee0", "size_in_bytes": 14081}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/version.cpython-39.pyc", "path_type": "hardlink", "sha256": "f6fa71d22cb9fe88db84f946817ba7105b1164ed4959a719bf074bfc48c2a3a6", "sha256_in_prefix": "f6fa71d22cb9fe88db84f946817ba7105b1164ed4959a719bf074bfc48c2a3a6", "size_in_bytes": 8092}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/versionpredicate.cpython-39.pyc", "path_type": "hardlink", "sha256": "2392999229c25cc4d8dff49e10fdd0ffdb59557668ff26c13139a7b7bf785f75", "sha256_in_prefix": "2392999229c25cc4d8dff49e10fdd0ffdb59557668ff26c13139a7b7bf785f75", "size_in_bytes": 5232}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/zosccompiler.cpython-39.pyc", "path_type": "hardlink", "sha256": "3ecb3584e054917277348325be5034014c2d05e15f5cdc3aabd3c9e5f0ebe9a6", "sha256_in_prefix": "3ecb3584e054917277348325be5034014c2d05e15f5cdc3aabd3c9e5f0ebe9a6", "size_in_bytes": 217}, {"_path": "Lib/site-packages/setuptools/_distutils/_log.py", "path_type": "hardlink", "sha256": "8be94d4d37174bc4e65884c9e833831afb56e73e6d31ab6d250efa87cad9c505", "sha256_in_prefix": "8be94d4d37174bc4e65884c9e833831afb56e73e6d31ab6d250efa87cad9c505", "size_in_bytes": 42}, {"_path": "Lib/site-packages/setuptools/_distutils/_macos_compat.py", "path_type": "hardlink", "sha256": "273506845e04e722084c76d468fa1b6445a318776badc355eb7cfce92e118c17", "sha256_in_prefix": "273506845e04e722084c76d468fa1b6445a318776badc355eb7cfce92e118c17", "size_in_bytes": 239}, {"_path": "Lib/site-packages/setuptools/_distutils/_modified.py", "path_type": "hardlink", "sha256": "445d67d427b1c83615de5bc66de5d2d2cf9708955ba0338851b03cc0442a6136", "sha256_in_prefix": "445d67d427b1c83615de5bc66de5d2d2cf9708955ba0338851b03cc0442a6136", "size_in_bytes": 3211}, {"_path": "Lib/site-packages/setuptools/_distutils/_msvccompiler.py", "path_type": "hardlink", "sha256": "f4f49f487c6f2671e740be92ab3e17733ee2681213eb6a7a061790cc6b12970a", "sha256_in_prefix": "f4f49f487c6f2671e740be92ab3e17733ee2681213eb6a7a061790cc6b12970a", "size_in_bytes": 335}, {"_path": "Lib/site-packages/setuptools/_distutils/archive_util.py", "path_type": "hardlink", "sha256": "430db3f8fb7e355f2535442bce3b375c31960961cc3e7a872f2b7c4e20f65c40", "sha256_in_prefix": "430db3f8fb7e355f2535442bce3b375c31960961cc3e7a872f2b7c4e20f65c40", "size_in_bytes": 8884}, {"_path": "Lib/site-packages/setuptools/_distutils/ccompiler.py", "path_type": "hardlink", "sha256": "14a563ab3189edcf85b68b8d8e12e268c3e6e4b28c6471c0aee5dff0b536d7a7", "sha256_in_prefix": "14a563ab3189edcf85b68b8d8e12e268c3e6e4b28c6471c0aee5dff0b536d7a7", "size_in_bytes": 524}, {"_path": "Lib/site-packages/setuptools/_distutils/cmd.py", "path_type": "hardlink", "sha256": "857b5a45a1fb4019df34e22a12f0ade3b8b06730fd315bc176185d41cb47b313", "sha256_in_prefix": "857b5a45a1fb4019df34e22a12f0ade3b8b06730fd315bc176185d41cb47b313", "size_in_bytes": 22186}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__init__.py", "path_type": "hardlink", "sha256": "19f140cdb06a935ab1487e0175a2a2a0a4b88514670f8e01026c0437ce42e2ef", "sha256_in_prefix": "19f140cdb06a935ab1487e0175a2a2a0a4b88514670f8e01026c0437ce42e2ef", "size_in_bytes": 386}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "cc4ca3cecc483bcd2697fcfe1b78940f810e9f9cd61e2fda3443b556b9bc4cd1", "sha256_in_prefix": "cc4ca3cecc483bcd2697fcfe1b78940f810e9f9cd61e2fda3443b556b9bc4cd1", "size_in_bytes": 451}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/_framework_compat.cpython-39.pyc", "path_type": "hardlink", "sha256": "7eacc0454e86679199d85f6e4cfbec7b48133544585c92d4d70e1dde49e7abe2", "sha256_in_prefix": "7eacc0454e86679199d85f6e4cfbec7b48133544585c92d4d70e1dde49e7abe2", "size_in_bytes": 1872}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/bdist.cpython-39.pyc", "path_type": "hardlink", "sha256": "fca6f6460a5adaf885e4f23b96b98c80370461c7e8626fce8ea0c653fc748876", "sha256_in_prefix": "fca6f6460a5adaf885e4f23b96b98c80370461c7e8626fce8ea0c653fc748876", "size_in_bytes": 4775}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/bdist_dumb.cpython-39.pyc", "path_type": "hardlink", "sha256": "d6af6267f19a7c1242cd66463a90cc4aa74cb95f8bb499e054245cc3859b1930", "sha256_in_prefix": "d6af6267f19a7c1242cd66463a90cc4aa74cb95f8bb499e054245cc3859b1930", "size_in_bytes": 3643}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/bdist_rpm.cpython-39.pyc", "path_type": "hardlink", "sha256": "16b18bd6be218cd637c78af7f9ce06bc4fd1b8fd75edac91e2283067a603003e", "sha256_in_prefix": "16b18bd6be218cd637c78af7f9ce06bc4fd1b8fd75edac91e2283067a603003e", "size_in_bytes": 12375}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/build.cpython-39.pyc", "path_type": "hardlink", "sha256": "402da887d58c218372c182f7e9580f46a2f5c3632710197f5bea0f0cc502f267", "sha256_in_prefix": "402da887d58c218372c182f7e9580f46a2f5c3632710197f5bea0f0cc502f267", "size_in_bytes": 4141}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_clib.cpython-39.pyc", "path_type": "hardlink", "sha256": "717d047cf3995e445611d66f67e20227b54b75051c7e060d7fdbe8b507128f69", "sha256_in_prefix": "717d047cf3995e445611d66f67e20227b54b75051c7e060d7fdbe8b507128f69", "size_in_bytes": 5020}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_ext.cpython-39.pyc", "path_type": "hardlink", "sha256": "d05acaa826f9468b848eb5766aca742019485f5ebb19156f80d7f7a7c01908bd", "sha256_in_prefix": "d05acaa826f9468b848eb5766aca742019485f5ebb19156f80d7f7a7c01908bd", "size_in_bytes": 17341}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_py.cpython-39.pyc", "path_type": "hardlink", "sha256": "1dd78e8c59756c2894745b63efcdc10ea60322ce9a0c89ab702ba0197efd9f73", "sha256_in_prefix": "1dd78e8c59756c2894745b63efcdc10ea60322ce9a0c89ab702ba0197efd9f73", "size_in_bytes": 9921}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_scripts.cpython-39.pyc", "path_type": "hardlink", "sha256": "9cf0e6fd414ee271992b534509aed518748bdc5754312e828bb1019fe3e5028b", "sha256_in_prefix": "9cf0e6fd414ee271992b534509aed518748bdc5754312e828bb1019fe3e5028b", "size_in_bytes": 4453}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/check.cpython-39.pyc", "path_type": "hardlink", "sha256": "a5368a7dd9c8b39e0656ecf0de1b59613007801acfacf5d64a87e285283f91ef", "sha256_in_prefix": "a5368a7dd9c8b39e0656ecf0de1b59613007801acfacf5d64a87e285283f91ef", "size_in_bytes": 4838}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/clean.cpython-39.pyc", "path_type": "hardlink", "sha256": "f3d2cf9e91642064e4ada2ffeabea477269b547769753390ee419d174e6f7909", "sha256_in_prefix": "f3d2cf9e91642064e4ada2ffeabea477269b547769753390ee419d174e6f7909", "size_in_bytes": 2163}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/config.cpython-39.pyc", "path_type": "hardlink", "sha256": "444d8e80065834a03d88cc06ff1a30237a04b0f9ab6c050564b3aa2fe4a2294f", "sha256_in_prefix": "444d8e80065834a03d88cc06ff1a30237a04b0f9ab6c050564b3aa2fe4a2294f", "size_in_bytes": 10348}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/install.cpython-39.pyc", "path_type": "hardlink", "sha256": "ac775871e4819b759a46c9d09f9ca41e7ae59f94f1eb5c7229527efde08a4509", "sha256_in_prefix": "ac775871e4819b759a46c9d09f9ca41e7ae59f94f1eb5c7229527efde08a4509", "size_in_bytes": 17019}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_data.cpython-39.pyc", "path_type": "hardlink", "sha256": "8138a69b7a9f881e16b8c4a53fcb30e69d094780a3f2af6937f9e375e4b25a1d", "sha256_in_prefix": "8138a69b7a9f881e16b8c4a53fcb30e69d094780a3f2af6937f9e375e4b25a1d", "size_in_bytes": 2967}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_egg_info.cpython-39.pyc", "path_type": "hardlink", "sha256": "47e4a3c7595277414367c384fa21a1677a73513084c37e3629d5162906d69af0", "sha256_in_prefix": "47e4a3c7595277414367c384fa21a1677a73513084c37e3629d5162906d69af0", "size_in_bytes": 3407}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_headers.cpython-39.pyc", "path_type": "hardlink", "sha256": "49f3d704d0b5553c84fd3296c4b43ea2e847d76cfbe0ff0768f52235436b120e", "sha256_in_prefix": "49f3d704d0b5553c84fd3296c4b43ea2e847d76cfbe0ff0768f52235436b120e", "size_in_bytes": 1841}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_lib.cpython-39.pyc", "path_type": "hardlink", "sha256": "6384e0b90fdbf005d4f43122a3d4ce332d58586d658723134e69482f26ecbb29", "sha256_in_prefix": "6384e0b90fdbf005d4f43122a3d4ce332d58586d658723134e69482f26ecbb29", "size_in_bytes": 5460}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_scripts.cpython-39.pyc", "path_type": "hardlink", "sha256": "8b525504392423bafe69fc0d564e9493f9aa39dfe25c410da6bb132183e39e82", "sha256_in_prefix": "8b525504392423bafe69fc0d564e9493f9aa39dfe25c410da6bb132183e39e82", "size_in_bytes": 2257}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/sdist.cpython-39.pyc", "path_type": "hardlink", "sha256": "ad7f2b3fc05dc1df9d8a6c13aaee89929c1874ab5a4b155114bda93255bcbcda", "sha256_in_prefix": "ad7f2b3fc05dc1df9d8a6c13aaee89929c1874ab5a4b155114bda93255bcbcda", "size_in_bytes": 14847}, {"_path": "Lib/site-packages/setuptools/_distutils/command/_framework_compat.py", "path_type": "hardlink", "sha256": "d2265d4896331915820afcd10ca13e474fbfc9a018bc531dd729576f67985ee8", "sha256_in_prefix": "d2265d4896331915820afcd10ca13e474fbfc9a018bc531dd729576f67985ee8", "size_in_bytes": 1609}, {"_path": "Lib/site-packages/setuptools/_distutils/command/bdist.py", "path_type": "hardlink", "sha256": "8d6b64eb547b7d635450dc49574b614d9cd4e67f342f7032d7069288ff6488b0", "sha256_in_prefix": "8d6b64eb547b7d635450dc49574b614d9cd4e67f342f7032d7069288ff6488b0", "size_in_bytes": 5854}, {"_path": "Lib/site-packages/setuptools/_distutils/command/bdist_dumb.py", "path_type": "hardlink", "sha256": "1f1d6302aa19371608cb83794cbcd4a7a2797e2f0bb35f29cbb5252cd1613b61", "sha256_in_prefix": "1f1d6302aa19371608cb83794cbcd4a7a2797e2f0bb35f29cbb5252cd1613b61", "size_in_bytes": 4631}, {"_path": "Lib/site-packages/setuptools/_distutils/command/bdist_rpm.py", "path_type": "hardlink", "sha256": "9f17175efe5aec1fb59ed5aee036c6982b444b810120dac968141c44d0180892", "sha256_in_prefix": "9f17175efe5aec1fb59ed5aee036c6982b444b810120dac968141c44d0180892", "size_in_bytes": 21785}, {"_path": "Lib/site-packages/setuptools/_distutils/command/build.py", "path_type": "hardlink", "sha256": "4a91e56a07f488d68a572221c437e13c567c5f5f8b0163824000b2fb2b762b4c", "sha256_in_prefix": "4a91e56a07f488d68a572221c437e13c567c5f5f8b0163824000b2fb2b762b4c", "size_in_bytes": 5923}, {"_path": "Lib/site-packages/setuptools/_distutils/command/build_clib.py", "path_type": "hardlink", "sha256": "68ca997147c26ce02eff1afe03d896f90f58647ce90c62d14decce80c4099924", "sha256_in_prefix": "68ca997147c26ce02eff1afe03d896f90f58647ce90c62d14decce80c4099924", "size_in_bytes": 7777}, {"_path": "Lib/site-packages/setuptools/_distutils/command/build_ext.py", "path_type": "hardlink", "sha256": "cebaecbbd1d79f357a6d761b26e6422b84b05593232a7978a46d68ddb35cc6d7", "sha256_in_prefix": "cebaecbbd1d79f357a6d761b26e6422b84b05593232a7978a46d68ddb35cc6d7", "size_in_bytes": 32710}, {"_path": "Lib/site-packages/setuptools/_distutils/command/build_py.py", "path_type": "hardlink", "sha256": "55fabe20d7a6a0c6e0e9fd34dc14f2fd47e9f1b8ce661985221a4a31c7d72e0b", "sha256_in_prefix": "55fabe20d7a6a0c6e0e9fd34dc14f2fd47e9f1b8ce661985221a4a31c7d72e0b", "size_in_bytes": 16696}, {"_path": "Lib/site-packages/setuptools/_distutils/command/build_scripts.py", "path_type": "hardlink", "sha256": "b54a44cf04ec9eb3fcaab368af2de574f076e3440308590ca7ea5d60fb36c139", "sha256_in_prefix": "b54a44cf04ec9eb3fcaab368af2de574f076e3440308590ca7ea5d60fb36c139", "size_in_bytes": 5118}, {"_path": "Lib/site-packages/setuptools/_distutils/command/check.py", "path_type": "hardlink", "sha256": "ca835ed8c3d8e0971333baf0a0841d7d9ef9ab9462d39f08d9ca22f86abd0a33", "sha256_in_prefix": "ca835ed8c3d8e0971333baf0a0841d7d9ef9ab9462d39f08d9ca22f86abd0a33", "size_in_bytes": 4946}, {"_path": "Lib/site-packages/setuptools/_distutils/command/clean.py", "path_type": "hardlink", "sha256": "75001a70e69bc015d4f49a19fb5185bacab778596d0da7972454989dca866ef1", "sha256_in_prefix": "75001a70e69bc015d4f49a19fb5185bacab778596d0da7972454989dca866ef1", "size_in_bytes": 2644}, {"_path": "Lib/site-packages/setuptools/_distutils/command/config.py", "path_type": "hardlink", "sha256": "06e51d3eef75568f70e38c730f54507e2c977d27d570da5e5f769ea0a70600ec", "sha256_in_prefix": "06e51d3eef75568f70e38c730f54507e2c977d27d570da5e5f769ea0a70600ec", "size_in_bytes": 12818}, {"_path": "Lib/site-packages/setuptools/_distutils/command/install.py", "path_type": "hardlink", "sha256": "f897a707e9ae6b885cd9123ff96f05f4f9cffc9f8e6853bb1343c918ac4ba35a", "sha256_in_prefix": "f897a707e9ae6b885cd9123ff96f05f4f9cffc9f8e6853bb1343c918ac4ba35a", "size_in_bytes": 30072}, {"_path": "Lib/site-packages/setuptools/_distutils/command/install_data.py", "path_type": "hardlink", "sha256": "1b306551658ab9b4d82653fe2f46ae52b8aaf5c2fee5128e728c874edb4a8f44", "sha256_in_prefix": "1b306551658ab9b4d82653fe2f46ae52b8aaf5c2fee5128e728c874edb4a8f44", "size_in_bytes": 2875}, {"_path": "Lib/site-packages/setuptools/_distutils/command/install_egg_info.py", "path_type": "hardlink", "sha256": "7df88ba14d62bd027cab6fd62fb6728196d470eb207452ca2fba2d1082565a42", "sha256_in_prefix": "7df88ba14d62bd027cab6fd62fb6728196d470eb207452ca2fba2d1082565a42", "size_in_bytes": 2868}, {"_path": "Lib/site-packages/setuptools/_distutils/command/install_headers.py", "path_type": "hardlink", "sha256": "e5c88a0a3f1cdd72ac60d29d91d32f9f2a5a50229ca1608379e6628f77c3f99e", "sha256_in_prefix": "e5c88a0a3f1cdd72ac60d29d91d32f9f2a5a50229ca1608379e6628f77c3f99e", "size_in_bytes": 1272}, {"_path": "Lib/site-packages/setuptools/_distutils/command/install_lib.py", "path_type": "hardlink", "sha256": "dacf7e9b9f9bd6a2a6e75176f250792f7f59eafbff187325bfd74d052ba9a24d", "sha256_in_prefix": "dacf7e9b9f9bd6a2a6e75176f250792f7f59eafbff187325bfd74d052ba9a24d", "size_in_bytes": 8588}, {"_path": "Lib/site-packages/setuptools/_distutils/command/install_scripts.py", "path_type": "hardlink", "sha256": "334a4f7626aa07b4c69aa4ccba3a4619e88bd08abf0937868cc16dae60e6c333", "sha256_in_prefix": "334a4f7626aa07b4c69aa4ccba3a4619e88bd08abf0937868cc16dae60e6c333", "size_in_bytes": 2002}, {"_path": "Lib/site-packages/setuptools/_distutils/command/sdist.py", "path_type": "hardlink", "sha256": "711205e87b75849e9ac8e38557270c14150dc63a3de1efeb1583f1e078bc0217", "sha256_in_prefix": "711205e87b75849e9ac8e38557270c14150dc63a3de1efeb1583f1e078bc0217", "size_in_bytes": 19151}, {"_path": "Lib/site-packages/setuptools/_distutils/compat/__init__.py", "path_type": "hardlink", "sha256": "276d1a5c68c9f3a460e35c452c85a57160a067d79d31d27dbef74d110f3bbcf4", "sha256_in_prefix": "276d1a5c68c9f3a460e35c452c85a57160a067d79d31d27dbef74d110f3bbcf4", "size_in_bytes": 522}, {"_path": "Lib/site-packages/setuptools/_distutils/compat/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "805559b1abe7f512cd42d8d32c7d65f9b917af2b76a3d37c29256dd1d97ad7e5", "sha256_in_prefix": "805559b1abe7f512cd42d8d32c7d65f9b917af2b76a3d37c29256dd1d97ad7e5", "size_in_bytes": 972}, {"_path": "Lib/site-packages/setuptools/_distutils/compat/__pycache__/numpy.cpython-39.pyc", "path_type": "hardlink", "sha256": "85db6f8a1b0586d2f2741a4e5c2152f14933e1f3a0f679add064acd4149e526d", "sha256_in_prefix": "85db6f8a1b0586d2f2741a4e5c2152f14933e1f3a0f679add064acd4149e526d", "size_in_bytes": 230}, {"_path": "Lib/site-packages/setuptools/_distutils/compat/__pycache__/py39.cpython-39.pyc", "path_type": "hardlink", "sha256": "0a14f5c8abfbd33609538195378f5beff4419b65bebccdb5f083fdce9e1e303a", "sha256_in_prefix": "0a14f5c8abfbd33609538195378f5beff4419b65bebccdb5f083fdce9e1e303a", "size_in_bytes": 1865}, {"_path": "Lib/site-packages/setuptools/_distutils/compat/numpy.py", "path_type": "hardlink", "sha256": "505827799c3dc3dee0e1cfb21a80083b22f150e590f9f3d122185f32ceff3ae7", "sha256_in_prefix": "505827799c3dc3dee0e1cfb21a80083b22f150e590f9f3d122185f32ceff3ae7", "size_in_bytes": 167}, {"_path": "Lib/site-packages/setuptools/_distutils/compat/py39.py", "path_type": "hardlink", "sha256": "84eb03ea5c192ea66832769c349dcfea7500f8b250844a55b584f3547d28f7a3", "sha256_in_prefix": "84eb03ea5c192ea66832769c349dcfea7500f8b250844a55b584f3547d28f7a3", "size_in_bytes": 1964}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/base.cpython-39.pyc", "path_type": "hardlink", "sha256": "943b283110019a001b1328fa657d1fde2398e47c52dc7560c51be5d05c53c291", "sha256_in_prefix": "943b283110019a001b1328fa657d1fde2398e47c52dc7560c51be5d05c53c291", "size_in_bytes": 39847}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/cygwin.cpython-39.pyc", "path_type": "hardlink", "sha256": "36db9784e59a75e3f16595b8707436517c6c5e599ef489a3b6c126a1278cbfad", "sha256_in_prefix": "36db9784e59a75e3f16595b8707436517c6c5e599ef489a3b6c126a1278cbfad", "size_in_bytes": 8036}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/errors.cpython-39.pyc", "path_type": "hardlink", "sha256": "06f720bc7a477a81920a4b00bd4c93a86043c3e599b2e1aefc006d236de8593b", "sha256_in_prefix": "06f720bc7a477a81920a4b00bd4c93a86043c3e599b2e1aefc006d236de8593b", "size_in_bytes": 1335}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/msvc.cpython-39.pyc", "path_type": "hardlink", "sha256": "cb88bfcca3ccb97c42c62786a66e41fbb78ede4102f4e0611196f78927fc7a70", "sha256_in_prefix": "cb88bfcca3ccb97c42c62786a66e41fbb78ede4102f4e0611196f78927fc7a70", "size_in_bytes": 15152}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/unix.cpython-39.pyc", "path_type": "hardlink", "sha256": "1fb0c3886899a23310b11f714621d94ce96aa9a84083c1707d34c4779ab660c0", "sha256_in_prefix": "1fb0c3886899a23310b11f714621d94ce96aa9a84083c1707d34c4779ab660c0", "size_in_bytes": 10942}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/zos.cpython-39.pyc", "path_type": "hardlink", "sha256": "8673ab5edf9c794815a88dafc0024ce8abcbbfb468746edc3f22620e322a86b1", "sha256_in_prefix": "8673ab5edf9c794815a88dafc0024ce8abcbbfb468746edc3f22620e322a86b1", "size_in_bytes": 4182}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/base.py", "path_type": "hardlink", "sha256": "5d1d6b0424ad0aabaa9bb40e6170f8d7e2dfbec15c3e91b1af0c5e5f32729ffc", "sha256_in_prefix": "5d1d6b0424ad0aabaa9bb40e6170f8d7e2dfbec15c3e91b1af0c5e5f32729ffc", "size_in_bytes": 54876}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/cygwin.py", "path_type": "hardlink", "sha256": "0d49704126f9e5a8fb39d72671d76b98299512311ac48889e611d43b71813cdb", "sha256_in_prefix": "0d49704126f9e5a8fb39d72671d76b98299512311ac48889e611d43b71813cdb", "size_in_bytes": 11844}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/errors.py", "path_type": "hardlink", "sha256": "b0a395cc96a331498d75fcb0a3d50cfd0406b0a15c7250e1b48e5394289730b7", "sha256_in_prefix": "b0a395cc96a331498d75fcb0a3d50cfd0406b0a15c7250e1b48e5394289730b7", "size_in_bytes": 573}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/msvc.py", "path_type": "hardlink", "sha256": "3bcce8e8d2830300aebf917414f65e02ec986fb0055c82ede4db676463e5c8d8", "sha256_in_prefix": "3bcce8e8d2830300aebf917414f65e02ec986fb0055c82ede4db676463e5c8d8", "size_in_bytes": 21802}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_base.cpython-39.pyc", "path_type": "hardlink", "sha256": "bddd156be2f15f921d396af6e3b40e388cb5c36b94caf81a67542053cdf161aa", "sha256_in_prefix": "bddd156be2f15f921d396af6e3b40e388cb5c36b94caf81a67542053cdf161aa", "size_in_bytes": 2383}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_cygwin.cpython-39.pyc", "path_type": "hardlink", "sha256": "15314a9f70a258941646ddd3dda3f9f98f84f80e3a2640454f1b46b716a9ac91", "sha256_in_prefix": "15314a9f70a258941646ddd3dda3f9f98f84f80e3a2640454f1b46b716a9ac91", "size_in_bytes": 2895}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_mingw.cpython-39.pyc", "path_type": "hardlink", "sha256": "81b935b6c75616d213abb298bd714a9c60ec5d52984d56630945bf4094a3756f", "sha256_in_prefix": "81b935b6c75616d213abb298bd714a9c60ec5d52984d56630945bf4094a3756f", "size_in_bytes": 2406}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_msvc.cpython-39.pyc", "path_type": "hardlink", "sha256": "267391c67fac7dad341b93d9a0b681435f478c01c6a1e7f3c2c77c9b4967bc16", "sha256_in_prefix": "267391c67fac7dad341b93d9a0b681435f478c01c6a1e7f3c2c77c9b4967bc16", "size_in_bytes": 5073}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_unix.cpython-39.pyc", "path_type": "hardlink", "sha256": "e563e809684e1cab98d959e83bd9bfcc0c57094c46778c77423e7caad85eac0b", "sha256_in_prefix": "e563e809684e1cab98d959e83bd9bfcc0c57094c46778c77423e7caad85eac0b", "size_in_bytes": 9607}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_base.py", "path_type": "hardlink", "sha256": "add847739e9b857b66e4d9cdf41487c2be9cebd52accc22d650ce5c3602c74c7", "sha256_in_prefix": "add847739e9b857b66e4d9cdf41487c2be9cebd52accc22d650ce5c3602c74c7", "size_in_bytes": 2706}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_cygwin.py", "path_type": "hardlink", "sha256": "5205765605178f756e95c6c373450159f132243c78dad812c12e0bcc78b1de66", "sha256_in_prefix": "5205765605178f756e95c6c373450159f132243c78dad812c12e0bcc78b1de66", "size_in_bytes": 2701}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_mingw.py", "path_type": "hardlink", "sha256": "8429b0cb2c084a9468c8ec926c51c12f84e9ad6455d265160ca98e2cef170571", "sha256_in_prefix": "8429b0cb2c084a9468c8ec926c51c12f84e9ad6455d265160ca98e2cef170571", "size_in_bytes": 1900}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_msvc.py", "path_type": "hardlink", "sha256": "0e51a3999d660523172209a5bbcd0129ced5f8424e66e62e730270161e5d9f6f", "sha256_in_prefix": "0e51a3999d660523172209a5bbcd0129ced5f8424e66e62e730270161e5d9f6f", "size_in_bytes": 4151}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_unix.py", "path_type": "hardlink", "sha256": "12b6d85a2a3b7a363666a4263e4e00c0ebb51c55b8fbff9a65d52f19ad56d85c", "sha256_in_prefix": "12b6d85a2a3b7a363666a4263e4e00c0ebb51c55b8fbff9a65d52f19ad56d85c", "size_in_bytes": 11834}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/unix.py", "path_type": "hardlink", "sha256": "97b0b1638ac3240102268faf72fea2a344819a63c9f4998de664a665c8a7d955", "sha256_in_prefix": "97b0b1638ac3240102268faf72fea2a344819a63c9f4998de664a665c8a7d955", "size_in_bytes": 16502}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/zos.py", "path_type": "hardlink", "sha256": "be735e58b45991d224759f98c819cbf2275351f7023a7d2d2cc5b938127449c5", "sha256_in_prefix": "be735e58b45991d224759f98c819cbf2275351f7023a7d2d2cc5b938127449c5", "size_in_bytes": 6586}, {"_path": "Lib/site-packages/setuptools/_distutils/core.py", "path_type": "hardlink", "sha256": "1841ca6850b8f13de8fbf4a61f8f3ae06bcacb1d4881b542e884883d5971edae", "sha256_in_prefix": "1841ca6850b8f13de8fbf4a61f8f3ae06bcacb1d4881b542e884883d5971edae", "size_in_bytes": 9364}, {"_path": "Lib/site-packages/setuptools/_distutils/cygwinccompiler.py", "path_type": "hardlink", "sha256": "986fdc53c4956786a60ff56d179bc7e815cfd3e920846b033db0d25eb43deb77", "sha256_in_prefix": "986fdc53c4956786a60ff56d179bc7e815cfd3e920846b033db0d25eb43deb77", "size_in_bytes": 594}, {"_path": "Lib/site-packages/setuptools/_distutils/debug.py", "path_type": "hardlink", "sha256": "37a32b4c0a8aea5f52564ead5b0791d74f0f33c3a5eea3657f257e9c770b86c6", "sha256_in_prefix": "37a32b4c0a8aea5f52564ead5b0791d74f0f33c3a5eea3657f257e9c770b86c6", "size_in_bytes": 139}, {"_path": "Lib/site-packages/setuptools/_distutils/dep_util.py", "path_type": "hardlink", "sha256": "c4def9a7a6691e13221c473eae92f65e29494329c79c336269f1ed79a678b635", "sha256_in_prefix": "c4def9a7a6691e13221c473eae92f65e29494329c79c336269f1ed79a678b635", "size_in_bytes": 349}, {"_path": "Lib/site-packages/setuptools/_distutils/dir_util.py", "path_type": "hardlink", "sha256": "0d73d495f5551ac83d07e26083802dfe3f53eef33ad0e8303579101ea4e8efe2", "sha256_in_prefix": "0d73d495f5551ac83d07e26083802dfe3f53eef33ad0e8303579101ea4e8efe2", "size_in_bytes": 7236}, {"_path": "Lib/site-packages/setuptools/_distutils/dist.py", "path_type": "hardlink", "sha256": "816e7df1413458c9335d0437d4dafef0becc3f0d2820ecf9392491cd8665c2b3", "sha256_in_prefix": "816e7df1413458c9335d0437d4dafef0becc3f0d2820ecf9392491cd8665c2b3", "size_in_bytes": 55794}, {"_path": "Lib/site-packages/setuptools/_distutils/errors.py", "path_type": "hardlink", "sha256": "3cf136a03461e72f50d5b78a2bdae176f0da0b34218b81c25ece0a72a7ea8196", "sha256_in_prefix": "3cf136a03461e72f50d5b78a2bdae176f0da0b34218b81c25ece0a72a7ea8196", "size_in_bytes": 3092}, {"_path": "Lib/site-packages/setuptools/_distutils/extension.py", "path_type": "hardlink", "sha256": "168caee2050b70faa6d7f53dceb6181f1364e0daa0957bf5adbb0e93f42b49db", "sha256_in_prefix": "168caee2050b70faa6d7f53dceb6181f1364e0daa0957bf5adbb0e93f42b49db", "size_in_bytes": 11155}, {"_path": "Lib/site-packages/setuptools/_distutils/fancy_getopt.py", "path_type": "hardlink", "sha256": "3e374ef9b5825b48a657f50df8c184c3d47618fd8e884f291e32138264c06374", "sha256_in_prefix": "3e374ef9b5825b48a657f50df8c184c3d47618fd8e884f291e32138264c06374", "size_in_bytes": 17895}, {"_path": "Lib/site-packages/setuptools/_distutils/file_util.py", "path_type": "hardlink", "sha256": "60540bfe90f784bb8447d208fc7ebe8430bf608184a2891d778c1e74bba24d6d", "sha256_in_prefix": "60540bfe90f784bb8447d208fc7ebe8430bf608184a2891d778c1e74bba24d6d", "size_in_bytes": 7978}, {"_path": "Lib/site-packages/setuptools/_distutils/filelist.py", "path_type": "hardlink", "sha256": "30179244998f70a983bfca28660494e018903d9d0a870bfc97f8e10f9d17c9c2", "sha256_in_prefix": "30179244998f70a983bfca28660494e018903d9d0a870bfc97f8e10f9d17c9c2", "size_in_bytes": 15337}, {"_path": "Lib/site-packages/setuptools/_distutils/log.py", "path_type": "hardlink", "sha256": "57206ce63ef3e3e2ba5d310405385473d1f2329a0f2c6b50a4446a6f3e72970c", "sha256_in_prefix": "57206ce63ef3e3e2ba5d310405385473d1f2329a0f2c6b50a4446a6f3e72970c", "size_in_bytes": 1200}, {"_path": "Lib/site-packages/setuptools/_distutils/spawn.py", "path_type": "hardlink", "sha256": "cec78287db0489fca9d08e5583bd7d24d2004a544e2767a15ea4271e5a6df3d4", "sha256_in_prefix": "cec78287db0489fca9d08e5583bd7d24d2004a544e2767a15ea4271e5a6df3d4", "size_in_bytes": 4086}, {"_path": "Lib/site-packages/setuptools/_distutils/sysconfig.py", "path_type": "hardlink", "sha256": "29e23c3876ccb84cc727c4347017b3f4a667cbc891cba67a634024333d6396c5", "sha256_in_prefix": "29e23c3876ccb84cc727c4347017b3f4a667cbc891cba67a634024333d6396c5", "size_in_bytes": 19728}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__init__.py", "path_type": "hardlink", "sha256": "8fe2283d912d42fdc438fbaa353c1a96be862f2463cc20be38e68dbd9ce61ec2", "sha256_in_prefix": "8fe2283d912d42fdc438fbaa353c1a96be862f2463cc20be38e68dbd9ce61ec2", "size_in_bytes": 1485}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "ab98f2cc61e353bbca21de0c3113b9174f3891d22832d33968efe63f72f6cb0e", "sha256_in_prefix": "ab98f2cc61e353bbca21de0c3113b9174f3891d22832d33968efe63f72f6cb0e", "size_in_bytes": 1471}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/support.cpython-39.pyc", "path_type": "hardlink", "sha256": "ce11610716b82123238df51a580da1158b664c0496908166bf332dc6d93392ec", "sha256_in_prefix": "ce11610716b82123238df51a580da1158b664c0496908166bf332dc6d93392ec", "size_in_bytes": 5050}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_archive_util.cpython-39.pyc", "path_type": "hardlink", "sha256": "5e0afaddd24ac7faeafe9d6f36fa3fc6cb11a8f58c7faa944bdf2dc26d8fbff3", "sha256_in_prefix": "5e0afaddd24ac7faeafe9d6f36fa3fc6cb11a8f58c7faa944bdf2dc26d8fbff3", "size_in_bytes": 10948}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist.cpython-39.pyc", "path_type": "hardlink", "sha256": "3655f30f26fcf3ab9ec7ebe202fb1c0d02d84164fd10938fdd2f7bb417bf9da8", "sha256_in_prefix": "3655f30f26fcf3ab9ec7ebe202fb1c0d02d84164fd10938fdd2f7bb417bf9da8", "size_in_bytes": 1280}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist_dumb.cpython-39.pyc", "path_type": "hardlink", "sha256": "f8bbcfa954f2bc46f71f85aa234b410e0a6b93fbaaac7d6046dca42b006e9b88", "sha256_in_prefix": "f8bbcfa954f2bc46f71f85aa234b410e0a6b93fbaaac7d6046dca42b006e9b88", "size_in_bytes": 2082}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist_rpm.cpython-39.pyc", "path_type": "hardlink", "sha256": "9b2152b3502677c68ef71246a37c6b3026d3392eb4a1558694f6c0c501e90bea", "sha256_in_prefix": "9b2152b3502677c68ef71246a37c6b3026d3392eb4a1558694f6c0c501e90bea", "size_in_bytes": 2986}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build.cpython-39.pyc", "path_type": "hardlink", "sha256": "b6b785b35120fb5a89ac05d8932394497362541950b9e331b46f2636bde68885", "sha256_in_prefix": "b6b785b35120fb5a89ac05d8932394497362541950b9e331b46f2636bde68885", "size_in_bytes": 1438}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_clib.cpython-39.pyc", "path_type": "hardlink", "sha256": "90ccce0e24f34aeb3345de08a2d17be65b48ce2255123febd9238ae1481def22", "sha256_in_prefix": "90ccce0e24f34aeb3345de08a2d17be65b48ce2255123febd9238ae1481def22", "size_in_bytes": 3711}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_ext.cpython-39.pyc", "path_type": "hardlink", "sha256": "ed01844cc2c9db8b780c883e811ab62b1134598c598bfc8d4642d73238a031de", "sha256_in_prefix": "ed01844cc2c9db8b780c883e811ab62b1134598c598bfc8d4642d73238a031de", "size_in_bytes": 15414}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_py.cpython-39.pyc", "path_type": "hardlink", "sha256": "5e13f9d78dbf191f1ea0ac1a798ea788f63c57c73deff897781c45494b96a77e", "sha256_in_prefix": "5e13f9d78dbf191f1ea0ac1a798ea788f63c57c73deff897781c45494b96a77e", "size_in_bytes": 5547}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_scripts.cpython-39.pyc", "path_type": "hardlink", "sha256": "54c7410faad185ac12908d9babb0a39dbbc98f3c0e54b23880bb7d454370d3c0", "sha256_in_prefix": "54c7410faad185ac12908d9babb0a39dbbc98f3c0e54b23880bb7d454370d3c0", "size_in_bytes": 3103}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_check.cpython-39.pyc", "path_type": "hardlink", "sha256": "440d733e9d58124680148ed14d9a704d0ae5cbb244e4bd00eb9c6145dbdf898d", "sha256_in_prefix": "440d733e9d58124680148ed14d9a704d0ae5cbb244e4bd00eb9c6145dbdf898d", "size_in_bytes": 4382}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_clean.cpython-39.pyc", "path_type": "hardlink", "sha256": "e6c4a01f5b228c9da7e67fcd8ab5e369cb225a8e48ff96297b34c7917049f3e3", "sha256_in_prefix": "e6c4a01f5b228c9da7e67fcd8ab5e369cb225a8e48ff96297b34c7917049f3e3", "size_in_bytes": 1289}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_cmd.cpython-39.pyc", "path_type": "hardlink", "sha256": "81bebe9b79c6e0e115e4b5fce08e5e5df574f5d2c2c747303a8a88ed3a94ba9c", "sha256_in_prefix": "81bebe9b79c6e0e115e4b5fce08e5e5df574f5d2c2c747303a8a88ed3a94ba9c", "size_in_bytes": 4029}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_config_cmd.cpython-39.pyc", "path_type": "hardlink", "sha256": "65a0da501ba8c8e134061eaa09a3d8787ba6c21e666d648b6555e46c4552296e", "sha256_in_prefix": "65a0da501ba8c8e134061eaa09a3d8787ba6c21e666d648b6555e46c4552296e", "size_in_bytes": 3003}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_core.cpython-39.pyc", "path_type": "hardlink", "sha256": "3d0147f67150952ea75fcb03b5b5b3658c10e4ae4606c9e390d796acd413cc12", "sha256_in_prefix": "3d0147f67150952ea75fcb03b5b5b3658c10e4ae4606c9e390d796acd413cc12", "size_in_bytes": 3948}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_dir_util.cpython-39.pyc", "path_type": "hardlink", "sha256": "0eb9d20783f10ab16dfa97242444f5dd84143a11ac6d4c0b9cf080594845ef8f", "sha256_in_prefix": "0eb9d20783f10ab16dfa97242444f5dd84143a11ac6d4c0b9cf080594845ef8f", "size_in_bytes": 4901}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_dist.cpython-39.pyc", "path_type": "hardlink", "sha256": "9ba0f30a947514e816db882663797259e50d135b1fbbc606429d6368a92340b1", "sha256_in_prefix": "9ba0f30a947514e816db882663797259e50d135b1fbbc606429d6368a92340b1", "size_in_bytes": 17136}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_extension.cpython-39.pyc", "path_type": "hardlink", "sha256": "cc59aed481d89730949d932713ef8195ae2241367438e4d52ce5ad6432b889c7", "sha256_in_prefix": "cc59aed481d89730949d932713ef8195ae2241367438e4d52ce5ad6432b889c7", "size_in_bytes": 2590}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_file_util.cpython-39.pyc", "path_type": "hardlink", "sha256": "18bfe62cf4ead279e6bf17801ecc6066c687520d7718b3dff602707c929308cc", "sha256_in_prefix": "18bfe62cf4ead279e6bf17801ecc6066c687520d7718b3dff602707c929308cc", "size_in_bytes": 3419}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_filelist.cpython-39.pyc", "path_type": "hardlink", "sha256": "4546fb73fa0d5ba7b68227644a89d253032f4527ac61737996c62d8b5bf9895e", "sha256_in_prefix": "4546fb73fa0d5ba7b68227644a89d253032f4527ac61737996c62d8b5bf9895e", "size_in_bytes": 8316}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install.cpython-39.pyc", "path_type": "hardlink", "sha256": "4af55ddff09abc03b2664a77cd348920f6a71ab01b3383f0d410f2beffe3e37a", "sha256_in_prefix": "4af55ddff09abc03b2664a77cd348920f6a71ab01b3383f0d410f2beffe3e37a", "size_in_bytes": 7397}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_data.cpython-39.pyc", "path_type": "hardlink", "sha256": "1806be9f24b9cbe9a07255a5397caa268001eadc441ca8c12fc6e53d2df08553", "sha256_in_prefix": "1806be9f24b9cbe9a07255a5397caa268001eadc441ca8c12fc6e53d2df08553", "size_in_bytes": 1848}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_headers.cpython-39.pyc", "path_type": "hardlink", "sha256": "5b21c3db3b87dce8428f3dd86e038020fd7fbb9d67cc02148fb1c0ca1f5f933c", "sha256_in_prefix": "5b21c3db3b87dce8428f3dd86e038020fd7fbb9d67cc02148fb1c0ca1f5f933c", "size_in_bytes": 1134}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_lib.cpython-39.pyc", "path_type": "hardlink", "sha256": "806da60864f5b36ffd29d91bd513de62a38cd11625baded53036ca6dba8c965e", "sha256_in_prefix": "806da60864f5b36ffd29d91bd513de62a38cd11625baded53036ca6dba8c965e", "size_in_bytes": 3277}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_scripts.cpython-39.pyc", "path_type": "hardlink", "sha256": "4dcb47687132a2d012f47c39bd957bf083e3b47aba6ed34fe3df41e2a1959738", "sha256_in_prefix": "4dcb47687132a2d012f47c39bd957bf083e3b47aba6ed34fe3df41e2a1959738", "size_in_bytes": 1649}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_log.cpython-39.pyc", "path_type": "hardlink", "sha256": "dad0a23d19809a34466efb5be8612fab8c8866b80cc5fcbc20e1d3556447c362", "sha256_in_prefix": "dad0a23d19809a34466efb5be8612fab8c8866b80cc5fcbc20e1d3556447c362", "size_in_bytes": 685}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_modified.cpython-39.pyc", "path_type": "hardlink", "sha256": "2b8058a1665339455fafafb599e4b4d9849e1cc95df26cff47bdde56c4ac0ff4", "sha256_in_prefix": "2b8058a1665339455fafafb599e4b4d9849e1cc95df26cff47bdde56c4ac0ff4", "size_in_bytes": 4160}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_sdist.cpython-39.pyc", "path_type": "hardlink", "sha256": "5144cd74570d51cb9e096759cd4425f7830134200527fafaf086523cd2e8f9b2", "sha256_in_prefix": "5144cd74570d51cb9e096759cd4425f7830134200527fafaf086523cd2e8f9b2", "size_in_bytes": 11205}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_spawn.cpython-39.pyc", "path_type": "hardlink", "sha256": "a22974044e8de1ffeb67e286c1d710b6ce57494c614e1af1ff57167348bf2818", "sha256_in_prefix": "a22974044e8de1ffeb67e286c1d710b6ce57494c614e1af1ff57167348bf2818", "size_in_bytes": 3528}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_sysconfig.cpython-39.pyc", "path_type": "hardlink", "sha256": "210e947d859c480d55ed60a9f460bf79721ed543984a9a1c431308f60ab8171e", "sha256_in_prefix": "210e947d859c480d55ed60a9f460bf79721ed543984a9a1c431308f60ab8171e", "size_in_bytes": 10929}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_text_file.cpython-39.pyc", "path_type": "hardlink", "sha256": "01412400093a51156a35f3a223737e97b5db1b2c5af2b0c81b0c7646843b2eeb", "sha256_in_prefix": "01412400093a51156a35f3a223737e97b5db1b2c5af2b0c81b0c7646843b2eeb", "size_in_bytes": 2143}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_util.cpython-39.pyc", "path_type": "hardlink", "sha256": "d25129be5c18b601c89db11d8942d589a495cb767fd3e38287cb49d61a9f1c77", "sha256_in_prefix": "d25129be5c18b601c89db11d8942d589a495cb767fd3e38287cb49d61a9f1c77", "size_in_bytes": 7817}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_version.cpython-39.pyc", "path_type": "hardlink", "sha256": "de90052fdf17103a8c5a25a1214029b5e38c42745f2ddc00911b42e43eca0fc1", "sha256_in_prefix": "de90052fdf17103a8c5a25a1214029b5e38c42745f2ddc00911b42e43eca0fc1", "size_in_bytes": 2436}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_versionpredicate.cpython-39.pyc", "path_type": "hardlink", "sha256": "8e86228f015ea07cae1a67603f775fce4d50e4af892d3afa8e304f30a89900f7", "sha256_in_prefix": "8e86228f015ea07cae1a67603f775fce4d50e4af892d3afa8e304f30a89900f7", "size_in_bytes": 161}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/unix_compat.cpython-39.pyc", "path_type": "hardlink", "sha256": "83b8dc05f4f854fb6a72d6c84ee117bafc3a58ee69083fb65bea4708facb6154", "sha256_in_prefix": "83b8dc05f4f854fb6a72d6c84ee117bafc3a58ee69083fb65bea4708facb6154", "size_in_bytes": 495}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/compat/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "2fdb74879abb542ce900c6c598237f4c9cd4e58184b24dcd4d9983d5311a7506", "sha256_in_prefix": "2fdb74879abb542ce900c6c598237f4c9cd4e58184b24dcd4d9983d5311a7506", "size_in_bytes": 155}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/compat/__pycache__/py39.cpython-39.pyc", "path_type": "hardlink", "sha256": "6b1cc0c50f87d4769ed0efe786e33a0ae8fe54eb10ebe4d23470925d8b84c70c", "sha256_in_prefix": "6b1cc0c50f87d4769ed0efe786e33a0ae8fe54eb10ebe4d23470925d8b84c70c", "size_in_bytes": 568}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/compat/py39.py", "path_type": "hardlink", "sha256": "b741814ccfb7d235fef7309f93094d045b73cda6de9b1eb4eb9989d1df7f551c", "sha256_in_prefix": "b741814ccfb7d235fef7309f93094d045b73cda6de9b1eb4eb9989d1df7f551c", "size_in_bytes": 1026}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/support.py", "path_type": "hardlink", "sha256": "b63b18b32c6fa532b836b902b1e876ba3bc320657431ffdbe522397cfd93d323", "sha256_in_prefix": "b63b18b32c6fa532b836b902b1e876ba3bc320657431ffdbe522397cfd93d323", "size_in_bytes": 4099}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_archive_util.py", "path_type": "hardlink", "sha256": "8e8ce2992c0f045f89a097cdfef0da895199a7ae8135c5991a1df81655b9ec34", "sha256_in_prefix": "8e8ce2992c0f045f89a097cdfef0da895199a7ae8135c5991a1df81655b9ec34", "size_in_bytes": 11787}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_bdist.py", "path_type": "hardlink", "sha256": "c4d1f152c2e51ec6504709332dbfe2483db8b3ef4c93e357d9f7c15b03b23f27", "sha256_in_prefix": "c4d1f152c2e51ec6504709332dbfe2483db8b3ef4c93e357d9f7c15b03b23f27", "size_in_bytes": 1396}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_bdist_dumb.py", "path_type": "hardlink", "sha256": "405d393073613ce759ca1f3c5e9c3c2ac3bae2cee9445925f0a2fe4685785cad", "sha256_in_prefix": "405d393073613ce759ca1f3c5e9c3c2ac3bae2cee9445925f0a2fe4685785cad", "size_in_bytes": 2247}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_bdist_rpm.py", "path_type": "hardlink", "sha256": "1dd9bea705a0c9aa067466c470665af1c461194e39cbc8072bcba639a9d38e29", "sha256_in_prefix": "1dd9bea705a0c9aa067466c470665af1c461194e39cbc8072bcba639a9d38e29", "size_in_bytes": 3932}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_build.py", "path_type": "hardlink", "sha256": "2496395e9399728db9658d29b2dc65fa223c987b163f4ba37f9a3c68eb6e6586", "sha256_in_prefix": "2496395e9399728db9658d29b2dc65fa223c987b163f4ba37f9a3c68eb6e6586", "size_in_bytes": 1742}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_build_clib.py", "path_type": "hardlink", "sha256": "328d5915be02d555c160e1af9da965c0ded80a74edaf6e1a90b0cef198b80ac6", "sha256_in_prefix": "328d5915be02d555c160e1af9da965c0ded80a74edaf6e1a90b0cef198b80ac6", "size_in_bytes": 4331}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_build_ext.py", "path_type": "hardlink", "sha256": "4053bda98561596749bb5ec75dce31f513272d99619349401e2f47569a5bb97e", "sha256_in_prefix": "4053bda98561596749bb5ec75dce31f513272d99619349401e2f47569a5bb97e", "size_in_bytes": 22545}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_build_py.py", "path_type": "hardlink", "sha256": "36c7e646ba2338705734ca9647f9a9e60e0f2d3823843ee264551f7c664521dc", "sha256_in_prefix": "36c7e646ba2338705734ca9647f9a9e60e0f2d3823843ee264551f7c664521dc", "size_in_bytes": 6882}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_build_scripts.py", "path_type": "hardlink", "sha256": "703f85472fa85f9e6c5d15f9133e7140269e1eb59a8f229ce17bb0bf67dee3cc", "sha256_in_prefix": "703f85472fa85f9e6c5d15f9133e7140269e1eb59a8f229ce17bb0bf67dee3cc", "size_in_bytes": 2880}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_check.py", "path_type": "hardlink", "sha256": "847495d3ba9fed8a12c46b136dbb1443db6cb19cf945135d6eb635b364b06852", "sha256_in_prefix": "847495d3ba9fed8a12c46b136dbb1443db6cb19cf945135d6eb635b364b06852", "size_in_bytes": 6226}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_clean.py", "path_type": "hardlink", "sha256": "84f1fa8df22918552bbd66c5d6dc6488d55235a031b76c2ae578d5e3df733b81", "sha256_in_prefix": "84f1fa8df22918552bbd66c5d6dc6488d55235a031b76c2ae578d5e3df733b81", "size_in_bytes": 1240}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_cmd.py", "path_type": "hardlink", "sha256": "6e0441efd9a2b6838a4753a2c991e70a882f1b1b77a56931793a880b4e254164", "sha256_in_prefix": "6e0441efd9a2b6838a4753a2c991e70a882f1b1b77a56931793a880b4e254164", "size_in_bytes": 3254}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_config_cmd.py", "path_type": "hardlink", "sha256": "66ce965f421fc43be6b82d7d5f3b953676029d3afd63e865ef74c09834813786", "sha256_in_prefix": "66ce965f421fc43be6b82d7d5f3b953676029d3afd63e865ef74c09834813786", "size_in_bytes": 2664}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_core.py", "path_type": "hardlink", "sha256": "2fb5ca540c5af8c1a8019780368a67b8af5f44a9de621912429830f1742f705f", "sha256_in_prefix": "2fb5ca540c5af8c1a8019780368a67b8af5f44a9de621912429830f1742f705f", "size_in_bytes": 3829}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_dir_util.py", "path_type": "hardlink", "sha256": "13ce250be938ae2554c1447259a43426ac76ba2dbe8a8fb446e25adcceea909b", "sha256_in_prefix": "13ce250be938ae2554c1447259a43426ac76ba2dbe8a8fb446e25adcceea909b", "size_in_bytes": 4500}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_dist.py", "path_type": "hardlink", "sha256": "6bac257397d025de6a43a1ce9ddcdcba93618d3c6f8fafbf24bb69b98bda3f53", "sha256_in_prefix": "6bac257397d025de6a43a1ce9ddcdcba93618d3c6f8fafbf24bb69b98bda3f53", "size_in_bytes": 18793}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_extension.py", "path_type": "hardlink", "sha256": "f987a32e0642bb2705ace05deb8a551f426fc0c73d3708731ef431bef8d71ea9", "sha256_in_prefix": "f987a32e0642bb2705ace05deb8a551f426fc0c73d3708731ef431bef8d71ea9", "size_in_bytes": 3670}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_file_util.py", "path_type": "hardlink", "sha256": "962be39e5dc592295096b076ac574542af67be3115647ca73726b46dfceffdbe", "sha256_in_prefix": "962be39e5dc592295096b076ac574542af67be3115647ca73726b46dfceffdbe", "size_in_bytes": 3522}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_filelist.py", "path_type": "hardlink", "sha256": "ac9c24a8251f9060e05a50f6d93a33b13f3271bba930707c0d7a93873c13d53e", "sha256_in_prefix": "ac9c24a8251f9060e05a50f6d93a33b13f3271bba930707c0d7a93873c13d53e", "size_in_bytes": 10766}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_install.py", "path_type": "hardlink", "sha256": "4df081d32921231c9d202d90e12b93019cd21efb5e30782b04bf708684a02bd4", "sha256_in_prefix": "4df081d32921231c9d202d90e12b93019cd21efb5e30782b04bf708684a02bd4", "size_in_bytes": 8618}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_install_data.py", "path_type": "hardlink", "sha256": "bcaab72bdee4d210409ce837f279b011d7fb7040d5afdad357209e2689606f80", "sha256_in_prefix": "bcaab72bdee4d210409ce837f279b011d7fb7040d5afdad357209e2689606f80", "size_in_bytes": 2464}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_install_headers.py", "path_type": "hardlink", "sha256": "3d5018a68fed625f7cd107fae033ce9a64afc9e7c81dd534e9fed5b09799ca41", "sha256_in_prefix": "3d5018a68fed625f7cd107fae033ce9a64afc9e7c81dd534e9fed5b09799ca41", "size_in_bytes": 936}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_install_lib.py", "path_type": "hardlink", "sha256": "aab8ba465fa668d4d0acd0d5f036de5cd974863b1f4482a2238adf64bae65812", "sha256_in_prefix": "aab8ba465fa668d4d0acd0d5f036de5cd974863b1f4482a2238adf64bae65812", "size_in_bytes": 3612}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_install_scripts.py", "path_type": "hardlink", "sha256": "284defd1c0e4156fbdd083880fe3a665918cda6872f99904bae5bb5174b6487c", "sha256_in_prefix": "284defd1c0e4156fbdd083880fe3a665918cda6872f99904bae5bb5174b6487c", "size_in_bytes": 1600}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_log.py", "path_type": "hardlink", "sha256": "8ac16d3ae7e5a02c84759690395edc554af8e86c2d755323e37986041e571fb9", "sha256_in_prefix": "8ac16d3ae7e5a02c84759690395edc554af8e86c2d755323e37986041e571fb9", "size_in_bytes": 323}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_modified.py", "path_type": "hardlink", "sha256": "875fbe6ce5a6b49a356e9555eae4617674bd6ebef508188d0ccd4c0f0486a6e8", "sha256_in_prefix": "875fbe6ce5a6b49a356e9555eae4617674bd6ebef508188d0ccd4c0f0486a6e8", "size_in_bytes": 4221}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_sdist.py", "path_type": "hardlink", "sha256": "71fcd4865080e35f3ed6f1fdb5adc806cdba73f8d405b909a0538ae469c0c8d9", "sha256_in_prefix": "71fcd4865080e35f3ed6f1fdb5adc806cdba73f8d405b909a0538ae469c0c8d9", "size_in_bytes": 15062}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_spawn.py", "path_type": "hardlink", "sha256": "792f30f43edb4f1c852d2c916a12567ae87c29cd45f11596898fdd486e41e417", "sha256_in_prefix": "792f30f43edb4f1c852d2c916a12567ae87c29cd45f11596898fdd486e41e417", "size_in_bytes": 4803}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_sysconfig.py", "path_type": "hardlink", "sha256": "97133c2ec522d53a268c35781e860af2ee6752806478d2fad14abc3d8d437305", "sha256_in_prefix": "97133c2ec522d53a268c35781e860af2ee6752806478d2fad14abc3d8d437305", "size_in_bytes": 11986}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_text_file.py", "path_type": "hardlink", "sha256": "59059207901f7410d968c03c045822a493e7b096ffd9228c7cbf747d291156dc", "sha256_in_prefix": "59059207901f7410d968c03c045822a493e7b096ffd9228c7cbf747d291156dc", "size_in_bytes": 3460}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_util.py", "path_type": "hardlink", "sha256": "1fdce5678cf8561e137e33580c1b313fbc20b902e9c427c963239c9b5c995377", "sha256_in_prefix": "1fdce5678cf8561e137e33580c1b313fbc20b902e9c427c963239c9b5c995377", "size_in_bytes": 7988}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_version.py", "path_type": "hardlink", "sha256": "6a17e0fe63fcc11cb5b20c18fbf3f1e61ae381febfba94c8a670a0a51e325919", "sha256_in_prefix": "6a17e0fe63fcc11cb5b20c18fbf3f1e61ae381febfba94c8a670a0a51e325919", "size_in_bytes": 2750}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_versionpredicate.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/unix_compat.py", "path_type": "hardlink", "sha256": "cfea29e82da255d5f56aae4120147b72a3b18a3284f7b6a537026a1cd74ba682", "sha256_in_prefix": "cfea29e82da255d5f56aae4120147b72a3b18a3284f7b6a537026a1cd74ba682", "size_in_bytes": 386}, {"_path": "Lib/site-packages/setuptools/_distutils/text_file.py", "path_type": "hardlink", "sha256": "cf876438906bf41a362c6d1336a9bcb03eb72c411a29248fd09d1b581ac51b77", "sha256_in_prefix": "cf876438906bf41a362c6d1336a9bcb03eb72c411a29248fd09d1b581ac51b77", "size_in_bytes": 12101}, {"_path": "Lib/site-packages/setuptools/_distutils/unixccompiler.py", "path_type": "hardlink", "sha256": "d5b5c9587e1f8aefc0d967eb887cdff3cc639654135e79e352465d44ab3d7165", "sha256_in_prefix": "d5b5c9587e1f8aefc0d967eb887cdff3cc639654135e79e352465d44ab3d7165", "size_in_bytes": 212}, {"_path": "Lib/site-packages/setuptools/_distutils/util.py", "path_type": "hardlink", "sha256": "3637e7aa4eb4ccc7648808d19c6713597dede3dfa86c76a93a56cdbf2225d362", "sha256_in_prefix": "3637e7aa4eb4ccc7648808d19c6713597dede3dfa86c76a93a56cdbf2225d362", "size_in_bytes": 18094}, {"_path": "Lib/site-packages/setuptools/_distutils/version.py", "path_type": "hardlink", "sha256": "bc8993e7e1025e4436d6828bd17605893a8ae8dc8cd3d729cc136803fdf80905", "sha256_in_prefix": "bc8993e7e1025e4436d6828bd17605893a8ae8dc8cd3d729cc136803fdf80905", "size_in_bytes": 12619}, {"_path": "Lib/site-packages/setuptools/_distutils/versionpredicate.py", "path_type": "hardlink", "sha256": "a81590eb04e3d76383cada13988c9d79f218da36f8b98d6c75b81bb8b9fe2093", "sha256_in_prefix": "a81590eb04e3d76383cada13988c9d79f218da36f8b98d6c75b81bb8b9fe2093", "size_in_bytes": 5205}, {"_path": "Lib/site-packages/setuptools/_distutils/zosccompiler.py", "path_type": "hardlink", "sha256": "b2f7625d9da475cc0aac929f8548626f4df2f20cfb68664aba45c7dc8ed89017", "sha256_in_prefix": "b2f7625d9da475cc0aac929f8548626f4df2f20cfb68664aba45c7dc8ed89017", "size_in_bytes": 58}, {"_path": "Lib/site-packages/setuptools/_entry_points.py", "path_type": "hardlink", "sha256": "63741413d24a156fd8caab839e97df3564ace9fde3284b757be767c7efbdf8ac", "sha256_in_prefix": "63741413d24a156fd8caab839e97df3564ace9fde3284b757be767c7efbdf8ac", "size_in_bytes": 2310}, {"_path": "Lib/site-packages/setuptools/_imp.py", "path_type": "hardlink", "sha256": "618d448d910dfb4cd8722d5cc4ed7407f69d0043abee2f1e2bc26809cf487ab7", "sha256_in_prefix": "618d448d910dfb4cd8722d5cc4ed7407f69d0043abee2f1e2bc26809cf487ab7", "size_in_bytes": 2435}, {"_path": "Lib/site-packages/setuptools/_importlib.py", "path_type": "hardlink", "sha256": "68a22370ad07297373d83f974ebc5a8b11a7fe3b9390e3709aeddd72178c385d", "sha256_in_prefix": "68a22370ad07297373d83f974ebc5a8b11a7fe3b9390e3709aeddd72178c385d", "size_in_bytes": 223}, {"_path": "Lib/site-packages/setuptools/_itertools.py", "path_type": "hardlink", "sha256": "8d645fb08ae90bb9b2a28cf78435118fd1adbe9b3065e2978361da926121363a", "sha256_in_prefix": "8d645fb08ae90bb9b2a28cf78435118fd1adbe9b3065e2978361da926121363a", "size_in_bytes": 657}, {"_path": "Lib/site-packages/setuptools/_normalization.py", "path_type": "hardlink", "sha256": "9009867ebc23179763c9d11f2cbc8a82391709b2ffd3f67150f3be0e52e59886", "sha256_in_prefix": "9009867ebc23179763c9d11f2cbc8a82391709b2ffd3f67150f3be0e52e59886", "size_in_bytes": 5824}, {"_path": "Lib/site-packages/setuptools/_path.py", "path_type": "hardlink", "sha256": "70fbf8d6fd371c3eee118a82228f84fdc1917e93d5af8972c010a22be1d2ac28", "sha256_in_prefix": "70fbf8d6fd371c3eee118a82228f84fdc1917e93d5af8972c010a22be1d2ac28", "size_in_bytes": 2685}, {"_path": "Lib/site-packages/setuptools/_reqs.py", "path_type": "hardlink", "sha256": "408dc2f6e38148d45c72edb4f2a3e78b11f1e759f10abcbbfe73d32096926313", "sha256_in_prefix": "408dc2f6e38148d45c72edb4f2a3e78b11f1e759f10abcbbfe73d32096926313", "size_in_bytes": 1438}, {"_path": "Lib/site-packages/setuptools/_shutil.py", "path_type": "hardlink", "sha256": "7003a595ca323135ece492e8c9b422dbdc88e6000193cda17a9272381bf66ccc", "sha256_in_prefix": "7003a595ca323135ece492e8c9b422dbdc88e6000193cda17a9272381bf66ccc", "size_in_bytes": 1496}, {"_path": "Lib/site-packages/setuptools/_static.py", "path_type": "hardlink", "sha256": "19347bf60112175fc968ae2dacb9290eb12e09e12d3e5c105b4311bfb54d417e", "sha256_in_prefix": "19347bf60112175fc968ae2dacb9290eb12e09e12d3e5c105b4311bfb54d417e", "size_in_bytes": 4855}, {"_path": "Lib/site-packages/setuptools/_vendor/__pycache__/typing_extensions.cpython-39.pyc", "path_type": "hardlink", "sha256": "e1d454280b2f8e0b72c261be91db8e4bfbfda51491d23056124f68d71e5df3ca", "sha256_in_prefix": "e1d454280b2f8e0b72c261be91db8e4bfbfda51491d23056124f68d71e5df3ca", "size_in_bytes": 101700}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/LICENSE", "path_type": "hardlink", "sha256": "ade78d04982d69972d444a8e14a94f87a2334dd3855cc80348ea8e240aa0df2d", "sha256_in_prefix": "ade78d04982d69972d444a8e14a94f87a2334dd3855cc80348ea8e240aa0df2d", "size_in_bytes": 7634}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "3800d9b91dceea2065a6ed6279383362e97ac38b8e56b9343f404ee531860099", "sha256_in_prefix": "3800d9b91dceea2065a6ed6279383362e97ac38b8e56b9343f404ee531860099", "size_in_bytes": 15006}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "822bba66b41526fa547186b80221f85da50d652bee5493dbfe5d14085112f0c3", "sha256_in_prefix": "822bba66b41526fa547186b80221f85da50d652bee5493dbfe5d14085112f0c3", "size_in_bytes": 1308}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "db07a93359e4e034b8785a58ad6d534ea3dca0635f1e184efe2e66e1c3a299ba", "sha256_in_prefix": "db07a93359e4e034b8785a58ad6d534ea3dca0635f1e184efe2e66e1c3a299ba", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "0337e180a292f04740c16513485f2681e5506d7398f64a241c1ea44aac30aaed", "sha256_in_prefix": "0337e180a292f04740c16513485f2681e5506d7398f64a241c1ea44aac30aaed", "size_in_bytes": 12}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__init__.py", "path_type": "hardlink", "sha256": "ce4a39467be896f6fe5178c2c7fd80acf4c6056c142b9418e0b21020a611ec0b", "sha256_in_prefix": "ce4a39467be896f6fe5178c2c7fd80acf4c6056c142b9418e0b21020a611ec0b", "size_in_bytes": 1037}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "b525665547722e617e0c973296fbffd5966c061f5a581c35ba461ed65e3f995c", "sha256_in_prefix": "b525665547722e617e0c973296fbffd5966c061f5a581c35ba461ed65e3f995c", "size_in_bytes": 344}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/autoasync.cpython-39.pyc", "path_type": "hardlink", "sha256": "094312bfcfb06bf7510b5a08f94eb9656855bc59b90355eebfda16cbeeb6785a", "sha256_in_prefix": "094312bfcfb06bf7510b5a08f94eb9656855bc59b90355eebfda16cbeeb6785a", "size_in_bytes": 4114}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/autocommand.cpython-39.pyc", "path_type": "hardlink", "sha256": "21f6b44ce894c243f49c836426c8a47d45980a23334b771bd2662a6e26009cb1", "sha256_in_prefix": "21f6b44ce894c243f49c836426c8a47d45980a23334b771bd2662a6e26009cb1", "size_in_bytes": 989}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/automain.cpython-39.pyc", "path_type": "hardlink", "sha256": "60b42c4e9a1e7e291857f8dc3f1c0f94311cf69b2e4f477abbab2f14223d46a8", "sha256_in_prefix": "60b42c4e9a1e7e291857f8dc3f1c0f94311cf69b2e4f477abbab2f14223d46a8", "size_in_bytes": 1629}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/autoparse.cpython-39.pyc", "path_type": "hardlink", "sha256": "0d0f694c562326607a1902834b5e25245fb7d3901bf7f334200134acecf5bf00", "sha256_in_prefix": "0d0f694c562326607a1902834b5e25245fb7d3901bf7f334200134acecf5bf00", "size_in_bytes": 8422}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/errors.cpython-39.pyc", "path_type": "hardlink", "sha256": "83539eab2e0d9090eb9f23226ec77f2bcb8323938e2500c49fd0aa6510f5e85c", "sha256_in_prefix": "83539eab2e0d9090eb9f23226ec77f2bcb8323938e2500c49fd0aa6510f5e85c", "size_in_bytes": 374}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/autoasync.py", "path_type": "hardlink", "sha256": "00c772af1352e29a9625f3ffc6ea0e70898e1d60fea93ef1d3ac2628dd55a7e5", "sha256_in_prefix": "00c772af1352e29a9625f3ffc6ea0e70898e1d60fea93ef1d3ac2628dd55a7e5", "size_in_bytes": 5680}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/autocommand.py", "path_type": "hardlink", "sha256": "866904990ef61ed2f9e609d44558c33a7b1f62519de652d76ef4f8286e3de90c", "sha256_in_prefix": "866904990ef61ed2f9e609d44558c33a7b1f62519de652d76ef4f8286e3de90c", "size_in_bytes": 2505}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/automain.py", "path_type": "hardlink", "sha256": "0366fc8bbe7833173f0e353d585afabea6035a5873d1c9fc9a2bbc77c12cc55f", "sha256_in_prefix": "0366fc8bbe7833173f0e353d585afabea6035a5873d1c9fc9a2bbc77c12cc55f", "size_in_bytes": 2076}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/autoparse.py", "path_type": "hardlink", "sha256": "5955a66493dc6f350a5cfe34ada430ff41c3f2a3c9d95f551b57851669a7171c", "sha256_in_prefix": "5955a66493dc6f350a5cfe34ada430ff41c3f2a3c9d95f551b57851669a7171c", "size_in_bytes": 11642}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/errors.py", "path_type": "hardlink", "sha256": "eda6b7ae887d1deaddea720aa501cd584b25584f28abb1a21d8554b91a8e4670", "sha256_in_prefix": "eda6b7ae887d1deaddea720aa501cd584b25584f28abb1a21d8554b91a8e4670", "size_in_bytes": 886}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "8215c54ead77d9dc5a108a25c6bdc72b5999aa6f62c9499a440359412afa5a51", "sha256_in_prefix": "8215c54ead77d9dc5a108a25c6bdc72b5999aa6f62c9499a440359412afa5a51", "size_in_bytes": 2020}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "258a1f1c849e1175069a55a5d6ce357afdd04e34cd5de27093e4acec7a9d2ce1", "sha256_in_prefix": "258a1f1c849e1175069a55a5d6ce357afdd04e34cd5de27093e4acec7a9d2ce1", "size_in_bytes": 1360}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "7068da2cc3a8051d452b4029a23b73595995893b49ec91882bf1f05e212cbed5", "sha256_in_prefix": "7068da2cc3a8051d452b4029a23b73595995893b49ec91882bf1f05e212cbed5", "size_in_bytes": 10}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/__init__.py", "path_type": "hardlink", "sha256": "88e10cc2794e4567b374ef3edafc4120f491dfb0fb2468e5b99f1fe79bf3c65b", "sha256_in_prefix": "88e10cc2794e4567b374ef3edafc4120f491dfb0fb2468e5b99f1fe79bf3c65b", "size_in_bytes": 81}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "2baa3f1d4ce2d1163964fbb7288ae7c7b72f19ea4f0d773b8ba40d72d641a9a6", "sha256_in_prefix": "2baa3f1d4ce2d1163964fbb7288ae7c7b72f19ea4f0d773b8ba40d72d641a9a6", "size_in_bytes": 216}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/__init__.py", "path_type": "hardlink", "sha256": "3f07f6a9421f0744a89493c229cc77bf3dd412efda89db38838b007f1cbde2a8", "sha256_in_prefix": "3f07f6a9421f0744a89493c229cc77bf3dd412efda89db38838b007f1cbde2a8", "size_in_bytes": 108491}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/__main__.py", "path_type": "hardlink", "sha256": "630da8193d5a7ebcf6781b24cdd3d82fc45e07fde5880a6684590dd846c399ce", "sha256_in_prefix": "630da8193d5a7ebcf6781b24cdd3d82fc45e07fde5880a6684590dd846c399ce", "size_in_bytes": 59}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "4dfaf458374f1a47673947005f4fd857805ee39cc9156817419a4f8d8fcba5ff", "sha256_in_prefix": "4dfaf458374f1a47673947005f4fd857805ee39cc9156817419a4f8d8fcba5ff", "size_in_bytes": 72337}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/__pycache__/__main__.cpython-39.pyc", "path_type": "hardlink", "sha256": "51f71e0d94eb40c65df390db3d5737bb15bb49f53ddbcb4b9c92a6669c04dbfb", "sha256_in_prefix": "51f71e0d94eb40c65df390db3d5737bb15bb49f53ddbcb4b9c92a6669c04dbfb", "size_in_bytes": 224}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "c52d6630d116edc7f007884d80061322351be075c575a7cb191c4c72f3ff4acb", "sha256_in_prefix": "c52d6630d116edc7f007884d80061322351be075c575a7cb191c4c72f3ff4acb", "size_in_bytes": 164}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/__pycache__/py38.cpython-39.pyc", "path_type": "hardlink", "sha256": "7b0539903874965e0cd138ea971aaa40116b418084a587b888e22ea177f1d0d3", "sha256_in_prefix": "7b0539903874965e0cd138ea971aaa40116b418084a587b888e22ea177f1d0d3", "size_in_bytes": 764}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/py38.py", "path_type": "hardlink", "sha256": "898932b7f82f5a32f31944c90fd4ee4df30c8ce93e7abb17666465bd060ddaa1", "sha256_in_prefix": "898932b7f82f5a32f31944c90fd4ee4df30c8ce93e7abb17666465bd060ddaa1", "size_in_bytes": 568}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "cfc7749b96f63bd31c3c42b5c471bf756814053e847c10f3eb003417bc523d30", "sha256_in_prefix": "cfc7749b96f63bd31c3c42b5c471bf756814053e847c10f3eb003417bc523d30", "size_in_bytes": 11358}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "6a7b90effee1e09d5b484cdf7232016a43e2d9cc9543bcbb8e494b1ec05e1f59", "sha256_in_prefix": "6a7b90effee1e09d5b484cdf7232016a43e2d9cc9543bcbb8e494b1ec05e1f59", "size_in_bytes": 4648}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "0d8d3c6eeb9ebbe86cac7d60861552433c329da9ea51248b61d02be2e5e64030", "sha256_in_prefix": "0d8d3c6eeb9ebbe86cac7d60861552433c329da9ea51248b61d02be2e5e64030", "size_in_bytes": 2518}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "9a0b8c95618c5fe5479cca4a3a38d089d228d6cb1194216ee1ae26069cf5b363", "sha256_in_prefix": "9a0b8c95618c5fe5479cca4a3a38d089d228d6cb1194216ee1ae26069cf5b363", "size_in_bytes": 91}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "08eddf0fdcb29403625e4acca38a872d5fe6a972f6b02e4914a82dd725804fe0", "sha256_in_prefix": "08eddf0fdcb29403625e4acca38a872d5fe6a972f6b02e4914a82dd725804fe0", "size_in_bytes": 19}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__init__.py", "path_type": "hardlink", "sha256": "b59341fb6de1f018b18bdb82ad0aa3f587f469e0bef89a2c772dc8651210781d", "sha256_in_prefix": "b59341fb6de1f018b18bdb82ad0aa3f587f469e0bef89a2c772dc8651210781d", "size_in_bytes": 33798}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "9a7e630a78a080426c7b23eded7c5748d13807cf72972151d30cf64a312c2dc4", "sha256_in_prefix": "9a7e630a78a080426c7b23eded7c5748d13807cf72972151d30cf64a312c2dc4", "size_in_bytes": 39895}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_adapters.cpython-39.pyc", "path_type": "hardlink", "sha256": "072e7c3153c2d81e80b2bb50350336f756212b525417196b59bc7316d8619bbd", "sha256_in_prefix": "072e7c3153c2d81e80b2bb50350336f756212b525417196b59bc7316d8619bbd", "size_in_bytes": 2838}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_collections.cpython-39.pyc", "path_type": "hardlink", "sha256": "f69e6b905fef9f07f93137ea6aa6ef689d7e4f4ab4856091d9bc2e7871f74dad", "sha256_in_prefix": "f69e6b905fef9f07f93137ea6aa6ef689d7e4f4ab4856091d9bc2e7871f74dad", "size_in_bytes": 1528}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_compat.cpython-39.pyc", "path_type": "hardlink", "sha256": "6bf1df226c7c334c26663cffd4c3270d2393cd32af8dbfcd0353c13c42caa39c", "sha256_in_prefix": "6bf1df226c7c334c26663cffd4c3270d2393cd32af8dbfcd0353c13c42caa39c", "size_in_bytes": 1862}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_functools.cpython-39.pyc", "path_type": "hardlink", "sha256": "ebdad8af485a896eb9fe33f36ea6a767a05c2243b13c34786a7fe2ca6eef97c4", "sha256_in_prefix": "ebdad8af485a896eb9fe33f36ea6a767a05c2243b13c34786a7fe2ca6eef97c4", "size_in_bytes": 3118}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_itertools.cpython-39.pyc", "path_type": "hardlink", "sha256": "7fe9e9e380bf0c71b121220a45f5bfbd34a493e3c70be125844e50bb1e47277c", "sha256_in_prefix": "7fe9e9e380bf0c71b121220a45f5bfbd34a493e3c70be125844e50bb1e47277c", "size_in_bytes": 1995}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_meta.cpython-39.pyc", "path_type": "hardlink", "sha256": "0f2c0114f8506b7fc0583cfba35bd345225a814d42e85866fec3cbab19c6a917", "sha256_in_prefix": "0f2c0114f8506b7fc0583cfba35bd345225a814d42e85866fec3cbab19c6a917", "size_in_bytes": 3218}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_text.cpython-39.pyc", "path_type": "hardlink", "sha256": "dc705b7f006e1d5d6e7e2ef5d65840867d02c98c954013ed9a9dd91c29bb7f9b", "sha256_in_prefix": "dc705b7f006e1d5d6e7e2ef5d65840867d02c98c954013ed9a9dd91c29bb7f9b", "size_in_bytes": 3061}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/diagnose.cpython-39.pyc", "path_type": "hardlink", "sha256": "4f286b717dcd254f4deb03f7684bee86e28a71dafad9edf94fe93d3d6c09fd6a", "sha256_in_prefix": "4f286b717dcd254f4deb03f7684bee86e28a71dafad9edf94fe93d3d6c09fd6a", "size_in_bytes": 812}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_adapters.py", "path_type": "hardlink", "sha256": "ac88564f006f600d5b57b8bee457d55f7f2a1170d35c5792e5c6f9c49b4fde4b", "sha256_in_prefix": "ac88564f006f600d5b57b8bee457d55f7f2a1170d35c5792e5c6f9c49b4fde4b", "size_in_bytes": 2317}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_collections.py", "path_type": "hardlink", "sha256": "089d0e4c21c88d6034648552e2fa0e440b27d91e11d9c40112d3ec6442690126", "sha256_in_prefix": "089d0e4c21c88d6034648552e2fa0e440b27d91e11d9c40112d3ec6442690126", "size_in_bytes": 743}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_compat.py", "path_type": "hardlink", "sha256": "ef740aacdf4a368699ce16d7e723c20996be15a00bc177bc4cf94347bd898015", "sha256_in_prefix": "ef740aacdf4a368699ce16d7e723c20996be15a00bc177bc4cf94347bd898015", "size_in_bytes": 1314}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_functools.py", "path_type": "hardlink", "sha256": "3ec636fb8aeb297e1155e442d681a9d65075a660bd78a37cf3f7fe6c3f6e3a80", "sha256_in_prefix": "3ec636fb8aeb297e1155e442d681a9d65075a660bd78a37cf3f7fe6c3f6e3a80", "size_in_bytes": 2895}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_itertools.py", "path_type": "hardlink", "sha256": "72faffdaff0145bc5c225e71e6575fa9d1e3848f188bcb3cca4e741bf9e6ea34", "sha256_in_prefix": "72faffdaff0145bc5c225e71e6575fa9d1e3848f188bcb3cca4e741bf9e6ea34", "size_in_bytes": 2068}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_meta.py", "path_type": "hardlink", "sha256": "9f167b0bc19595c04500a5b254e9ff767ee8b7fb7005c6e6d4d9af8c87ad0472", "sha256_in_prefix": "9f167b0bc19595c04500a5b254e9ff767ee8b7fb7005c6e6d4d9af8c87ad0472", "size_in_bytes": 1801}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_text.py", "path_type": "hardlink", "sha256": "1c2b0592c66924b7933f734493f9e0ac079755146d4ebb7287d78e001a113f80", "sha256_in_prefix": "1c2b0592c66924b7933f734493f9e0ac079755146d4ebb7287d78e001a113f80", "size_in_bytes": 2166}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "5818b97567ab91159e0333719ad6c83a091353329fc488ec4b60db3fd9fcbc7b", "sha256_in_prefix": "5818b97567ab91159e0333719ad6c83a091353329fc488ec4b60db3fd9fcbc7b", "size_in_bytes": 165}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/py311.cpython-39.pyc", "path_type": "hardlink", "sha256": "7d30fbc468501bfb31f451a4b23d64e3b77a74fd8bcf19f91828d765ab21fa28", "sha256_in_prefix": "7d30fbc468501bfb31f451a4b23d64e3b77a74fd8bcf19f91828d765ab21fa28", "size_in_bytes": 1001}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/py39.cpython-39.pyc", "path_type": "hardlink", "sha256": "58194307ea144d42825a7d297cc55cc1362a9cac415157479a3b1019316cb89e", "sha256_in_prefix": "58194307ea144d42825a7d297cc55cc1362a9cac415157479a3b1019316cb89e", "size_in_bytes": 1164}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/py311.py", "path_type": "hardlink", "sha256": "baa9be2beba88728f5d38d931f86bd12bfc8e68efaebb0efba5703fa00bf7d20", "sha256_in_prefix": "baa9be2beba88728f5d38d931f86bd12bfc8e68efaebb0efba5703fa00bf7d20", "size_in_bytes": 608}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/py39.py", "path_type": "hardlink", "sha256": "70f90cbfafb48a52bed09c3f4e49f4c586ce28965ce1624a407a30d1cd93e38c", "sha256_in_prefix": "70f90cbfafb48a52bed09c3f4e49f4c586ce28965ce1624a407a30d1cd93e38c", "size_in_bytes": 1102}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/diagnose.py", "path_type": "hardlink", "sha256": "9e4491322a309669212d884a86f0a0f60966b7fd750a8c7e1262f311ba984daf", "sha256_in_prefix": "9e4491322a309669212d884a86f0a0f60966b7fd750a8c7e1262f311ba984daf", "size_in_bytes": 379}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "66030d634580651b3e53cc19895d9231f8d22aa06b327817c8332cfc20303308", "sha256_in_prefix": "66030d634580651b3e53cc19895d9231f8d22aa06b327817c8332cfc20303308", "size_in_bytes": 21079}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "5d7834ac1ba2612c6801050fde57a7b98b0f36acf88c3c2d4f376fd8911b3091", "sha256_in_prefix": "5d7834ac1ba2612c6801050fde57a7b98b0f36acf88c3c2d4f376fd8911b3091", "size_in_bytes": 943}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "cb8997f92397e1f6089289ec0060393743b2fbcfe0238157c391cd235c6abd68", "sha256_in_prefix": "cb8997f92397e1f6089289ec0060393743b2fbcfe0238157c391cd235c6abd68", "size_in_bytes": 91}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "9b9dae8dda75d02a93ea38755d0c594fa9049ed727bfeed397b52218d4f35990", "sha256_in_prefix": "9b9dae8dda75d02a93ea38755d0c594fa9049ed727bfeed397b52218d4f35990", "size_in_bytes": 8}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/__init__.py", "path_type": "hardlink", "sha256": "271cb51c95d9899f3990778b021926bf3e84313745a817be76ebeddf847a20e7", "sha256_in_prefix": "271cb51c95d9899f3990778b021926bf3e84313745a817be76ebeddf847a20e7", "size_in_bytes": 103796}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "70f22f78ddb280e092715a5393c99a425d71ccf58ebe1a4f14cb36ef375e91a3", "sha256_in_prefix": "70f22f78ddb280e092715a5393c99a425d71ccf58ebe1a4f14cb36ef375e91a3", "size_in_bytes": 73239}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/compat/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "37bfdfda88e9700df44ec757dae5462da64f949b748415947aaf88b67d778757", "sha256_in_prefix": "37bfdfda88e9700df44ec757dae5462da64f949b748415947aaf88b67d778757", "size_in_bytes": 154}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/compat/__pycache__/py38.cpython-39.pyc", "path_type": "hardlink", "sha256": "a8e63a1a67095b02e9d466aa0f4cce0a1ef8d10a60fd3e0ed88b7fab98a3f5d9", "sha256_in_prefix": "a8e63a1a67095b02e9d466aa0f4cce0a1ef8d10a60fd3e0ed88b7fab98a3f5d9", "size_in_bytes": 278}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/compat/py38.py", "path_type": "hardlink", "sha256": "a0e6d57d59d65fdfcea673ae1099f59856c9c55870c91e5ea5b8933570c36aca", "sha256_in_prefix": "a0e6d57d59d65fdfcea673ae1099f59856c9c55870c91e5ea5b8933570c36aca", "size_in_bytes": 160}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "20c51a96236c0395f53b1f4c5d458e6a0721e51e16c1bff733b7aba76f5d06d8", "sha256_in_prefix": "20c51a96236c0395f53b1f4c5d458e6a0721e51e16c1bff733b7aba76f5d06d8", "size_in_bytes": 3933}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "1e9b62bd70e4a5fa26e9594cbb80860ffeca3debfee8773daefa774cd259ca06", "sha256_in_prefix": "1e9b62bd70e4a5fa26e9594cbb80860ffeca3debfee8773daefa774cd259ca06", "size_in_bytes": 873}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "31d8bd3c3370119a6d3a34e551c02d87b5c90c5b4aac761a40c3ee9597810a24", "sha256_in_prefix": "31d8bd3c3370119a6d3a34e551c02d87b5c90c5b4aac761a40c3ee9597810a24", "size_in_bytes": 91}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "sha256_in_prefix": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "size_in_bytes": 7}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "c43b60b897a3d2d37d8845c252fc44261d9aef171e21154111a9012d2afffed6", "sha256_in_prefix": "c43b60b897a3d2d37d8845c252fc44261d9aef171e21154111a9012d2afffed6", "size_in_bytes": 4020}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "55197b88a78443297bb2d827a75baae740b33896251d872835d4b4c75ec2f57e", "sha256_in_prefix": "55197b88a78443297bb2d827a75baae740b33896251d872835d4b4c75ec2f57e", "size_in_bytes": 641}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "sha256_in_prefix": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "size_in_bytes": 7}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "8b86946900d7fa38dd1102b9c1ebe17a0cb1f09c8b7e29f61f2bda4a4dc51eca", "sha256_in_prefix": "8b86946900d7fa38dd1102b9c1ebe17a0cb1f09c8b7e29f61f2bda4a4dc51eca", "size_in_bytes": 2891}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "632aa7c04f7c4bcc01c027af5b9bc76fe8958f4a181035b957a3bd3014ba248b", "sha256_in_prefix": "632aa7c04f7c4bcc01c027af5b9bc76fe8958f4a181035b957a3bd3014ba248b", "size_in_bytes": 843}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "sha256_in_prefix": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "size_in_bytes": 7}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "03359d9ba56231f0ce3e840c7cb5a7db380141218949ccaa78ddbd4dcb965d52", "sha256_in_prefix": "03359d9ba56231f0ce3e840c7cb5a7db380141218949ccaa78ddbd4dcb965d52", "size_in_bytes": 3658}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "816d945741dca246099388ca3eed74fc0667acbaa36f70b559b2494c3979b1f6", "sha256_in_prefix": "816d945741dca246099388ca3eed74fc0667acbaa36f70b559b2494c3979b1f6", "size_in_bytes": 1500}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "sha256_in_prefix": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "size_in_bytes": 7}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/__pycache__/context.cpython-39.pyc", "path_type": "hardlink", "sha256": "53da956186a1b869e35a3cb910f5a2c5f22f9e8a0c80b69798dd00d2937a8962", "sha256_in_prefix": "53da956186a1b869e35a3cb910f5a2c5f22f9e8a0c80b69798dd00d2937a8962", "size_in_bytes": 10969}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/collections/__init__.py", "path_type": "hardlink", "sha256": "3dcd7e4aa8d69bcd5a7753f4f86b6da64c0efcb5a59da63a814abc81c2a1dafd", "sha256_in_prefix": "3dcd7e4aa8d69bcd5a7753f4f86b6da64c0efcb5a59da63a814abc81c2a1dafd", "size_in_bytes": 26640}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/collections/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "4db260d61a2ea7eb06676b4e5c5c4b250573ce818032930c93e5bebb45d5e465", "sha256_in_prefix": "4db260d61a2ea7eb06676b4e5c5c4b250573ce818032930c93e5bebb45d5e465", "size_in_bytes": 32438}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/collections/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/context.py", "path_type": "hardlink", "sha256": "444a0b2310e43b931f118a30b7f5a8ba9342468c414b9bfb617d8f6d6f2521cb", "sha256_in_prefix": "444a0b2310e43b931f118a30b7f5a8ba9342468c414b9bfb617d8f6d6f2521cb", "size_in_bytes": 9552}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/functools/__init__.py", "path_type": "hardlink", "sha256": "844009692dae49946e17f258e02c421c8621efd669c5a3e9f4e887cabf44275c", "sha256_in_prefix": "844009692dae49946e17f258e02c421c8621efd669c5a3e9f4e887cabf44275c", "size_in_bytes": 16642}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/functools/__init__.pyi", "path_type": "hardlink", "sha256": "824dddb201f3a3917f53be07cc0be9362bc500f0a43c9d5bdbec8277ad9d7e7c", "sha256_in_prefix": "824dddb201f3a3917f53be07cc0be9362bc500f0a43c9d5bdbec8277ad9d7e7c", "size_in_bytes": 3878}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/functools/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "43d282328c66378e2685dfc8026ee6c077a8681733cb4ac8a667260e70771dcf", "sha256_in_prefix": "43d282328c66378e2685dfc8026ee6c077a8681733cb4ac8a667260e70771dcf", "size_in_bytes": 19254}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/functools/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/Lorem ipsum.txt", "path_type": "hardlink", "sha256": "37fedcffbf73c4eb9f058f47677cb33203a436ff9390e4d38a8e01c9dad28e0b", "sha256_in_prefix": "37fedcffbf73c4eb9f058f47677cb33203a436ff9390e4d38a8e01c9dad28e0b", "size_in_bytes": 1335}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__init__.py", "path_type": "hardlink", "sha256": "636614a9747fa2b5280da6384a43d17ba65985be4750707f021f5108db15ca1a", "sha256_in_prefix": "636614a9747fa2b5280da6384a43d17ba65985be4750707f021f5108db15ca1a", "size_in_bytes": 16250}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "6f2e6edcb8133f4aa7ad4f625eee59d475d08e81bb60250fc245e6d08a103819", "sha256_in_prefix": "6f2e6edcb8133f4aa7ad4f625eee59d475d08e81bb60250fc245e6d08a103819", "size_in_bytes": 20443}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/layouts.cpython-39.pyc", "path_type": "hardlink", "sha256": "063307eed52fbdcab60be24c0b79679f7ca9391599743087f2e8185f2ae7fcd9", "sha256_in_prefix": "063307eed52fbdcab60be24c0b79679f7ca9391599743087f2e8185f2ae7fcd9", "size_in_bytes": 867}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/show-newlines.cpython-39.pyc", "path_type": "hardlink", "sha256": "eb8ab0b064ea55536a2741dd2a3e265245b85591ddae000bc7bdc7dce9cfc28a", "sha256_in_prefix": "eb8ab0b064ea55536a2741dd2a3e265245b85591ddae000bc7bdc7dce9cfc28a", "size_in_bytes": 1089}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/strip-prefix.cpython-39.pyc", "path_type": "hardlink", "sha256": "e1b72368b16cba676495bfe0bb3d480a685071cd5b7478574f844452b6638719", "sha256_in_prefix": "e1b72368b16cba676495bfe0bb3d480a685071cd5b7478574f844452b6638719", "size_in_bytes": 637}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/to-dvorak.cpython-39.pyc", "path_type": "hardlink", "sha256": "be038655b7c26c84226933d96411347a0083aebcd9ba05ff1a4b163a5a6c9b41", "sha256_in_prefix": "be038655b7c26c84226933d96411347a0083aebcd9ba05ff1a4b163a5a6c9b41", "size_in_bytes": 287}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/to-qwerty.cpython-39.pyc", "path_type": "hardlink", "sha256": "34bd7997a73c7873e04111f11b97d4bc4301516f2cec0c3becde224e20314c3c", "sha256_in_prefix": "34bd7997a73c7873e04111f11b97d4bc4301516f2cec0c3becde224e20314c3c", "size_in_bytes": 287}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/layouts.py", "path_type": "hardlink", "sha256": "1d30bc6924cb67bb978a9c8e5daa51302d79f23b9e7232ba455c22b5f999f7fc", "sha256_in_prefix": "1d30bc6924cb67bb978a9c8e5daa51302d79f23b9e7232ba455c22b5f999f7fc", "size_in_bytes": 643}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/show-newlines.py", "path_type": "hardlink", "sha256": "58641aeb97bc97285bf762d438ba959fa29a0ada1e560570b3a5ad49198b93ac", "sha256_in_prefix": "58641aeb97bc97285bf762d438ba959fa29a0ada1e560570b3a5ad49198b93ac", "size_in_bytes": 904}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/strip-prefix.py", "path_type": "hardlink", "sha256": "35f55757c255368ea7a9cb980127cc57bff2e04a3cccc42a942386bc09d1215c", "sha256_in_prefix": "35f55757c255368ea7a9cb980127cc57bff2e04a3cccc42a942386bc09d1215c", "size_in_bytes": 412}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/to-dvorak.py", "path_type": "hardlink", "sha256": "d5235c6d2b2f212a575e0f8b9f26c81c763e45414e42bdfacdc1e4635a5616ac", "sha256_in_prefix": "d5235c6d2b2f212a575e0f8b9f26c81c763e45414e42bdfacdc1e4635a5616ac", "size_in_bytes": 119}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/to-qwerty.py", "path_type": "hardlink", "sha256": "b3850c4149cfc059ff741e6e642dbb06eac7390718a277417f322954be69133c", "sha256_in_prefix": "b3850c4149cfc059ff741e6e642dbb06eac7390718a277417f322954be69133c", "size_in_bytes": 119}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "09f1c8c9e941af3e584d59641ea9b87d83c0cb0fd007eb5ef391a7e2643c1a46", "sha256_in_prefix": "09f1c8c9e941af3e584d59641ea9b87d83c0cb0fd007eb5ef391a7e2643c1a46", "size_in_bytes": 1053}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "0453bdd0ef9f2cd89540ca63ee8212e73b73809514419dd3037d8fe471f737e0", "sha256_in_prefix": "0453bdd0ef9f2cd89540ca63ee8212e73b73809514419dd3037d8fe471f737e0", "size_in_bytes": 36293}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "77c8e73e018dc0fd7e9ed6c80b05a4404545f641fb085220ce42b368b59aa3d3", "sha256_in_prefix": "77c8e73e018dc0fd7e9ed6c80b05a4404545f641fb085220ce42b368b59aa3d3", "size_in_bytes": 1259}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "ad282afc9a4717d7c7475971e77ab083fd7ed8bca9644fea99cb976d552af78f", "sha256_in_prefix": "ad282afc9a4717d7c7475971e77ab083fd7ed8bca9644fea99cb976d552af78f", "size_in_bytes": 81}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/__init__.py", "path_type": "hardlink", "sha256": "76d01b1a34c39a7fe08625396177e1c83cb4a610255d576de649590397d46be4", "sha256_in_prefix": "76d01b1a34c39a7fe08625396177e1c83cb4a610255d576de649590397d46be4", "size_in_bytes": 149}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/__init__.pyi", "path_type": "hardlink", "sha256": "e41dde4f338dd4106e38ba1bd6f09f97211bda549deaeb17410f82bfe85791e0", "sha256_in_prefix": "e41dde4f338dd4106e38ba1bd6f09f97211bda549deaeb17410f82bfe85791e0", "size_in_bytes": 43}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "7e5a2917ccebbf7396b685933dcc5ec0bd2139118e33dd329a7a42835c1b90ed", "sha256_in_prefix": "7e5a2917ccebbf7396b685933dcc5ec0bd2139118e33dd329a7a42835c1b90ed", "size_in_bytes": 296}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/__pycache__/more.cpython-39.pyc", "path_type": "hardlink", "sha256": "0d91272bc968e6656f2cb6c6ebbc55990eedf0bbebb3873ded9c00e353e1268a", "sha256_in_prefix": "0d91272bc968e6656f2cb6c6ebbc55990eedf0bbebb3873ded9c00e353e1268a", "size_in_bytes": 138366}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/__pycache__/recipes.cpython-39.pyc", "path_type": "hardlink", "sha256": "337714a030fbada5fdec809df87d99fbb51a238dcea51c362bb772a49b011e57", "sha256_in_prefix": "337714a030fbada5fdec809df87d99fbb51a238dcea51c362bb772a49b011e57", "size_in_bytes": 29089}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/more.py", "path_type": "hardlink", "sha256": "d44e64cc59dc44a4c3c34718bf5c915cc80376e9ecb4b41dd504ad7272fa53dd", "sha256_in_prefix": "d44e64cc59dc44a4c3c34718bf5c915cc80376e9ecb4b41dd504ad7272fa53dd", "size_in_bytes": 148370}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/more.pyi", "path_type": "hardlink", "sha256": "8975deaade3c3717bc5469885a99155ee2a947615836ebb60d4f2740b5820aed", "sha256_in_prefix": "8975deaade3c3717bc5469885a99155ee2a947615836ebb60d4f2740b5820aed", "size_in_bytes": 21484}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/recipes.py", "path_type": "hardlink", "sha256": "59e76185f846560aface28bc7c86c62559914f0d1929188b2a76010c626fe276", "sha256_in_prefix": "59e76185f846560aface28bc7c86c62559914f0d1929188b2a76010c626fe276", "size_in_bytes": 28591}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/recipes.pyi", "path_type": "hardlink", "sha256": "4ff99d1a970575facfdc94966f0cd83fd272355f86a3eed13dfa717dfb405a50", "sha256_in_prefix": "4ff99d1a970575facfdc94966f0cd83fd272355f86a3eed13dfa717dfb405a50", "size_in_bytes": 4617}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE", "path_type": "hardlink", "sha256": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "sha256_in_prefix": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "size_in_bytes": 197}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE.APACHE", "path_type": "hardlink", "sha256": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "sha256_in_prefix": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "size_in_bytes": 10174}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE.BSD", "path_type": "hardlink", "sha256": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "sha256_in_prefix": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "size_in_bytes": 1344}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "a211fceacea4e6621f4316364d2d0b7127c00de3856b8062082f9bc5957ea4db", "sha256_in_prefix": "a211fceacea4e6621f4316364d2d0b7127c00de3856b8062082f9bc5957ea4db", "size_in_bytes": 3204}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "6380eb5ccd0a63402b7f385b0046b52d814fc16dd612011e2f8882a977be03e3", "sha256_in_prefix": "6380eb5ccd0a63402b7f385b0046b52d814fc16dd612011e2f8882a977be03e3", "size_in_bytes": 2009}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "0a950253178741b44de54191407611268acee407fe432fdf1cc72d710f034862", "sha256_in_prefix": "0a950253178741b44de54191407611268acee407fe432fdf1cc72d710f034862", "size_in_bytes": 82}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__init__.py", "path_type": "hardlink", "sha256": "764e136bfbe67552716070dc7f286f40dc3c5773e0481a2628d5ea83e0f62436", "sha256_in_prefix": "764e136bfbe67552716070dc7f286f40dc3c5773e0481a2628d5ea83e0f62436", "size_in_bytes": 494}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "30b16a29fc123573aca8d4cead32a40b5258692d933f8728d0e2917059cec76e", "sha256_in_prefix": "30b16a29fc123573aca8d4cead32a40b5258692d933f8728d0e2917059cec76e", "size_in_bytes": 479}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_elffile.cpython-39.pyc", "path_type": "hardlink", "sha256": "2d0b05c9df8a940a3c7c0838f58bf9c8f7d928183fd95f5045d5838be4f9da9c", "sha256_in_prefix": "2d0b05c9df8a940a3c7c0838f58bf9c8f7d928183fd95f5045d5838be4f9da9c", "size_in_bytes": 3378}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_manylinux.cpython-39.pyc", "path_type": "hardlink", "sha256": "9e9801c048aec2bf59fb6a5f7c582cbd5d7b01182d51ba9d69beae268d910154", "sha256_in_prefix": "9e9801c048aec2bf59fb6a5f7c582cbd5d7b01182d51ba9d69beae268d910154", "size_in_bytes": 6434}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_musllinux.cpython-39.pyc", "path_type": "hardlink", "sha256": "71ae3b50fdb71b1f27ccf64c53cb69d695639cf10cc2eb57d0a44a5c99d79d20", "sha256_in_prefix": "71ae3b50fdb71b1f27ccf64c53cb69d695639cf10cc2eb57d0a44a5c99d79d20", "size_in_bytes": 3343}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_parser.cpython-39.pyc", "path_type": "hardlink", "sha256": "b3c6aea25212798af0042b343afaf38661cedd8d782847d44ffb0229f2d02715", "sha256_in_prefix": "b3c6aea25212798af0042b343afaf38661cedd8d782847d44ffb0229f2d02715", "size_in_bytes": 9053}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_structures.cpython-39.pyc", "path_type": "hardlink", "sha256": "0351982eef70485f2c7f4b5a6e334d948f0e1b6baae45f07a26aff7375da3080", "sha256_in_prefix": "0351982eef70485f2c7f4b5a6e334d948f0e1b6baae45f07a26aff7375da3080", "size_in_bytes": 2759}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_tokenizer.cpython-39.pyc", "path_type": "hardlink", "sha256": "0bb1f931a51d7a54036b938c248df2d40b49b8004e56408a05def6443605f1fa", "sha256_in_prefix": "0bb1f931a51d7a54036b938c248df2d40b49b8004e56408a05def6443605f1fa", "size_in_bytes": 5612}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/markers.cpython-39.pyc", "path_type": "hardlink", "sha256": "f7e92da7299365d125d6ef988a38fa633b0789b18f76dd2df99297da5fd5b584", "sha256_in_prefix": "f7e92da7299365d125d6ef988a38fa633b0789b18f76dd2df99297da5fd5b584", "size_in_bytes": 7698}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/metadata.cpython-39.pyc", "path_type": "hardlink", "sha256": "bfd3a720b04c0ccab1faf04290bc79921b1d210beb1bcf93aa0fe57aa31abf76", "sha256_in_prefix": "bfd3a720b04c0ccab1faf04290bc79921b1d210beb1bcf93aa0fe57aa31abf76", "size_in_bytes": 18344}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/requirements.cpython-39.pyc", "path_type": "hardlink", "sha256": "2da6e410516d3beac20ffb05600649090e2391dba72ccf1149399ee2d718f8fe", "sha256_in_prefix": "2da6e410516d3beac20ffb05600649090e2391dba72ccf1149399ee2d718f8fe", "size_in_bytes": 2824}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/specifiers.cpython-39.pyc", "path_type": "hardlink", "sha256": "c8b14c9d2a9866bf201b71bcbb8ab1bca69031d5f59ae4462720a7ed43b64b73", "sha256_in_prefix": "c8b14c9d2a9866bf201b71bcbb8ab1bca69031d5f59ae4462720a7ed43b64b73", "size_in_bytes": 31169}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/tags.cpython-39.pyc", "path_type": "hardlink", "sha256": "bf31212e631b4aba51032b84f95fce9605146f4e2d31e0dd46c351990323d826", "sha256_in_prefix": "bf31212e631b4aba51032b84f95fce9605146f4e2d31e0dd46c351990323d826", "size_in_bytes": 14892}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/utils.cpython-39.pyc", "path_type": "hardlink", "sha256": "a16dedb619efe79fd5feb536349a687fc971a9ffe761c19dbf09f2d1bfd41c27", "sha256_in_prefix": "a16dedb619efe79fd5feb536349a687fc971a9ffe761c19dbf09f2d1bfd41c27", "size_in_bytes": 4567}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/version.cpython-39.pyc", "path_type": "hardlink", "sha256": "988915f11e97c86089d54e1cfefb2a9087991ce57754d2b0ac24439d80ac2631", "sha256_in_prefix": "988915f11e97c86089d54e1cfefb2a9087991ce57754d2b0ac24439d80ac2631", "size_in_bytes": 15039}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/_elffile.py", "path_type": "hardlink", "sha256": "71f940400904db9b738589aafda0a2ef641f6d3fed9fcf75b4fcdfa5b7873b01", "sha256_in_prefix": "71f940400904db9b738589aafda0a2ef641f6d3fed9fcf75b4fcdfa5b7873b01", "size_in_bytes": 3306}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/_manylinux.py", "path_type": "hardlink", "sha256": "be5e4e0a8cf8931f341f9af05ca7975a397d58d2121a6af86604e94cff6553d7", "sha256_in_prefix": "be5e4e0a8cf8931f341f9af05ca7975a397d58d2121a6af86604e94cff6553d7", "size_in_bytes": 9612}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/_musllinux.py", "path_type": "hardlink", "sha256": "a7d66a35888e22d19e7bc29c64578717f61c76157018774aeabfbc9608b1bc64", "sha256_in_prefix": "a7d66a35888e22d19e7bc29c64578717f61c76157018774aeabfbc9608b1bc64", "size_in_bytes": 2694}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/_parser.py", "path_type": "hardlink", "sha256": "b3f4ef4ef0cd2b436b336401dd529385d58533835cd0fe899e439b925dcc8e93", "sha256_in_prefix": "b3f4ef4ef0cd2b436b336401dd529385d58533835cd0fe899e439b925dcc8e93", "size_in_bytes": 10236}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/_structures.py", "path_type": "hardlink", "sha256": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "sha256_in_prefix": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "size_in_bytes": 1431}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/_tokenizer.py", "path_type": "hardlink", "sha256": "27abf91fb273bdbfa0f35c69ff640008ac0eecbc47400ea292bc8c53bcd7c0df", "sha256_in_prefix": "27abf91fb273bdbfa0f35c69ff640008ac0eecbc47400ea292bc8c53bcd7c0df", "size_in_bytes": 5273}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/licenses/__init__.py", "path_type": "hardlink", "sha256": "d71e4cd671188dc83011b2edd1d5f0cf6ba48ebd7c0e20b30b4b2b690a89f96c", "sha256_in_prefix": "d71e4cd671188dc83011b2edd1d5f0cf6ba48ebd7c0e20b30b4b2b690a89f96c", "size_in_bytes": 5715}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/licenses/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "28cca6ccf6b19b6cf501facabacd7cf7d64449afd8967c0e0f5f41bd2ba1c594", "sha256_in_prefix": "28cca6ccf6b19b6cf501facabacd7cf7d64449afd8967c0e0f5f41bd2ba1c594", "size_in_bytes": 2566}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/licenses/__pycache__/_spdx.cpython-39.pyc", "path_type": "hardlink", "sha256": "e787d2943bd5ba2db0d06cab24b463c02600fb4c049239349a7c0cf90964662d", "sha256_in_prefix": "e787d2943bd5ba2db0d06cab24b463c02600fb4c049239349a7c0cf90964662d", "size_in_bytes": 29582}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/licenses/_spdx.py", "path_type": "hardlink", "sha256": "a009b5ced3c5c25b2608a7bb94002cbff38839f4b57160eef5b34191ebbeda7b", "sha256_in_prefix": "a009b5ced3c5c25b2608a7bb94002cbff38839f4b57160eef5b34191ebbeda7b", "size_in_bytes": 48398}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/markers.py", "path_type": "hardlink", "sha256": "73cf5337307b65d198864a2f9ba3d89aa1b21f15e561568b5b9f753c750d283f", "sha256_in_prefix": "73cf5337307b65d198864a2f9ba3d89aa1b21f15e561568b5b9f753c750d283f", "size_in_bytes": 10561}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/metadata.py", "path_type": "hardlink", "sha256": "60989b33b1987b8adef3ed1adce9579864be5c38131283b8b6506ddaadb90678", "sha256_in_prefix": "60989b33b1987b8adef3ed1adce9579864be5c38131283b8b6506ddaadb90678", "size_in_bytes": 34762}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/requirements.py", "path_type": "hardlink", "sha256": "818c9148075bac8c8a0d8ebaba02035108d132fc641f600b8a84e65f7b672faa", "sha256_in_prefix": "818c9148075bac8c8a0d8ebaba02035108d132fc641f600b8a84e65f7b672faa", "size_in_bytes": 2947}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/specifiers.py", "path_type": "hardlink", "sha256": "186d703cd31c2f47cc24eebcbc5e77c0a31dc277de84371a23eafd3694df8a50", "sha256_in_prefix": "186d703cd31c2f47cc24eebcbc5e77c0a31dc277de84371a23eafd3694df8a50", "size_in_bytes": 40074}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/tags.py", "path_type": "hardlink", "sha256": "085aab2730337365cd19ec5eac7fff4fe639230abb59bb185ec88b1112d6c58d", "sha256_in_prefix": "085aab2730337365cd19ec5eac7fff4fe639230abb59bb185ec88b1112d6c58d", "size_in_bytes": 21014}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/utils.py", "path_type": "hardlink", "sha256": "d05dc787d385b9182b8538066549792b6d85bf560fdad665d73ff680eea42620", "sha256_in_prefix": "d05dc787d385b9182b8538066549792b6d85bf560fdad665d73ff680eea42620", "size_in_bytes": 5050}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/version.py", "path_type": "hardlink", "sha256": "a257f2ba4fc33db7e5364278c0159eb57435edcef8c770c1e74d5d7a052fec36", "sha256_in_prefix": "a257f2ba4fc33db7e5364278c0159eb57435edcef8c770c1e74d5d7a052fec36", "size_in_bytes": 16676}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "ce6b227b4d46d4cb57474c2022fe57a557933bb89daf4596bdf9b12ac296b869", "sha256_in_prefix": "ce6b227b4d46d4cb57474c2022fe57a557933bb89daf4596bdf9b12ac296b869", "size_in_bytes": 11429}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "4c211d76d42ed40efc3acfcc866d8912a718afbca2b7e51849442366d6e99fe8", "sha256_in_prefix": "4c211d76d42ed40efc3acfcc866d8912a718afbca2b7e51849442366d6e99fe8", "size_in_bytes": 1642}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "cc431c46bf4aaf4df1d68cc6c20e6ff4d4012a7de49dda7a2d2a1295583e8e15", "sha256_in_prefix": "cc431c46bf4aaf4df1d68cc6c20e6ff4d4012a7de49dda7a2d2a1295583e8e15", "size_in_bytes": 87}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "29e0fd62e929850e86eb28c3fdccf0cefdf4fa94879011cffb3d0d4bed6d4db6", "sha256_in_prefix": "29e0fd62e929850e86eb28c3fdccf0cefdf4fa94879011cffb3d0d4bed6d4db6", "size_in_bytes": 1089}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__init__.py", "path_type": "hardlink", "sha256": "10c184f2a787451f42cc316bf242f7b40f217596678988d705565dd1419358ad", "sha256_in_prefix": "10c184f2a787451f42cc316bf242f7b40f217596678988d705565dd1419358ad", "size_in_bytes": 22225}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__main__.py", "path_type": "hardlink", "sha256": "1e7b14407a6205a893c70726c15c3e9c568f755359b5021d8b57960ed23e3332", "sha256_in_prefix": "1e7b14407a6205a893c70726c15c3e9c568f755359b5021d8b57960ed23e3332", "size_in_bytes": 1493}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "c04fc21f21612baafa8e87bfbd44bf4d2f97271378cddd87397fd5b2ddcc9ad5", "sha256_in_prefix": "c04fc21f21612baafa8e87bfbd44bf4d2f97271378cddd87397fd5b2ddcc9ad5", "size_in_bytes": 16415}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/__main__.cpython-39.pyc", "path_type": "hardlink", "sha256": "6e9fa1dfc3d0187fa0ad484aa4277c3d6e787ed1babab8f7851b05a65f8ca26c", "sha256_in_prefix": "6e9fa1dfc3d0187fa0ad484aa4277c3d6e787ed1babab8f7851b05a65f8ca26c", "size_in_bytes": 1333}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/android.cpython-39.pyc", "path_type": "hardlink", "sha256": "e9b900959c76266d317b63c424e3558ea730b66f402c301511827cbb7fe15d6a", "sha256_in_prefix": "e9b900959c76266d317b63c424e3558ea730b66f402c301511827cbb7fe15d6a", "size_in_bytes": 7928}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/api.cpython-39.pyc", "path_type": "hardlink", "sha256": "b9d57d77dcc48a433406163705f2a2e00d212544bf877324a7ec6e9bbe4b7aa5", "sha256_in_prefix": "b9d57d77dcc48a433406163705f2a2e00d212544bf877324a7ec6e9bbe4b7aa5", "size_in_bytes": 10315}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/macos.cpython-39.pyc", "path_type": "hardlink", "sha256": "4887e67e3d6b9493354972b67d7bf085c0ccba43d94eba797c556d64889d4b30", "sha256_in_prefix": "4887e67e3d6b9493354972b67d7bf085c0ccba43d94eba797c556d64889d4b30", "size_in_bytes": 6144}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/unix.cpython-39.pyc", "path_type": "hardlink", "sha256": "9f2740c832b8c8c26dad6331bcd5c71a233001dde6e853754e3e64b496c0ee17", "sha256_in_prefix": "9f2740c832b8c8c26dad6331bcd5c71a233001dde6e853754e3e64b496c0ee17", "size_in_bytes": 11039}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/version.cpython-39.pyc", "path_type": "hardlink", "sha256": "bc1f493d53ebb3e072d26441397aedaf1e14ff656f3acde94dfe30a6d95c0c9b", "sha256_in_prefix": "bc1f493d53ebb3e072d26441397aedaf1e14ff656f3acde94dfe30a6d95c0c9b", "size_in_bytes": 474}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/windows.cpython-39.pyc", "path_type": "hardlink", "sha256": "b7c4d381012d64fe04612487cf68868429832a250fcb17da7fef10fcb106e508", "sha256_in_prefix": "b7c4d381012d64fe04612487cf68868429832a250fcb17da7fef10fcb106e508", "size_in_bytes": 9201}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/android.py", "path_type": "hardlink", "sha256": "c595d8f49778e963acc53d94ebee47b0db4367e210ab170452b04b977858938a", "sha256_in_prefix": "c595d8f49778e963acc53d94ebee47b0db4367e210ab170452b04b977858938a", "size_in_bytes": 9016}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/api.py", "path_type": "hardlink", "sha256": "40161d51a736782e76d5e93fcb9dee0f50dcabe9495fc22049155de089c2eae7", "sha256_in_prefix": "40161d51a736782e76d5e93fcb9dee0f50dcabe9495fc22049155de089c2eae7", "size_in_bytes": 8996}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/macos.py", "path_type": "hardlink", "sha256": "c1fb6c6ecbeaea767458e4574a20ab64d9111f3fd62ae92d9746ba982ecc1642", "sha256_in_prefix": "c1fb6c6ecbeaea767458e4574a20ab64d9111f3fd62ae92d9746ba982ecc1642", "size_in_bytes": 5580}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/unix.py", "path_type": "hardlink", "sha256": "09c8bd5aab77e5d00cb20e874fd9d11874815b9a1b6f4a51dc01352499ec0978", "sha256_in_prefix": "09c8bd5aab77e5d00cb20e874fd9d11874815b9a1b6f4a51dc01352499ec0978", "size_in_bytes": 10643}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/version.py", "path_type": "hardlink", "sha256": "afb17bead6518e040aceba71fc8d3f64c40e314f8f4bb7869c70fbcc42b7281d", "sha256_in_prefix": "afb17bead6518e040aceba71fc8d3f64c40e314f8f4bb7869c70fbcc42b7281d", "size_in_bytes": 411}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/windows.py", "path_type": "hardlink", "sha256": "205a62a21501c313ed0b39722b036dc725b8264f2169ae96f28e7d99fac35d5a", "sha256_in_prefix": "205a62a21501c313ed0b39722b036dc725b8264f2169ae96f28e7d99fac35d5a", "size_in_bytes": 10125}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "b80816b0d530b8accb4c2211783790984a6e3b61922c2b5ee92f3372ab2742fe", "sha256_in_prefix": "b80816b0d530b8accb4c2211783790984a6e3b61922c2b5ee92f3372ab2742fe", "size_in_bytes": 1072}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "ccf0dc78a98fc0918b5ad67292b1e2c4bed65575a6246cd9d63c914f9942a0f2", "sha256_in_prefix": "ccf0dc78a98fc0918b5ad67292b1e2c4bed65575a6246cd9d63c914f9942a0f2", "size_in_bytes": 8875}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "0cb9f9a451a1e365ac54b4c88662e1da0cb54a72d16a5258fb0abff9d3e1c022", "sha256_in_prefix": "0cb9f9a451a1e365ac54b4c88662e1da0cb54a72d16a5258fb0abff9d3e1c022", "size_in_bytes": 999}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "8cf311fc3ce47385f889c42d9b3f35967358fe402c7e883baf2eeaa11bd82d7c", "sha256_in_prefix": "8cf311fc3ce47385f889c42d9b3f35967358fe402c7e883baf2eeaa11bd82d7c", "size_in_bytes": 81}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/__init__.py", "path_type": "hardlink", "sha256": "26153057ae830758381efb7551009531d7c2bbe220015f055e6bc353da27c5de", "sha256_in_prefix": "26153057ae830758381efb7551009531d7c2bbe220015f055e6bc353da27c5de", "size_in_bytes": 396}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "57ff1acd3ba903ed2706064f5043ca08df25d7632cf35f740d6cec282b6f610a", "sha256_in_prefix": "57ff1acd3ba903ed2706064f5043ca08df25d7632cf35f740d6cec282b6f610a", "size_in_bytes": 311}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/_parser.cpython-39.pyc", "path_type": "hardlink", "sha256": "693db1bdf76db9307f3d3374c0635c8b6aa9de9e042d388dd420f147c9d49973", "sha256_in_prefix": "693db1bdf76db9307f3d3374c0635c8b6aa9de9e042d388dd420f147c9d49973", "size_in_bytes": 16557}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/_re.cpython-39.pyc", "path_type": "hardlink", "sha256": "3724567052f7c001e5e3cbae2eb14956f46a8d4fb5b080a1eb4b9ec5fdf38b22", "sha256_in_prefix": "3724567052f7c001e5e3cbae2eb14956f46a8d4fb5b080a1eb4b9ec5fdf38b22", "size_in_bytes": 2790}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/_types.cpython-39.pyc", "path_type": "hardlink", "sha256": "af01ca3164efc957c12d82011be6063e88e91bd085e7aac08ff787718cdd840f", "sha256_in_prefix": "af01ca3164efc957c12d82011be6063e88e91bd085e7aac08ff787718cdd840f", "size_in_bytes": 281}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/_parser.py", "path_type": "hardlink", "sha256": "83df8435a00b4be07c768918a42bb35056a55a5a20ed3f922183232d9496aed3", "sha256_in_prefix": "83df8435a00b4be07c768918a42bb35056a55a5a20ed3f922183232d9496aed3", "size_in_bytes": 22633}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/_re.py", "path_type": "hardlink", "sha256": "75b8e0e428594f6dca6bdcfd0c73977ddb52a4fc147dd80c5e78fc34ea25cbec", "sha256_in_prefix": "75b8e0e428594f6dca6bdcfd0c73977ddb52a4fc147dd80c5e78fc34ea25cbec", "size_in_bytes": 2943}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/_types.py", "path_type": "hardlink", "sha256": "f864c6d9552a929c7032ace654ee05ef26ca75d21b027b801d77e65907138b74", "sha256_in_prefix": "f864c6d9552a929c7032ace654ee05ef26ca75d21b027b801d77e65907138b74", "size_in_bytes": 254}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/py.typed", "path_type": "hardlink", "sha256": "f0f8f2675695a10a5156fb7bd66bafbaae6a13e8d315990af862c792175e6e67", "sha256_in_prefix": "f0f8f2675695a10a5156fb7bd66bafbaae6a13e8d315990af862c792175e6e67", "size_in_bytes": 26}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "6163f7987dfb38d6bc320ce2b70b2f02b862bc41126516d552ef1cd43247e758", "sha256_in_prefix": "6163f7987dfb38d6bc320ce2b70b2f02b862bc41126516d552ef1cd43247e758", "size_in_bytes": 1130}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "cf675c1c0a744f08580855390de87cc77d676b312582e8d4cfdb5bb8fd298d21", "sha256_in_prefix": "cf675c1c0a744f08580855390de87cc77d676b312582e8d4cfdb5bb8fd298d21", "size_in_bytes": 3717}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "48a51959582478352275428ceecd78ef77d79ac9dae796e39a2eaf2540282552", "sha256_in_prefix": "48a51959582478352275428ceecd78ef77d79ac9dae796e39a2eaf2540282552", "size_in_bytes": 2402}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "aa9ecd43568bb624a0310aa8ea05a57c6a72d08217ce830999e4132e9cea1594", "sha256_in_prefix": "aa9ecd43568bb624a0310aa8ea05a57c6a72d08217ce830999e4132e9cea1594", "size_in_bytes": 48}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "e33dbc021b83a1dc114bf73527f97c1f9d6de50bb07d3b1eb24633971a7a82bb", "sha256_in_prefix": "e33dbc021b83a1dc114bf73527f97c1f9d6de50bb07d3b1eb24633971a7a82bb", "size_in_bytes": 10}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__init__.py", "path_type": "hardlink", "sha256": "3a7878c37f1e94f0a3b65714dc963d93787bd0d8ecc5722401f966427f99d056", "sha256_in_prefix": "3a7878c37f1e94f0a3b65714dc963d93787bd0d8ecc5722401f966427f99d056", "size_in_bytes": 2071}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "048ea5831af31c8af431a5225aecc9fdea11402497c6c57151bb98b53104b4a2", "sha256_in_prefix": "048ea5831af31c8af431a5225aecc9fdea11402497c6c57151bb98b53104b4a2", "size_in_bytes": 1737}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_checkers.cpython-39.pyc", "path_type": "hardlink", "sha256": "6f1e1c9e29afe9f67a78cf165aae2666007f2a26fefb361e38ebe9467f74ba7f", "sha256_in_prefix": "6f1e1c9e29afe9f67a78cf165aae2666007f2a26fefb361e38ebe9467f74ba7f", "size_in_bytes": 20052}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_config.cpython-39.pyc", "path_type": "hardlink", "sha256": "fa1b826ca01468c8d33f7e740faf805abc930f018bec4446f725b48374b8e252", "sha256_in_prefix": "fa1b826ca01468c8d33f7e740faf805abc930f018bec4446f725b48374b8e252", "size_in_bytes": 3389}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_decorators.cpython-39.pyc", "path_type": "hardlink", "sha256": "d56848fecf3f858be8bb9ed77fdfaca263c2b599a4305a90b853c06eb33843dd", "sha256_in_prefix": "d56848fecf3f858be8bb9ed77fdfaca263c2b599a4305a90b853c06eb33843dd", "size_in_bytes": 6990}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_exceptions.cpython-39.pyc", "path_type": "hardlink", "sha256": "b62885ff9da8a3f7bc7d1b84e0bab8daa302f72a45062ff5498a85ab33ac51e0", "sha256_in_prefix": "b62885ff9da8a3f7bc7d1b84e0bab8daa302f72a45062ff5498a85ab33ac51e0", "size_in_bytes": 2197}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_functions.cpython-39.pyc", "path_type": "hardlink", "sha256": "dbd2a126a39940d70058a537e00a3b059a2466bc0bb92177a5d8e11a23965342", "sha256_in_prefix": "dbd2a126a39940d70058a537e00a3b059a2466bc0bb92177a5d8e11a23965342", "size_in_bytes": 7602}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_importhook.cpython-39.pyc", "path_type": "hardlink", "sha256": "05be08bddfa0e8e6129df16ff9f6dfeb92fd2372498f092476a249ef90a074ff", "sha256_in_prefix": "05be08bddfa0e8e6129df16ff9f6dfeb92fd2372498f092476a249ef90a074ff", "size_in_bytes": 6771}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_memo.cpython-39.pyc", "path_type": "hardlink", "sha256": "afc4d3c0a57c878392a5b87e51938bbcc672ae910ca851ff2bf16d93d9561533", "sha256_in_prefix": "afc4d3c0a57c878392a5b87e51938bbcc672ae910ca851ff2bf16d93d9561533", "size_in_bytes": 1543}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_pytest_plugin.cpython-39.pyc", "path_type": "hardlink", "sha256": "9c59308147a2aed0406e3b52b476cba6d1e0e53d71d2c0bdcaf1eb8fbb34b989", "sha256_in_prefix": "9c59308147a2aed0406e3b52b476cba6d1e0e53d71d2c0bdcaf1eb8fbb34b989", "size_in_bytes": 3980}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_suppression.cpython-39.pyc", "path_type": "hardlink", "sha256": "605fbc2b77b04a4c3a7c55816ed25bf43377eef6f5a75e4a0671285e4a30ed92", "sha256_in_prefix": "605fbc2b77b04a4c3a7c55816ed25bf43377eef6f5a75e4a0671285e4a30ed92", "size_in_bytes": 2632}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_transformer.cpython-39.pyc", "path_type": "hardlink", "sha256": "cc5c9c73dd0ff4584116a6a69639e14c577f46d549fd71d92d61f36af491c062", "sha256_in_prefix": "cc5c9c73dd0ff4584116a6a69639e14c577f46d549fd71d92d61f36af491c062", "size_in_bytes": 27417}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_union_transformer.cpython-39.pyc", "path_type": "hardlink", "sha256": "586beae21389f3e1cec388966a0497fe0f104e534fd3eeca84c329a6b3e0a4de", "sha256_in_prefix": "586beae21389f3e1cec388966a0497fe0f104e534fd3eeca84c329a6b3e0a4de", "size_in_bytes": 1837}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_utils.cpython-39.pyc", "path_type": "hardlink", "sha256": "3314c80980b15e992285d219c69e71fcc0f6537bb74e8860732941e1660ddf88", "sha256_in_prefix": "3314c80980b15e992285d219c69e71fcc0f6537bb74e8860732941e1660ddf88", "size_in_bytes": 5079}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_checkers.py", "path_type": "hardlink", "sha256": "251ae02a271d3847c8068344b5e81808422586969df9ad6ed449bb1828f45822", "sha256_in_prefix": "251ae02a271d3847c8068344b5e81808422586969df9ad6ed449bb1828f45822", "size_in_bytes": 31360}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_config.py", "path_type": "hardlink", "sha256": "9c8cfc4300dafa814edcbf4ef3feacaf396677df6949bcb6c0e33859bec5fc1d", "sha256_in_prefix": "9c8cfc4300dafa814edcbf4ef3feacaf396677df6949bcb6c0e33859bec5fc1d", "size_in_bytes": 2846}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_decorators.py", "path_type": "hardlink", "sha256": "bfa76c21e5af3e113118b3ffc1717e4660d4ca365ffc0936f20ee0049fefd3ed", "sha256_in_prefix": "bfa76c21e5af3e113118b3ffc1717e4660d4ca365ffc0936f20ee0049fefd3ed", "size_in_bytes": 9033}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_exceptions.py", "path_type": "hardlink", "sha256": "6483de895f8505de449b0d8469677616f96caf08b8a1cc13d9f54604802d1dc4", "sha256_in_prefix": "6483de895f8505de449b0d8469677616f96caf08b8a1cc13d9f54604802d1dc4", "size_in_bytes": 1121}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_functions.py", "path_type": "hardlink", "sha256": "89b81200a6b9a6d226d5e47d0111b4052a3300524f14d01266a84f57241eaa28", "sha256_in_prefix": "89b81200a6b9a6d226d5e47d0111b4052a3300524f14d01266a84f57241eaa28", "size_in_bytes": 10393}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_importhook.py", "path_type": "hardlink", "sha256": "ba08c20ef15c756314ed4ba0aa5246f7522954da44231b51afef7db3487593b3", "sha256_in_prefix": "ba08c20ef15c756314ed4ba0aa5246f7522954da44231b51afef7db3487593b3", "size_in_bytes": 6389}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_memo.py", "path_type": "hardlink", "sha256": "d63b9057fbf19c3d8960a6d2ade6e242e8f8a0a1f3ea7ebbbfda5803e0822128", "sha256_in_prefix": "d63b9057fbf19c3d8960a6d2ade6e242e8f8a0a1f3ea7ebbbfda5803e0822128", "size_in_bytes": 1303}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_pytest_plugin.py", "path_type": "hardlink", "sha256": "f9f712aa4bf9e2b21f205f290dabd8e5840f923e0e5fc18cb7f94bec24120f82", "sha256_in_prefix": "f9f712aa4bf9e2b21f205f290dabd8e5840f923e0e5fc18cb7f94bec24120f82", "size_in_bytes": 4416}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_suppression.py", "path_type": "hardlink", "sha256": "5507f3c5cc086eede27f47fb54190a33b86460e03bb4d170f5aee3301b26320e", "sha256_in_prefix": "5507f3c5cc086eede27f47fb54190a33b86460e03bb4d170f5aee3301b26320e", "size_in_bytes": 2266}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_transformer.py", "path_type": "hardlink", "sha256": "f476bbfd085dc285278bfea1bdd63e8596ee11eae0a301eb34bdafcc721a9056", "sha256_in_prefix": "f476bbfd085dc285278bfea1bdd63e8596ee11eae0a301eb34bdafcc721a9056", "size_in_bytes": 44937}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_union_transformer.py", "path_type": "hardlink", "sha256": "bffe36afbfba1ee457d92a05c27c89f84e4f9715e955e5093c9475f8753da92a", "sha256_in_prefix": "bffe36afbfba1ee457d92a05c27c89f84e4f9715e955e5093c9475f8753da92a", "size_in_bytes": 1354}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_utils.py", "path_type": "hardlink", "sha256": "e4784ed6b3e7e5fd4ceb29a454012fed79a5cf5717fa3d0e9d3325c87aaaad1f", "sha256_in_prefix": "e4784ed6b3e7e5fd4ceb29a454012fed79a5cf5717fa3d0e9d3325c87aaaad1f", "size_in_bytes": 5270}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/LICENSE", "path_type": "hardlink", "sha256": "3b2f81fe21d181c499c59a256c8e1968455d6689d269aa85373bfb6af41da3bf", "sha256_in_prefix": "3b2f81fe21d181c499c59a256c8e1968455d6689d269aa85373bfb6af41da3bf", "size_in_bytes": 13936}, {"_path": "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "05e51021af1c9d86eb8d6c7e37c4cece733d5065b91a6d8389c5690ed440f16d", "sha256_in_prefix": "05e51021af1c9d86eb8d6c7e37c4cece733d5065b91a6d8389c5690ed440f16d", "size_in_bytes": 3018}, {"_path": "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "7710002d81971e632aa6a2fc33dc5d74aaf5d7caae22040a65d3e31503b05ee9", "sha256_in_prefix": "7710002d81971e632aa6a2fc33dc5d74aaf5d7caae22040a65d3e31503b05ee9", "size_in_bytes": 571}, {"_path": "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "sha256_in_prefix": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "size_in_bytes": 81}, {"_path": "Lib/site-packages/setuptools/_vendor/typing_extensions.py", "path_type": "hardlink", "sha256": "8307a4a721bd0d51b797158a5f89e2f2eee793759ee6c946f7c980f45dc3250c", "sha256_in_prefix": "8307a4a721bd0d51b797158a5f89e2f2eee793759ee6c946f7c980f45dc3250c", "size_in_bytes": 134451}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/LICENSE.txt", "path_type": "hardlink", "sha256": "30c23618679108f3e8ea1d2a658c7ca417bdfc891c98ef1a89fa4ff0c9828654", "sha256_in_prefix": "30c23618679108f3e8ea1d2a658c7ca417bdfc891c98ef1a89fa4ff0c9828654", "size_in_bytes": 1107}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "98acfce07ee6ee3b31272cde21c4d53918936f434f315dfd2af3886211a09a30", "sha256_in_prefix": "98acfce07ee6ee3b31272cde21c4d53918936f434f315dfd2af3886211a09a30", "size_in_bytes": 2313}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "d639f1ac7c993c3715bd42f27c616189b6b86792fdfd1b17afd77170d6e16984", "sha256_in_prefix": "d639f1ac7c993c3715bd42f27c616189b6b86792fdfd1b17afd77170d6e16984", "size_in_bytes": 4900}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "0a950253178741b44de54191407611268acee407fe432fdf1cc72d710f034862", "sha256_in_prefix": "0a950253178741b44de54191407611268acee407fe432fdf1cc72d710f034862", "size_in_bytes": 82}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "ad363505b90f1e1906326e10dc5d29233241cd6da4331a06d68ae27dfbc6740d", "sha256_in_prefix": "ad363505b90f1e1906326e10dc5d29233241cd6da4331a06d68ae27dfbc6740d", "size_in_bytes": 104}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__init__.py", "path_type": "hardlink", "sha256": "9abc4c9ef757002babfcb59e81b51f879839cac599addeb75099fcf74c2f18d9", "sha256_in_prefix": "9abc4c9ef757002babfcb59e81b51f879839cac599addeb75099fcf74c2f18d9", "size_in_bytes": 59}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__main__.py", "path_type": "hardlink", "sha256": "3643149ee4c219c3a4818d0804b8010950bf04619c58e471d8af236064b5d941", "sha256_in_prefix": "3643149ee4c219c3a4818d0804b8010950bf04619c58e471d8af236064b5d941", "size_in_bytes": 455}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "f6a63e93dc4819d916044ff92f0bd4ff601527d163a15412069248e63f584f61", "sha256_in_prefix": "f6a63e93dc4819d916044ff92f0bd4ff601527d163a15412069248e63f584f61", "size_in_bytes": 218}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/__main__.cpython-39.pyc", "path_type": "hardlink", "sha256": "78974a4382b0510b336c838dbac1ffe74900dac009e06fb554b5d9f1cc84f2e5", "sha256_in_prefix": "78974a4382b0510b336c838dbac1ffe74900dac009e06fb554b5d9f1cc84f2e5", "size_in_bytes": 610}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/_bdist_wheel.cpython-39.pyc", "path_type": "hardlink", "sha256": "ce04d13d7056bf170c9dd20da7ef6354e2ee070843c71c606d34ff2dedaa09d4", "sha256_in_prefix": "ce04d13d7056bf170c9dd20da7ef6354e2ee070843c71c606d34ff2dedaa09d4", "size_in_bytes": 15002}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/_setuptools_logging.cpython-39.pyc", "path_type": "hardlink", "sha256": "9d212e2fb97c9a865597c59519348e9b064625a18ca80c70f2a833537d5a37a4", "sha256_in_prefix": "9d212e2fb97c9a865597c59519348e9b064625a18ca80c70f2a833537d5a37a4", "size_in_bytes": 989}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/bdist_wheel.cpython-39.pyc", "path_type": "hardlink", "sha256": "a93742558a7f06906b7e129a30f2cf41011218f4e2fbc81add0c585d18751b4c", "sha256_in_prefix": "a93742558a7f06906b7e129a30f2cf41011218f4e2fbc81add0c585d18751b4c", "size_in_bytes": 674}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/macosx_libfile.cpython-39.pyc", "path_type": "hardlink", "sha256": "51ea66ecd1d2898244fe6cd90ba8a89045d76fd0635da4a4e9c8e4ceed897d0e", "sha256_in_prefix": "51ea66ecd1d2898244fe6cd90ba8a89045d76fd0635da4a4e9c8e4ceed897d0e", "size_in_bytes": 10469}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/metadata.cpython-39.pyc", "path_type": "hardlink", "sha256": "32745a9ecab9474d99f04dbc6bdfc46038a9a9c30294d56367491f8087f8b880", "sha256_in_prefix": "32745a9ecab9474d99f04dbc6bdfc46038a9a9c30294d56367491f8087f8b880", "size_in_bytes": 6052}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/util.cpython-39.pyc", "path_type": "hardlink", "sha256": "d462b373d2a96bfc6d43261da1e32f35427dcb8909a0e74ea2974effd64c7c3d", "sha256_in_prefix": "d462b373d2a96bfc6d43261da1e32f35427dcb8909a0e74ea2974effd64c7c3d", "size_in_bytes": 691}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/wheelfile.cpython-39.pyc", "path_type": "hardlink", "sha256": "24b2ba02e77c67ac6bd39ac87f2e80114ed74b0c6dc2bd9bf418024940612d52", "sha256_in_prefix": "24b2ba02e77c67ac6bd39ac87f2e80114ed74b0c6dc2bd9bf418024940612d52", "size_in_bytes": 6338}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/_bdist_wheel.py", "path_type": "hardlink", "sha256": "520842423487fe955f71987aa118f34b0fd342171fdda9d2c753a488b48bf363", "sha256_in_prefix": "520842423487fe955f71987aa118f34b0fd342171fdda9d2c753a488b48bf363", "size_in_bytes": 21694}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/_setuptools_logging.py", "path_type": "hardlink", "sha256": "fb9282fa59ded2294e5162037ce92a6a951618c15986e2980c86af219881e643", "sha256_in_prefix": "fb9282fa59ded2294e5162037ce92a6a951618c15986e2980c86af219881e643", "size_in_bytes": 781}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/bdist_wheel.py", "path_type": "hardlink", "sha256": "b697fd5ae7e248ed51b84320e683e121f486f0333388267fe26b82285ebd0aaa", "sha256_in_prefix": "b697fd5ae7e248ed51b84320e683e121f486f0333388267fe26b82285ebd0aaa", "size_in_bytes": 1107}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/__init__.py", "path_type": "hardlink", "sha256": "369abafe32a2d3776121c46799bb85870be2549c703b4b5812712158cbfd709a", "sha256_in_prefix": "369abafe32a2d3776121c46799bb85870be2549c703b4b5812712158cbfd709a", "size_in_bytes": 4402}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "e878b5fe16003ed5bd32a65c957f190c3fb76a27f215f69940bc8083696c9c00", "sha256_in_prefix": "e878b5fe16003ed5bd32a65c957f190c3fb76a27f215f69940bc8083696c9c00", "size_in_bytes": 4591}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/convert.cpython-39.pyc", "path_type": "hardlink", "sha256": "5e770396cbdc2a2eb99b37ed8702766f15b127b664540e865501e1d95af299fe", "sha256_in_prefix": "5e770396cbdc2a2eb99b37ed8702766f15b127b664540e865501e1d95af299fe", "size_in_bytes": 9465}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/pack.cpython-39.pyc", "path_type": "hardlink", "sha256": "a9b0d9c138d879c4e59c5509899f12580e10a57b65f4d58aa9706c644ce2f148", "sha256_in_prefix": "a9b0d9c138d879c4e59c5509899f12580e10a57b65f4d58aa9706c644ce2f148", "size_in_bytes": 3091}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/tags.cpython-39.pyc", "path_type": "hardlink", "sha256": "bfc424cb42e13b4da25e0d1fb7960449418bd02be0733292f0d793a362b08201", "sha256_in_prefix": "bfc424cb42e13b4da25e0d1fb7960449418bd02be0733292f0d793a362b08201", "size_in_bytes": 3804}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/unpack.cpython-39.pyc", "path_type": "hardlink", "sha256": "ac0ee18e58021ab914c12b7b5a1e302b7118236c725b4ddbf733d02ac7376ef6", "sha256_in_prefix": "ac0ee18e58021ab914c12b7b5a1e302b7118236c725b4ddbf733d02ac7376ef6", "size_in_bytes": 1062}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/convert.py", "path_type": "hardlink", "sha256": "062d27b445dbf674e5942c56793450e23fa73ecdeccd64842a2a46fc68273244", "sha256_in_prefix": "062d27b445dbf674e5942c56793450e23fa73ecdeccd64842a2a46fc68273244", "size_in_bytes": 12634}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/pack.py", "path_type": "hardlink", "sha256": "08015c1dd055ba5bec1d82659dd2953bb28c23d26a053673e628b43cac7108eb", "sha256_in_prefix": "08015c1dd055ba5bec1d82659dd2953bb28c23d26a053673e628b43cac7108eb", "size_in_bytes": 3103}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/tags.py", "path_type": "hardlink", "sha256": "947c3e2da5ab912e49cbfa96730fbaa528de34ceb20230e7a8a2371392534c25", "sha256_in_prefix": "947c3e2da5ab912e49cbfa96730fbaa528de34ceb20230e7a8a2371392534c25", "size_in_bytes": 4760}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/unpack.py", "path_type": "hardlink", "sha256": "63f27bca7c4f4a81454d3ec7d1f3206c195512bc320c670e6e099ee4c06ecf9f", "sha256_in_prefix": "63f27bca7c4f4a81454d3ec7d1f3206c195512bc320c670e6e099ee4c06ecf9f", "size_in_bytes": 1021}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/macosx_libfile.py", "path_type": "hardlink", "sha256": "935c7b084dcb3ed3951aa8fa3574359d319854f69e46b855cd41bf28fab7cc3b", "sha256_in_prefix": "935c7b084dcb3ed3951aa8fa3574359d319854f69e46b855cd41bf28fab7cc3b", "size_in_bytes": 16572}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/metadata.py", "path_type": "hardlink", "sha256": "242e29ee395066ed9b513010d9f7af92a2e383f5fa8273724612e7e8e50ed6d7", "sha256_in_prefix": "242e29ee395066ed9b513010d9f7af92a2e383f5fa8273724612e7e8e50ed6d7", "size_in_bytes": 6171}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/util.py", "path_type": "hardlink", "sha256": "68beda89b1f061481f73c5a5a252f9b577645780dab5b2716476f59301c52405", "sha256_in_prefix": "68beda89b1f061481f73c5a5a252f9b577645780dab5b2716476f59301c52405", "size_in_bytes": 423}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "ddfe877e5d154e9ada28ba44b38f854065269898b2c61c8e1b16a201bd2b2b75", "sha256_in_prefix": "ddfe877e5d154e9ada28ba44b38f854065269898b2c61c8e1b16a201bd2b2b75", "size_in_bytes": 154}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/LICENSE", "path_type": "hardlink", "sha256": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "sha256_in_prefix": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "size_in_bytes": 197}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/LICENSE.APACHE", "path_type": "hardlink", "sha256": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "sha256_in_prefix": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "size_in_bytes": 10174}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/LICENSE.BSD", "path_type": "hardlink", "sha256": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "sha256_in_prefix": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "size_in_bytes": 1344}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "38aff64d7f426b3242d4bf62ece6b13c914595f5caf558a77d405f25c789bc80", "sha256_in_prefix": "38aff64d7f426b3242d4bf62ece6b13c914595f5caf558a77d405f25c789bc80", "size_in_bytes": 164}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_elffile.cpython-39.pyc", "path_type": "hardlink", "sha256": "6c68bec5803c2e839b18d19305b150cbbc28be002079406ce1d58550799ee2ed", "sha256_in_prefix": "6c68bec5803c2e839b18d19305b150cbbc28be002079406ce1d58550799ee2ed", "size_in_bytes": 3334}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_manylinux.cpython-39.pyc", "path_type": "hardlink", "sha256": "13bf44ac93a213a431e35beb9a92866e984424c9658451ee32f375aa77bc5e49", "sha256_in_prefix": "13bf44ac93a213a431e35beb9a92866e984424c9658451ee32f375aa77bc5e49", "size_in_bytes": 6381}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_musllinux.cpython-39.pyc", "path_type": "hardlink", "sha256": "e04922231348eda9cdfb9cca025bfeaa47a5150c4d5ff5d941f2328ccc9e6e56", "sha256_in_prefix": "e04922231348eda9cdfb9cca025bfeaa47a5150c4d5ff5d941f2328ccc9e6e56", "size_in_bytes": 3296}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_parser.cpython-39.pyc", "path_type": "hardlink", "sha256": "a317e70f88c21a3a1ff30ebce61dc77475a3f6475e43c90b0efd8b73c9d45380", "sha256_in_prefix": "a317e70f88c21a3a1ff30ebce61dc77475a3f6475e43c90b0efd8b73c9d45380", "size_in_bytes": 8965}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_structures.cpython-39.pyc", "path_type": "hardlink", "sha256": "57962270b93d69554a6ff6ba45c8aa6f1a2028988a1b5938d14423872a6c582d", "sha256_in_prefix": "57962270b93d69554a6ff6ba45c8aa6f1a2028988a1b5938d14423872a6c582d", "size_in_bytes": 2774}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_tokenizer.cpython-39.pyc", "path_type": "hardlink", "sha256": "e215b0c651411bc2efc713b30b9609155d81c89d19002c7fb7745f9d60af199f", "sha256_in_prefix": "e215b0c651411bc2efc713b30b9609155d81c89d19002c7fb7745f9d60af199f", "size_in_bytes": 5671}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/markers.cpython-39.pyc", "path_type": "hardlink", "sha256": "f716858383d737ef8412b49f4e49dc2c225c0bf50ed349f5003a39bf736879cd", "sha256_in_prefix": "f716858383d737ef8412b49f4e49dc2c225c0bf50ed349f5003a39bf736879cd", "size_in_bytes": 6961}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/requirements.cpython-39.pyc", "path_type": "hardlink", "sha256": "24dc731c526b75723254a3d517d3042659b35c56bb941957cd0192a6821aa387", "sha256_in_prefix": "24dc731c526b75723254a3d517d3042659b35c56bb941957cd0192a6821aa387", "size_in_bytes": 2814}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/specifiers.cpython-39.pyc", "path_type": "hardlink", "sha256": "ea39cf1d8f1d1547d3aedd43b94faf818414bd0727a6a7fca736e5f230147368", "sha256_in_prefix": "ea39cf1d8f1d1547d3aedd43b94faf818414bd0727a6a7fca736e5f230147368", "size_in_bytes": 31015}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/tags.cpython-39.pyc", "path_type": "hardlink", "sha256": "0e9d4ebfafb86175510bf38a5c1388a08309bd0a3d4e8235c82ad6c7211aee17", "sha256_in_prefix": "0e9d4ebfafb86175510bf38a5c1388a08309bd0a3d4e8235c82ad6c7211aee17", "size_in_bytes": 13856}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/utils.cpython-39.pyc", "path_type": "hardlink", "sha256": "c72c574bc0436f911891abb3f416d7cd606b8665b7bf2faee55cf9be8f091948", "sha256_in_prefix": "c72c574bc0436f911891abb3f416d7cd606b8665b7bf2faee55cf9be8f091948", "size_in_bytes": 4572}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/version.cpython-39.pyc", "path_type": "hardlink", "sha256": "8f2431f447b956b0672a7dde571e188ebe9e4eebe911212d509a96f073d80598", "sha256_in_prefix": "8f2431f447b956b0672a7dde571e188ebe9e4eebe911212d509a96f073d80598", "size_in_bytes": 14292}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_elffile.py", "path_type": "hardlink", "sha256": "85b98af0e0fa67b7d8ea1c229c7114703d5bcbb73390688d62eed28671449369", "sha256_in_prefix": "85b98af0e0fa67b7d8ea1c229c7114703d5bcbb73390688d62eed28671449369", "size_in_bytes": 3266}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_manylinux.py", "path_type": "hardlink", "sha256": "3fbb1d479ffb5c1634f4b55860f8479b274c2482303d75ac878a2593be14ba3e", "sha256_in_prefix": "3fbb1d479ffb5c1634f4b55860f8479b274c2482303d75ac878a2593be14ba3e", "size_in_bytes": 9588}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_musllinux.py", "path_type": "hardlink", "sha256": "cf5b3c4e8da1434be99ff77e3b68b9ab11b010af1698694bb7777fdba57b35e6", "sha256_in_prefix": "cf5b3c4e8da1434be99ff77e3b68b9ab11b010af1698694bb7777fdba57b35e6", "size_in_bytes": 2674}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_parser.py", "path_type": "hardlink", "sha256": "e2d4f87a64a5daa4da53b553404d576bda358cc3c2b017b3b18071c8d31437eb", "sha256_in_prefix": "e2d4f87a64a5daa4da53b553404d576bda358cc3c2b017b3b18071c8d31437eb", "size_in_bytes": 10347}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_structures.py", "path_type": "hardlink", "sha256": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "sha256_in_prefix": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "size_in_bytes": 1431}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_tokenizer.py", "path_type": "hardlink", "sha256": "6a50ad6f05e138502614667a050fb0093485a11009db3fb2b087fbfff31327f9", "sha256_in_prefix": "6a50ad6f05e138502614667a050fb0093485a11009db3fb2b087fbfff31327f9", "size_in_bytes": 5292}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/markers.py", "path_type": "hardlink", "sha256": "fd348f2350612583bb069f40cd398743122a1c45576938e60e1f46fb0f2accf0", "sha256_in_prefix": "fd348f2350612583bb069f40cd398743122a1c45576938e60e1f46fb0f2accf0", "size_in_bytes": 8232}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/requirements.py", "path_type": "hardlink", "sha256": "760a01795a6b3eed9813a43c9c67f038f4e30131db45afd918bc978451259fa4", "sha256_in_prefix": "760a01795a6b3eed9813a43c9c67f038f4e30131db45afd918bc978451259fa4", "size_in_bytes": 2933}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/specifiers.py", "path_type": "hardlink", "sha256": "2164add12acb48fef685e5a1002f142f4786bdab3b5c84078ea8958957e63ca1", "sha256_in_prefix": "2164add12acb48fef685e5a1002f142f4786bdab3b5c84078ea8958957e63ca1", "size_in_bytes": 39778}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/tags.py", "path_type": "hardlink", "sha256": "7de7475e2387901c4d6535e8b57bfcb973e630553d69ef93281ba38181e281c0", "sha256_in_prefix": "7de7475e2387901c4d6535e8b57bfcb973e630553d69ef93281ba38181e281c0", "size_in_bytes": 18950}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/utils.py", "path_type": "hardlink", "sha256": "5e07663f7cb1f7ec101058ceecebcc8fd46311fe49951e4714547af6fed243d1", "sha256_in_prefix": "5e07663f7cb1f7ec101058ceecebcc8fd46311fe49951e4714547af6fed243d1", "size_in_bytes": 5268}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/version.py", "path_type": "hardlink", "sha256": "3c525a6190f1060cb191f6211f7490c38a9f13d202096ad39a2b6fab5e32ddbb", "sha256_in_prefix": "3c525a6190f1060cb191f6211f7490c38a9f13d202096ad39a2b6fab5e32ddbb", "size_in_bytes": 16234}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/vendor.txt", "path_type": "hardlink", "sha256": "67610d8c1d62e69adf7b3f0274cd5276bddce99c6fdab451a253292e60677001", "sha256_in_prefix": "67610d8c1d62e69adf7b3f0274cd5276bddce99c6fdab451a253292e60677001", "size_in_bytes": 16}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/wheelfile.py", "path_type": "hardlink", "sha256": "5120adb4d949c1a7f1b79d5860514a1bb8e7c73f1d7e16f2a8064bea331303db", "sha256_in_prefix": "5120adb4d949c1a7f1b79d5860514a1bb8e7c73f1d7e16f2a8064bea331303db", "size_in_bytes": 8411}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "508ae4fe43081c64b0b0a2828588b3a8cc3430c6693d1676662569400b0dfdb1", "sha256_in_prefix": "508ae4fe43081c64b0b0a2828588b3a8cc3430c6693d1676662569400b0dfdb1", "size_in_bytes": 3575}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "f316f2e03fd9ade7ebbc0b154706848e2bb8fd568b90935109f0d8e3ce2b9bfe", "sha256_in_prefix": "f316f2e03fd9ade7ebbc0b154706848e2bb8fd568b90935109f0d8e3ce2b9bfe", "size_in_bytes": 1039}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "8806dda121df686a817d56f65ee47d26a4901c2a0eb0eb46eb2f42fcb4a9a85c", "sha256_in_prefix": "8806dda121df686a817d56f65ee47d26a4901c2a0eb0eb46eb2f42fcb4a9a85c", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/__init__.py", "path_type": "hardlink", "sha256": "42e235834d06e1f440706b7e1ea6d5d285889264a079d086198b071d8ccd6bc0", "sha256_in_prefix": "42e235834d06e1f440706b7e1ea6d5d285889264a079d086198b071d8ccd6bc0", "size_in_bytes": 13412}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "1b6562fedb4b05314bee04976371fb2b9a557f36876b06e62b22aa200c83ae22", "sha256_in_prefix": "1b6562fedb4b05314bee04976371fb2b9a557f36876b06e62b22aa200c83ae22", "size_in_bytes": 16227}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/__pycache__/glob.cpython-39.pyc", "path_type": "hardlink", "sha256": "b354d6a92c04504255a0f3b5f91c8ac61bc4835fbe3b9f07f543d4ed8c92246b", "sha256_in_prefix": "b354d6a92c04504255a0f3b5f91c8ac61bc4835fbe3b9f07f543d4ed8c92246b", "size_in_bytes": 3929}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/compat/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "05014f815a595124e649da499e5e6e32b32bd2b7f4a368937b30b56963680f68", "sha256_in_prefix": "05014f815a595124e649da499e5e6e32b32bd2b7f4a368937b30b56963680f68", "size_in_bytes": 151}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/compat/__pycache__/py310.cpython-39.pyc", "path_type": "hardlink", "sha256": "9e11518230ee4db979ad4a7c86b66502a2d32ad239a58c73167a0694f06f4968", "sha256_in_prefix": "9e11518230ee4db979ad4a7c86b66502a2d32ad239a58c73167a0694f06f4968", "size_in_bytes": 384}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/compat/py310.py", "path_type": "hardlink", "sha256": "799a645b4cd1b6e9e484487c8e35f780219edb67a6a0a081270ef666de119210", "sha256_in_prefix": "799a645b4cd1b6e9e484487c8e35f780219edb67a6a0a081270ef666de119210", "size_in_bytes": 219}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/glob.py", "path_type": "hardlink", "sha256": "7ad5a99df1284727d4beb52c8bab13886984aef3f07ba1f363aa53f2383f9542", "sha256_in_prefix": "7ad5a99df1284727d4beb52c8bab13886984aef3f07ba1f363aa53f2383f9542", "size_in_bytes": 3082}, {"_path": "Lib/site-packages/setuptools/archive_util.py", "path_type": "hardlink", "sha256": "4e5ffae21493b5ce32f31ef16bdf2b15551b1b6e2802ba63ccb0181983f6fec2", "sha256_in_prefix": "4e5ffae21493b5ce32f31ef16bdf2b15551b1b6e2802ba63ccb0181983f6fec2", "size_in_bytes": 7356}, {"_path": "Lib/site-packages/setuptools/build_meta.py", "path_type": "hardlink", "sha256": "aebcbe2e8c2abd616cc46e909b94167ad1c919e113cd1762439f9bb386ce923a", "sha256_in_prefix": "aebcbe2e8c2abd616cc46e909b94167ad1c919e113cd1762439f9bb386ce923a", "size_in_bytes": 20446}, {"_path": "Lib/site-packages/setuptools/cli-32.exe", "path_type": "hardlink", "sha256": "32acc1bc543116cbe2cff10cb867772df2f254ff2634c870aef0b46c4b696fdb", "sha256_in_prefix": "32acc1bc543116cbe2cff10cb867772df2f254ff2634c870aef0b46c4b696fdb", "size_in_bytes": 11776}, {"_path": "Lib/site-packages/setuptools/cli-64.exe", "path_type": "hardlink", "sha256": "bbb3de5707629e6a60a0c238cd477b28f07f0066982fda953fa6fcec39073a4a", "sha256_in_prefix": "bbb3de5707629e6a60a0c238cd477b28f07f0066982fda953fa6fcec39073a4a", "size_in_bytes": 14336}, {"_path": "Lib/site-packages/setuptools/cli-arm64.exe", "path_type": "hardlink", "sha256": "b9a7d08da880dfac8bcf548eba4b06fb59b6f09b17d33148a0f6618328926c61", "sha256_in_prefix": "b9a7d08da880dfac8bcf548eba4b06fb59b6f09b17d33148a0f6618328926c61", "size_in_bytes": 13824}, {"_path": "Lib/site-packages/setuptools/cli.exe", "path_type": "hardlink", "sha256": "32acc1bc543116cbe2cff10cb867772df2f254ff2634c870aef0b46c4b696fdb", "sha256_in_prefix": "32acc1bc543116cbe2cff10cb867772df2f254ff2634c870aef0b46c4b696fdb", "size_in_bytes": 11776}, {"_path": "Lib/site-packages/setuptools/command/__init__.py", "path_type": "hardlink", "sha256": "c1d4ab94d4743fa9c2cfdfe816d08088091e14932c65ad633dca574f9ddfd123", "sha256_in_prefix": "c1d4ab94d4743fa9c2cfdfe816d08088091e14932c65ad633dca574f9ddfd123", "size_in_bytes": 803}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "3b9c6a33d8ab813c0b2ecf25692500e9f10f02f95f9f3666afc0cc52b9f62bb6", "sha256_in_prefix": "3b9c6a33d8ab813c0b2ecf25692500e9f10f02f95f9f3666afc0cc52b9f62bb6", "size_in_bytes": 379}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/_requirestxt.cpython-39.pyc", "path_type": "hardlink", "sha256": "d131e09ca9d412531fbeb0c4e3e3e716e42bdb916556a905b0890bbfcdbde533", "sha256_in_prefix": "d131e09ca9d412531fbeb0c4e3e3e716e42bdb916556a905b0890bbfcdbde533", "size_in_bytes": 4587}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/alias.cpython-39.pyc", "path_type": "hardlink", "sha256": "51d4898cf21cd907b23a18ea33eb451268e99d558211d5edf960948af0542549", "sha256_in_prefix": "51d4898cf21cd907b23a18ea33eb451268e99d558211d5edf960948af0542549", "size_in_bytes": 2344}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/bdist_egg.cpython-39.pyc", "path_type": "hardlink", "sha256": "8e755231163ae63c9334a1e34af1d40e639fef9f9b3fb212f8d4c8eb4098b550", "sha256_in_prefix": "8e755231163ae63c9334a1e34af1d40e639fef9f9b3fb212f8d4c8eb4098b550", "size_in_bytes": 13575}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/bdist_rpm.cpython-39.pyc", "path_type": "hardlink", "sha256": "90e480b77decf76106343d831a8a0e8a46631766e3c839176df7e7ac4e038960", "sha256_in_prefix": "90e480b77decf76106343d831a8a0e8a46631766e3c839176df7e7ac4e038960", "size_in_bytes": 1764}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/bdist_wheel.cpython-39.pyc", "path_type": "hardlink", "sha256": "82c1c857ec62619aa4261476952b5d014b6c30145dd5939aa732f9735328fe9d", "sha256_in_prefix": "82c1c857ec62619aa4261476952b5d014b6c30145dd5939aa732f9735328fe9d", "size_in_bytes": 15039}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/build.cpython-39.pyc", "path_type": "hardlink", "sha256": "12507670e884a2d29f1f35490c7d5e222d54ae3677677cb3e2bb13ea6ce20e21", "sha256_in_prefix": "12507670e884a2d29f1f35490c7d5e222d54ae3677677cb3e2bb13ea6ce20e21", "size_in_bytes": 5272}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/build_clib.cpython-39.pyc", "path_type": "hardlink", "sha256": "64388133691846ae3ec1d30f8e29dcff38829f8d929780601c234fa151a27d30", "sha256_in_prefix": "64388133691846ae3ec1d30f8e29dcff38829f8d929780601c234fa151a27d30", "size_in_bytes": 2484}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/build_ext.cpython-39.pyc", "path_type": "hardlink", "sha256": "5f740f79cbc20050d71d9d564013c1ae8e23659f54d21807404210c15559ea36", "sha256_in_prefix": "5f740f79cbc20050d71d9d564013c1ae8e23659f54d21807404210c15559ea36", "size_in_bytes": 13825}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/build_py.cpython-39.pyc", "path_type": "hardlink", "sha256": "b02108769afac2bcf9f2868428ca4759bca1c07ff877a8b533bfede9e92d9264", "sha256_in_prefix": "b02108769afac2bcf9f2868428ca4759bca1c07ff877a8b533bfede9e92d9264", "size_in_bytes": 14765}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/develop.cpython-39.pyc", "path_type": "hardlink", "sha256": "6eb58447370371450699803f99f65b79ef4c36355724a3a8495debb35c481484", "sha256_in_prefix": "6eb58447370371450699803f99f65b79ef4c36355724a3a8495debb35c481484", "size_in_bytes": 6073}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/dist_info.cpython-39.pyc", "path_type": "hardlink", "sha256": "0eabd5418f501ae289dc99a98f1164059390521dd091bed41a1e86f29989d118", "sha256_in_prefix": "0eabd5418f501ae289dc99a98f1164059390521dd091bed41a1e86f29989d118", "size_in_bytes": 3234}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/easy_install.cpython-39.pyc", "path_type": "hardlink", "sha256": "7c0d4843acaec6fbba6276abdc16face4502bef34da964ba6199309493ca85ec", "sha256_in_prefix": "7c0d4843acaec6fbba6276abdc16face4502bef34da964ba6199309493ca85ec", "size_in_bytes": 64979}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/editable_wheel.cpython-39.pyc", "path_type": "hardlink", "sha256": "2e28f78e9c2a6b3ad9f97d3922ddb17b17353cb34fd081c0655d70ca0dbe2324", "sha256_in_prefix": "2e28f78e9c2a6b3ad9f97d3922ddb17b17353cb34fd081c0655d70ca0dbe2324", "size_in_bytes": 35081}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/egg_info.cpython-39.pyc", "path_type": "hardlink", "sha256": "d1ca6154269fb91bc1d379b9c4c3174a5cb7148c9a93de7c41dd2234c6b93c23", "sha256_in_prefix": "d1ca6154269fb91bc1d379b9c4c3174a5cb7148c9a93de7c41dd2234c6b93c23", "size_in_bytes": 22351}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/install.cpython-39.pyc", "path_type": "hardlink", "sha256": "da5516ac135a5585d551800314cdc8d795b2b77956c23764826d16f3287d9e36", "sha256_in_prefix": "da5516ac135a5585d551800314cdc8d795b2b77956c23764826d16f3287d9e36", "size_in_bytes": 5385}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/install_egg_info.cpython-39.pyc", "path_type": "hardlink", "sha256": "563d6fe313a9ea6fb05f6000c57d379d837dabf879c3dec1073e1b4d9a7beae2", "sha256_in_prefix": "563d6fe313a9ea6fb05f6000c57d379d837dabf879c3dec1073e1b4d9a7beae2", "size_in_bytes": 2362}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/install_lib.cpython-39.pyc", "path_type": "hardlink", "sha256": "27d064b949f85911a5c695995b3a99b66dcc77507672cae1e19a880ee13ab72b", "sha256_in_prefix": "27d064b949f85911a5c695995b3a99b66dcc77507672cae1e19a880ee13ab72b", "size_in_bytes": 4380}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/install_scripts.cpython-39.pyc", "path_type": "hardlink", "sha256": "c1bcd538476ee9a3869fd1a7bae8775dfc36c91dcd3b5e4d09c24919b1c6b469", "sha256_in_prefix": "c1bcd538476ee9a3869fd1a7bae8775dfc36c91dcd3b5e4d09c24919b1c6b469", "size_in_bytes": 2546}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/rotate.cpython-39.pyc", "path_type": "hardlink", "sha256": "9f6e08c6d78a289db3b5ab5d7574e825a833b8c1457a7c14a8ae446917a1b6eb", "sha256_in_prefix": "9f6e08c6d78a289db3b5ab5d7574e825a833b8c1457a7c14a8ae446917a1b6eb", "size_in_bytes": 2610}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/saveopts.cpython-39.pyc", "path_type": "hardlink", "sha256": "b2beccd9558069f42d5039d0bd34e18605ba88f3025e9d4b0505464c9abe4631", "sha256_in_prefix": "b2beccd9558069f42d5039d0bd34e18605ba88f3025e9d4b0505464c9abe4631", "size_in_bytes": 890}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/sdist.cpython-39.pyc", "path_type": "hardlink", "sha256": "e7e5c5fd38ae829f8307ea32f4369be5661338572cb005881253b959a82a61b2", "sha256_in_prefix": "e7e5c5fd38ae829f8307ea32f4369be5661338572cb005881253b959a82a61b2", "size_in_bytes": 7909}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/setopt.cpython-39.pyc", "path_type": "hardlink", "sha256": "d3db855e2563ae8b021fd0fb7fb1628c02cf7d4f7c019a9c2f6bc7c7fd66e6c7", "sha256_in_prefix": "d3db855e2563ae8b021fd0fb7fb1628c02cf7d4f7c019a9c2f6bc7c7fd66e6c7", "size_in_bytes": 4725}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/test.cpython-39.pyc", "path_type": "hardlink", "sha256": "1eb080397bbb0e6504bdb427c580c13084cb180a9493a574e9299346adabc854", "sha256_in_prefix": "1eb080397bbb0e6504bdb427c580c13084cb180a9493a574e9299346adabc854", "size_in_bytes": 1698}, {"_path": "Lib/site-packages/setuptools/command/_requirestxt.py", "path_type": "hardlink", "sha256": "22d60c4c91a1fe2e53950b2d5ff9c5a29a848640b83c915a7412f665ddd5ebbd", "sha256_in_prefix": "22d60c4c91a1fe2e53950b2d5ff9c5a29a848640b83c915a7412f665ddd5ebbd", "size_in_bytes": 4228}, {"_path": "Lib/site-packages/setuptools/command/alias.py", "path_type": "hardlink", "sha256": "ac376b32ddf60d2eaa7f72bbb63659c870ff74c2ab9bbec05dc02dc7e9c14342", "sha256_in_prefix": "ac376b32ddf60d2eaa7f72bbb63659c870ff74c2ab9bbec05dc02dc7e9c14342", "size_in_bytes": 2380}, {"_path": "Lib/site-packages/setuptools/command/bdist_egg.py", "path_type": "hardlink", "sha256": "dde0ee710e1f75e60cb0b3bd3e105f63526470c2e1657827008ffd15d14db041", "sha256_in_prefix": "dde0ee710e1f75e60cb0b3bd3e105f63526470c2e1657827008ffd15d14db041", "size_in_bytes": 16972}, {"_path": "Lib/site-packages/setuptools/command/bdist_rpm.py", "path_type": "hardlink", "sha256": "2f2a88e3dc38f122a4d059ae1ec13d30bcd7d52b978cbed830d6d930566a1482", "sha256_in_prefix": "2f2a88e3dc38f122a4d059ae1ec13d30bcd7d52b978cbed830d6d930566a1482", "size_in_bytes": 1435}, {"_path": "Lib/site-packages/setuptools/command/bdist_wheel.py", "path_type": "hardlink", "sha256": "fcb7c61c1ec257fbb29dcaa53934844c48b6823542a0f2ae017732445a2aec2b", "sha256_in_prefix": "fcb7c61c1ec257fbb29dcaa53934844c48b6823542a0f2ae017732445a2aec2b", "size_in_bytes": 22246}, {"_path": "Lib/site-packages/setuptools/command/build.py", "path_type": "hardlink", "sha256": "788ed24cc111186644a73935b6f24df29f483a30005cc7062f3963bf69b02373", "sha256_in_prefix": "788ed24cc111186644a73935b6f24df29f483a30005cc7062f3963bf69b02373", "size_in_bytes": 6052}, {"_path": "Lib/site-packages/setuptools/command/build_clib.py", "path_type": "hardlink", "sha256": "01b8293c817fdea2fc7d9af724879b23e5874cc4c188c7eb164550cfc2b8d06e", "sha256_in_prefix": "01b8293c817fdea2fc7d9af724879b23e5874cc4c188c7eb164550cfc2b8d06e", "size_in_bytes": 4528}, {"_path": "Lib/site-packages/setuptools/command/build_ext.py", "path_type": "hardlink", "sha256": "6d41f8334362cda249aefd74c0af990f7b98d13c42499958403862c30cc7b253", "sha256_in_prefix": "6d41f8334362cda249aefd74c0af990f7b98d13c42499958403862c30cc7b253", "size_in_bytes": 18377}, {"_path": "Lib/site-packages/setuptools/command/build_py.py", "path_type": "hardlink", "sha256": "0c26e3bc1d7c9242fec542b9aef9739b40bab704de3b1361caf243c716bb7c82", "sha256_in_prefix": "0c26e3bc1d7c9242fec542b9aef9739b40bab704de3b1361caf243c716bb7c82", "size_in_bytes": 15539}, {"_path": "Lib/site-packages/setuptools/command/develop.py", "path_type": "hardlink", "sha256": "cd7db6d75f6c2351b581f27580d084e21920db36cb2b1d2e530bcd982e5b22ef", "sha256_in_prefix": "cd7db6d75f6c2351b581f27580d084e21920db36cb2b1d2e530bcd982e5b22ef", "size_in_bytes": 6886}, {"_path": "Lib/site-packages/setuptools/command/dist_info.py", "path_type": "hardlink", "sha256": "1d4ef9da22cb9a660c1dbb03060cf6b9b4639202686ee80ea7c1fbd4bcf30f2b", "sha256_in_prefix": "1d4ef9da22cb9a660c1dbb03060cf6b9b4639202686ee80ea7c1fbd4bcf30f2b", "size_in_bytes": 3450}, {"_path": "Lib/site-packages/setuptools/command/easy_install.py", "path_type": "hardlink", "sha256": "d19e2416513bf007b601f1d7613c591546b6b77aa536a5c2b50bb8275371f220", "sha256_in_prefix": "d19e2416513bf007b601f1d7613c591546b6b77aa536a5c2b50bb8275371f220", "size_in_bytes": 87870}, {"_path": "Lib/site-packages/setuptools/command/editable_wheel.py", "path_type": "hardlink", "sha256": "ddb062a51640dc4e29a10f0b11684c6048c78c2cea53fa4874ef3a0b7b7ba0d6", "sha256_in_prefix": "ddb062a51640dc4e29a10f0b11684c6048c78c2cea53fa4874ef3a0b7b7ba0d6", "size_in_bytes": 35624}, {"_path": "Lib/site-packages/setuptools/command/egg_info.py", "path_type": "hardlink", "sha256": "596528cd1dc3642ad6b134211d73b280c88451cae32d6a61113d3e66ca1cb26e", "sha256_in_prefix": "596528cd1dc3642ad6b134211d73b280c88451cae32d6a61113d3e66ca1cb26e", "size_in_bytes": 25982}, {"_path": "Lib/site-packages/setuptools/command/install.py", "path_type": "hardlink", "sha256": "3264c66fc9b547c7c9d1c73915358217abaafacd59266be9626f8dfc2b6a11a2", "sha256_in_prefix": "3264c66fc9b547c7c9d1c73915358217abaafacd59266be9626f8dfc2b6a11a2", "size_in_bytes": 7046}, {"_path": "Lib/site-packages/setuptools/command/install_egg_info.py", "path_type": "hardlink", "sha256": "dc8f483c21fb0f9f5287ee9a558cfe87ac30cb1abec24c6b2b02a0de70dd26ab", "sha256_in_prefix": "dc8f483c21fb0f9f5287ee9a558cfe87ac30cb1abec24c6b2b02a0de70dd26ab", "size_in_bytes": 2075}, {"_path": "Lib/site-packages/setuptools/command/install_lib.py", "path_type": "hardlink", "sha256": "f67d7f53cdde1dc1112ff6bfaeffcb8470a485794b76ac99e12741a30fbda9c1", "sha256_in_prefix": "f67d7f53cdde1dc1112ff6bfaeffcb8470a485794b76ac99e12741a30fbda9c1", "size_in_bytes": 4319}, {"_path": "Lib/site-packages/setuptools/command/install_scripts.py", "path_type": "hardlink", "sha256": "b553828f77bc39322b9282ff6c66d3e693a4b1dc597d06e51ff6dd2380ed555e", "sha256_in_prefix": "b553828f77bc39322b9282ff6c66d3e693a4b1dc597d06e51ff6dd2380ed555e", "size_in_bytes": 2637}, {"_path": "Lib/site-packages/setuptools/command/launcher manifest.xml", "path_type": "hardlink", "sha256": "c652db8d6ac1d35b4a0b4fa195590e2a48923dbccc9a5d9e38fb49fee7029db1", "sha256_in_prefix": "c652db8d6ac1d35b4a0b4fa195590e2a48923dbccc9a5d9e38fb49fee7029db1", "size_in_bytes": 628}, {"_path": "Lib/site-packages/setuptools/command/rotate.py", "path_type": "hardlink", "sha256": "5cd77f04410e5802475b515c2d3314596978401eb302e93b6fc556420dc51e8b", "sha256_in_prefix": "5cd77f04410e5802475b515c2d3314596978401eb302e93b6fc556420dc51e8b", "size_in_bytes": 2187}, {"_path": "Lib/site-packages/setuptools/command/saveopts.py", "path_type": "hardlink", "sha256": "369d0f55bed20fba136eef59f6ca2c4bb0fe0a4908914ef1e2096ee44b35b630", "sha256_in_prefix": "369d0f55bed20fba136eef59f6ca2c4bb0fe0a4908914ef1e2096ee44b35b630", "size_in_bytes": 692}, {"_path": "Lib/site-packages/setuptools/command/sdist.py", "path_type": "hardlink", "sha256": "25a426dbe79b5c8da4bf2ac18c928ff3234b3dae5e31b31e8acf3c09704c6259", "sha256_in_prefix": "25a426dbe79b5c8da4bf2ac18c928ff3234b3dae5e31b31e8acf3c09704c6259", "size_in_bytes": 7374}, {"_path": "Lib/site-packages/setuptools/command/setopt.py", "path_type": "hardlink", "sha256": "c59176442738001bc4f5e1c7033179d3e7e4420ddabbc7dc45455519de7c9375", "sha256_in_prefix": "c59176442738001bc4f5e1c7033179d3e7e4420ddabbc7dc45455519de7c9375", "size_in_bytes": 5100}, {"_path": "Lib/site-packages/setuptools/command/test.py", "path_type": "hardlink", "sha256": "93bc5cabb0fb6c47a18316ab6f0f9d5b702d98664e46acfc1e3291e85189de39", "sha256_in_prefix": "93bc5cabb0fb6c47a18316ab6f0f9d5b702d98664e46acfc1e3291e85189de39", "size_in_bytes": 1343}, {"_path": "Lib/site-packages/setuptools/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/compat/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "79de2afcc06e3c3e433b60e8c1f7a790f77981b5d695b065f78f72d756179a76", "sha256_in_prefix": "79de2afcc06e3c3e433b60e8c1f7a790f77981b5d695b065f78f72d756179a76", "size_in_bytes": 138}, {"_path": "Lib/site-packages/setuptools/compat/__pycache__/py310.cpython-39.pyc", "path_type": "hardlink", "sha256": "0c7c49e63249e8bb8f6c25aaaa7a79dfd3504a7df018eb519064830fd9020788", "sha256_in_prefix": "0c7c49e63249e8bb8f6c25aaaa7a79dfd3504a7df018eb519064830fd9020788", "size_in_bytes": 248}, {"_path": "Lib/site-packages/setuptools/compat/__pycache__/py311.cpython-39.pyc", "path_type": "hardlink", "sha256": "630845e0cbe63ac077c1c96f4aa00b4f2b9d409738811b511fca78b21a978629", "sha256_in_prefix": "630845e0cbe63ac077c1c96f4aa00b4f2b9d409738811b511fca78b21a978629", "size_in_bytes": 1092}, {"_path": "Lib/site-packages/setuptools/compat/__pycache__/py312.cpython-39.pyc", "path_type": "hardlink", "sha256": "6fdecd1845b22972e17c5ee80d8d03dbe2fb2e235dc0eded01d196662ea1d1fc", "sha256_in_prefix": "6fdecd1845b22972e17c5ee80d8d03dbe2fb2e235dc0eded01d196662ea1d1fc", "size_in_bytes": 367}, {"_path": "Lib/site-packages/setuptools/compat/__pycache__/py39.cpython-39.pyc", "path_type": "hardlink", "sha256": "946f37c8f1d41cc25fd9c5775edb3d08c0c00fa00ff7ecb9a414b88044deea25", "sha256_in_prefix": "946f37c8f1d41cc25fd9c5775edb3d08c0c00fa00ff7ecb9a414b88044deea25", "size_in_bytes": 220}, {"_path": "Lib/site-packages/setuptools/compat/py310.py", "path_type": "hardlink", "sha256": "f2cab059ccc872b9337806e16a29b8a4a55de2d5d975caa679b81dbf38e2d2b7", "sha256_in_prefix": "f2cab059ccc872b9337806e16a29b8a4a55de2d5d975caa679b81dbf38e2d2b7", "size_in_bytes": 141}, {"_path": "Lib/site-packages/setuptools/compat/py311.py", "path_type": "hardlink", "sha256": "7bab49005c1910ff36866301975d0761e4b2a5e968fd38b6c138ca65528bc0e1", "sha256_in_prefix": "7bab49005c1910ff36866301975d0761e4b2a5e968fd38b6c138ca65528bc0e1", "size_in_bytes": 790}, {"_path": "Lib/site-packages/setuptools/compat/py312.py", "path_type": "hardlink", "sha256": "bd8295b5dadd393b0efd1f747499045ec1707cc245b881497e5848807ae327e6", "sha256_in_prefix": "bd8295b5dadd393b0efd1f747499045ec1707cc245b881497e5848807ae327e6", "size_in_bytes": 366}, {"_path": "Lib/site-packages/setuptools/compat/py39.py", "path_type": "hardlink", "sha256": "04932d9e47dcab24df71caa3610c5fa11b54da74e759a104481564b214e25ea6", "sha256_in_prefix": "04932d9e47dcab24df71caa3610c5fa11b54da74e759a104481564b214e25ea6", "size_in_bytes": 493}, {"_path": "Lib/site-packages/setuptools/config/NOTICE", "path_type": "hardlink", "sha256": "2dddf08818297a3b89d43d95ff659d8da85741108c9136dfa3a4d856c0623bd8", "sha256_in_prefix": "2dddf08818297a3b89d43d95ff659d8da85741108c9136dfa3a4d856c0623bd8", "size_in_bytes": 493}, {"_path": "Lib/site-packages/setuptools/config/__init__.py", "path_type": "hardlink", "sha256": "6a23e72fd0499f53ba31f9ae357ca7f16d8ba7cbbdaa2cd156ac0f88e74f2236", "sha256_in_prefix": "6a23e72fd0499f53ba31f9ae357ca7f16d8ba7cbbdaa2cd156ac0f88e74f2236", "size_in_bytes": 1499}, {"_path": "Lib/site-packages/setuptools/config/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "b42b92d7ca725879d12af6573d3d9d9d48edad82896c3d94642d718325f227ba", "sha256_in_prefix": "b42b92d7ca725879d12af6573d3d9d9d48edad82896c3d94642d718325f227ba", "size_in_bytes": 1597}, {"_path": "Lib/site-packages/setuptools/config/__pycache__/_apply_pyprojecttoml.cpython-39.pyc", "path_type": "hardlink", "sha256": "cf0c30066ca04fc5f1b61952b92ea66d329c06ba1a0cf52d6b237febea0392cc", "sha256_in_prefix": "cf0c30066ca04fc5f1b61952b92ea66d329c06ba1a0cf52d6b237febea0392cc", "size_in_bytes": 17511}, {"_path": "Lib/site-packages/setuptools/config/__pycache__/expand.cpython-39.pyc", "path_type": "hardlink", "sha256": "c23c873bc79923eac28da1e7b79ca350c7e8be77c06abf310f0edb92b2bca75e", "sha256_in_prefix": "c23c873bc79923eac28da1e7b79ca350c7e8be77c06abf310f0edb92b2bca75e", "size_in_bytes": 17796}, {"_path": "Lib/site-packages/setuptools/config/__pycache__/pyprojecttoml.cpython-39.pyc", "path_type": "hardlink", "sha256": "97e8ccfceb4d5649647ef18c8572cf3f49cf4ef9dffc647f7a758342656cc6ca", "sha256_in_prefix": "97e8ccfceb4d5649647ef18c8572cf3f49cf4ef9dffc647f7a758342656cc6ca", "size_in_bytes": 15671}, {"_path": "Lib/site-packages/setuptools/config/__pycache__/setupcfg.cpython-39.pyc", "path_type": "hardlink", "sha256": "c9074acb39d70e7586b28c9014a1a6ca58292f5e6955ed9e20f130ca49da88c3", "sha256_in_prefix": "c9074acb39d70e7586b28c9014a1a6ca58292f5e6955ed9e20f130ca49da88c3", "size_in_bytes": 23772}, {"_path": "Lib/site-packages/setuptools/config/_apply_pyprojecttoml.py", "path_type": "hardlink", "sha256": "494c93c3b0366ed675941b9628de68e36f838b2bfde5e193898277ad93a71927", "sha256_in_prefix": "494c93c3b0366ed675941b9628de68e36f838b2bfde5e193898277ad93a71927", "size_in_bytes": 19120}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/NOTICE", "path_type": "hardlink", "sha256": "5d300dbfa643138b013b75ac9caeee591f951b8b0ee24288c34ccd926c4780c8", "sha256_in_prefix": "5d300dbfa643138b013b75ac9caeee591f951b8b0ee24288c34ccd926c4780c8", "size_in_bytes": 18737}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__init__.py", "path_type": "hardlink", "sha256": "767a7a4fb78f3f5479cf83ae0bb15dd9d905948aed21f8b351fbe91893fa9f3d", "sha256_in_prefix": "767a7a4fb78f3f5479cf83ae0bb15dd9d905948aed21f8b351fbe91893fa9f3d", "size_in_bytes": 1042}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "326b1e736b03d3a1d67f19ae3ce3d70ea2c2db66eb351b8aee0bd39f14414fb4", "sha256_in_prefix": "326b1e736b03d3a1d67f19ae3ce3d70ea2c2db66eb351b8aee0bd39f14414fb4", "size_in_bytes": 1457}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/error_reporting.cpython-39.pyc", "path_type": "hardlink", "sha256": "a02fc24580e26f06ad77d5f095b45f3bb5c2f3b637ea60308b1fcb108a3aef6b", "sha256_in_prefix": "a02fc24580e26f06ad77d5f095b45f3bb5c2f3b637ea60308b1fcb108a3aef6b", "size_in_bytes": 12032}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/extra_validations.cpython-39.pyc", "path_type": "hardlink", "sha256": "ccf312688740a7b06ea9a6c38f709abf8683d598c3b160675567ab7c2d0fbded", "sha256_in_prefix": "ccf312688740a7b06ea9a6c38f709abf8683d598c3b160675567ab7c2d0fbded", "size_in_bytes": 2346}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/fastjsonschema_exceptions.cpython-39.pyc", "path_type": "hardlink", "sha256": "075ad031983ae8a409a0f49fe6ec1d58619378f692067f3442862e6aff464987", "sha256_in_prefix": "075ad031983ae8a409a0f49fe6ec1d58619378f692067f3442862e6aff464987", "size_in_bytes": 2415}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/fastjsonschema_validations.cpython-39.pyc", "path_type": "hardlink", "sha256": "5a3b5a756ab0ff6813bbfc02549b352c6854bc9f11c0df51017f04415e8e445c", "sha256_in_prefix": "5a3b5a756ab0ff6813bbfc02549b352c6854bc9f11c0df51017f04415e8e445c", "size_in_bytes": 88661}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/formats.cpython-39.pyc", "path_type": "hardlink", "sha256": "31f467cb0ffa1cc4e11960722ad2083bfa77d357be717f6d8bcb56269adba8f5", "sha256_in_prefix": "31f467cb0ffa1cc4e11960722ad2083bfa77d357be717f6d8bcb56269adba8f5", "size_in_bytes": 13311}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/error_reporting.py", "path_type": "hardlink", "sha256": "99e95d0fb9c141da25421bc6fb8debd547be814d67ece440251f3abe1dd1aef9", "sha256_in_prefix": "99e95d0fb9c141da25421bc6fb8debd547be814d67ece440251f3abe1dd1aef9", "size_in_bytes": 11813}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/extra_validations.py", "path_type": "hardlink", "sha256": "f86506e52fbe8a363c59f5db7573e81e5eb2c06b32105f5db43a5e9d2a093c78", "sha256_in_prefix": "f86506e52fbe8a363c59f5db7573e81e5eb2c06b32105f5db43a5e9d2a093c78", "size_in_bytes": 2858}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_exceptions.py", "path_type": "hardlink", "sha256": "c3be3d260a8a8bc72504570e6dd71b655aac985e2827f401ca16754866d414dc", "sha256_in_prefix": "c3be3d260a8a8bc72504570e6dd71b655aac985e2827f401ca16754866d414dc", "size_in_bytes": 1612}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_validations.py", "path_type": "hardlink", "sha256": "162843e5970cea9efb04f674e021aa877044c153683cc289649032b89a64014d", "sha256_in_prefix": "162843e5970cea9efb04f674e021aa877044c153683cc289649032b89a64014d", "size_in_bytes": 354682}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/formats.py", "path_type": "hardlink", "sha256": "4c44e890904af618e5f9c560d6896ca23909c0bc5f3fbfdc860250366cc007df", "sha256_in_prefix": "4c44e890904af618e5f9c560d6896ca23909c0bc5f3fbfdc860250366cc007df", "size_in_bytes": 13564}, {"_path": "Lib/site-packages/setuptools/config/distutils.schema.json", "path_type": "hardlink", "sha256": "4dca77da44678703911b0ffda7a1848b4f258f6875e6d411cce6016f31a67015", "sha256_in_prefix": "4dca77da44678703911b0ffda7a1848b4f258f6875e6d411cce6016f31a67015", "size_in_bytes": 972}, {"_path": "Lib/site-packages/setuptools/config/expand.py", "path_type": "hardlink", "sha256": "24d024b510accb2441fab42875b3e70ae7262d6a9c62fcc20c2f046e7d99ef13", "sha256_in_prefix": "24d024b510accb2441fab42875b3e70ae7262d6a9c62fcc20c2f046e7d99ef13", "size_in_bytes": 16041}, {"_path": "Lib/site-packages/setuptools/config/pyprojecttoml.py", "path_type": "hardlink", "sha256": "60cbb93dd6c9248e5ace9ea447f6e833599f95fe67a8e03e227178b3a2e72e0c", "sha256_in_prefix": "60cbb93dd6c9248e5ace9ea447f6e833599f95fe67a8e03e227178b3a2e72e0c", "size_in_bytes": 18320}, {"_path": "Lib/site-packages/setuptools/config/setupcfg.py", "path_type": "hardlink", "sha256": "5590e4c04ec362fe3949b69d243f02c0aac3b625ef8e09652fc3d84afa110b28", "sha256_in_prefix": "5590e4c04ec362fe3949b69d243f02c0aac3b625ef8e09652fc3d84afa110b28", "size_in_bytes": 26575}, {"_path": "Lib/site-packages/setuptools/config/setuptools.schema.json", "path_type": "hardlink", "sha256": "759051b921276646ada1596dd645701bca1c4de45d3bb043d31bce58a1f9e0f6", "sha256_in_prefix": "759051b921276646ada1596dd645701bca1c4de45d3bb043d31bce58a1f9e0f6", "size_in_bytes": 16071}, {"_path": "Lib/site-packages/setuptools/depends.py", "path_type": "hardlink", "sha256": "8ca61f8e6b7fd9941856085bf0bf5b53b2c9e9eac7279cdef8afdf295d413179", "sha256_in_prefix": "8ca61f8e6b7fd9941856085bf0bf5b53b2c9e9eac7279cdef8afdf295d413179", "size_in_bytes": 5965}, {"_path": "Lib/site-packages/setuptools/discovery.py", "path_type": "hardlink", "sha256": "fb8d9cdd7870ce47e874328a3f9d02d98073af5d5f9dc020994cc174145bd3e4", "sha256_in_prefix": "fb8d9cdd7870ce47e874328a3f9d02d98073af5d5f9dc020994cc174145bd3e4", "size_in_bytes": 21258}, {"_path": "Lib/site-packages/setuptools/dist.py", "path_type": "hardlink", "sha256": "459cfb6a3f51c6a498ae2aa016864ebbeeca215f3672695a305c7da3066b0294", "sha256_in_prefix": "459cfb6a3f51c6a498ae2aa016864ebbeeca215f3672695a305c7da3066b0294", "size_in_bytes": 44897}, {"_path": "Lib/site-packages/setuptools/errors.py", "path_type": "hardlink", "sha256": "818db1d8f21a220cb4d724403510becdc0b0c430aa09272026808e6457b4ca2a", "sha256_in_prefix": "818db1d8f21a220cb4d724403510becdc0b0c430aa09272026808e6457b4ca2a", "size_in_bytes": 3024}, {"_path": "Lib/site-packages/setuptools/extension.py", "path_type": "hardlink", "sha256": "2829eff69ded826d1956ab60138e757f220bb26e210b2bce894b4ebbbf3b0fee", "sha256_in_prefix": "2829eff69ded826d1956ab60138e757f220bb26e210b2bce894b4ebbbf3b0fee", "size_in_bytes": 6683}, {"_path": "Lib/site-packages/setuptools/glob.py", "path_type": "hardlink", "sha256": "002fc1df70d8f20f821c42f10ec26bb7016ba62b9c48066c6a43c5752390ce17", "sha256_in_prefix": "002fc1df70d8f20f821c42f10ec26bb7016ba62b9c48066c6a43c5752390ce17", "size_in_bytes": 6062}, {"_path": "Lib/site-packages/setuptools/gui-32.exe", "path_type": "hardlink", "sha256": "85dae1e95d77845f2cb59bcac3d4afe74bbe4c91a9bcc5bf4a71cd43104dbe7c", "sha256_in_prefix": "85dae1e95d77845f2cb59bcac3d4afe74bbe4c91a9bcc5bf4a71cd43104dbe7c", "size_in_bytes": 11776}, {"_path": "Lib/site-packages/setuptools/gui-64.exe", "path_type": "hardlink", "sha256": "3471b6140eadc6412277dbbefe3fef8c345a0f1a59776086b80a3618c3a83e3b", "sha256_in_prefix": "3471b6140eadc6412277dbbefe3fef8c345a0f1a59776086b80a3618c3a83e3b", "size_in_bytes": 14336}, {"_path": "Lib/site-packages/setuptools/gui-arm64.exe", "path_type": "hardlink", "sha256": "e694f4743405c8b5926ff457db6fe7f1a12dec7c16a9c3864784d3f4e07ae097", "sha256_in_prefix": "e694f4743405c8b5926ff457db6fe7f1a12dec7c16a9c3864784d3f4e07ae097", "size_in_bytes": 13824}, {"_path": "Lib/site-packages/setuptools/gui.exe", "path_type": "hardlink", "sha256": "85dae1e95d77845f2cb59bcac3d4afe74bbe4c91a9bcc5bf4a71cd43104dbe7c", "sha256_in_prefix": "85dae1e95d77845f2cb59bcac3d4afe74bbe4c91a9bcc5bf4a71cd43104dbe7c", "size_in_bytes": 11776}, {"_path": "Lib/site-packages/setuptools/installer.py", "path_type": "hardlink", "sha256": "ff859e831e2bdcbd39b0ca37f8896a169f8ebb19d6c81aa3c8c67b2d64179a1f", "sha256_in_prefix": "ff859e831e2bdcbd39b0ca37f8896a169f8ebb19d6c81aa3c8c67b2d64179a1f", "size_in_bytes": 5110}, {"_path": "Lib/site-packages/setuptools/launch.py", "path_type": "hardlink", "sha256": "2016f9944bfaf42cae67d7b022b98a957875e7891d2e63f6f4b29f4cc9318a61", "sha256_in_prefix": "2016f9944bfaf42cae67d7b022b98a957875e7891d2e63f6f4b29f4cc9318a61", "size_in_bytes": 820}, {"_path": "Lib/site-packages/setuptools/logging.py", "path_type": "hardlink", "sha256": "5b5ea21c9d477025d8434471cab11f27cdc54f8d7be6d0ada1883e13ab92a552", "sha256_in_prefix": "5b5ea21c9d477025d8434471cab11f27cdc54f8d7be6d0ada1883e13ab92a552", "size_in_bytes": 1261}, {"_path": "Lib/site-packages/setuptools/modified.py", "path_type": "hardlink", "sha256": "6706df05f0853fcf25b6f6effdd243cfeb752ec4239ccf895298199e74198e33", "sha256_in_prefix": "6706df05f0853fcf25b6f6effdd243cfeb752ec4239ccf895298199e74198e33", "size_in_bytes": 568}, {"_path": "Lib/site-packages/setuptools/monkey.py", "path_type": "hardlink", "sha256": "1703169769f5bf66c76dea81cbea3d83cc9435a0246056eccc26d77bd77965af", "sha256_in_prefix": "1703169769f5bf66c76dea81cbea3d83cc9435a0246056eccc26d77bd77965af", "size_in_bytes": 3717}, {"_path": "Lib/site-packages/setuptools/msvc.py", "path_type": "hardlink", "sha256": "be6334a8be2b233aed0fda626bd644c2da99e0b6dbae02f4754d0400d558466f", "sha256_in_prefix": "be6334a8be2b233aed0fda626bd644c2da99e0b6dbae02f4754d0400d558466f", "size_in_bytes": 41631}, {"_path": "Lib/site-packages/setuptools/namespaces.py", "path_type": "hardlink", "sha256": "d861aa618d4134312132d05cd6b1d3bfb92582635545d92c25e5be2f57fefb2b", "sha256_in_prefix": "d861aa618d4134312132d05cd6b1d3bfb92582635545d92c25e5be2f57fefb2b", "size_in_bytes": 3171}, {"_path": "Lib/site-packages/setuptools/package_index.py", "path_type": "hardlink", "sha256": "229e1037982820092350ae941e0d34e6ea70c55f1ad948ed1045a3b0ff3174e9", "sha256_in_prefix": "229e1037982820092350ae941e0d34e6ea70c55f1ad948ed1045a3b0ff3174e9", "size_in_bytes": 40519}, {"_path": "Lib/site-packages/setuptools/sandbox.py", "path_type": "hardlink", "sha256": "7ccaad70eba2a473ba44a3e1d58079a3b77df3974b2a8efa5a1a77beb21e8b61", "sha256_in_prefix": "7ccaad70eba2a473ba44a3e1d58079a3b77df3974b2a8efa5a1a77beb21e8b61", "size_in_bytes": 14906}, {"_path": "Lib/site-packages/setuptools/script (dev).tmpl", "path_type": "hardlink", "sha256": "454cd0cc2414697b7074bb581d661b21098e6844b906baaad45bd403fb6efb92", "sha256_in_prefix": "454cd0cc2414697b7074bb581d661b21098e6844b906baaad45bd403fb6efb92", "size_in_bytes": 218}, {"_path": "Lib/site-packages/setuptools/script.tmpl", "path_type": "hardlink", "sha256": "5864ede6989eccedbb73e0dbc7a9794384f715fdb4039cfbf3bda1bf76808586", "sha256_in_prefix": "5864ede6989eccedbb73e0dbc7a9794384f715fdb4039cfbf3bda1bf76808586", "size_in_bytes": 138}, {"_path": "Lib/site-packages/setuptools/tests/__init__.py", "path_type": "hardlink", "sha256": "02705f96cda225b4c343398c29e2d1b7ef65c6168e1d454e644817bfcf54c2fb", "sha256_in_prefix": "02705f96cda225b4c343398c29e2d1b7ef65c6168e1d454e644817bfcf54c2fb", "size_in_bytes": 335}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "0303cb7203c8eab250399e374c7a6e6d523ebf32aa3da10cd5e3a2b1b0095b4e", "sha256_in_prefix": "0303cb7203c8eab250399e374c7a6e6d523ebf32aa3da10cd5e3a2b1b0095b4e", "size_in_bytes": 444}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/contexts.cpython-39.pyc", "path_type": "hardlink", "sha256": "807ffedc9d16e70cb7fd215cfe796545e2802642446305a2a13c6cf8f817b1b2", "sha256_in_prefix": "807ffedc9d16e70cb7fd215cfe796545e2802642446305a2a13c6cf8f817b1b2", "size_in_bytes": 3871}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/environment.cpython-39.pyc", "path_type": "hardlink", "sha256": "f6b0759aab6fab1ab1f763534a47d0e9e136d5899c3effc132bd74a369467cbb", "sha256_in_prefix": "f6b0759aab6fab1ab1f763534a47d0e9e136d5899c3effc132bd74a369467cbb", "size_in_bytes": 2077}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/fixtures.cpython-39.pyc", "path_type": "hardlink", "sha256": "0137ba141d662a98a5d833ffd699d8785b1d15bd78f0aa8e87af7b85ec036cf2", "sha256_in_prefix": "0137ba141d662a98a5d833ffd699d8785b1d15bd78f0aa8e87af7b85ec036cf2", "size_in_bytes": 4059}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/mod_with_constant.cpython-39.pyc", "path_type": "hardlink", "sha256": "344f4b2815633814e185634769287fffc4dbc1f2e67bfc075e0714a275f1259f", "sha256_in_prefix": "344f4b2815633814e185634769287fffc4dbc1f2e67bfc075e0714a275f1259f", "size_in_bytes": 167}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/namespaces.cpython-39.pyc", "path_type": "hardlink", "sha256": "80aa4c3249a8e658206a24b5b42de5518d0e3a36455fab4dcfc705bbf3612daf", "sha256_in_prefix": "80aa4c3249a8e658206a24b5b42de5518d0e3a36455fab4dcfc705bbf3612daf", "size_in_bytes": 2701}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/script-with-bom.cpython-39.pyc", "path_type": "hardlink", "sha256": "bfe0c1b74a47bf94e6c2315a5218f5a37bf202978d56e2694e2db31db7df2481", "sha256_in_prefix": "bfe0c1b74a47bf94e6c2315a5218f5a37bf202978d56e2694e2db31db7df2481", "size_in_bytes": 161}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/server.cpython-39.pyc", "path_type": "hardlink", "sha256": "8d8aaf885e8ff065ffac02afe25a51d1d6cbee2e22069e73108d3accb56013e4", "sha256_in_prefix": "8d8aaf885e8ff065ffac02afe25a51d1d6cbee2e22069e73108d3accb56013e4", "size_in_bytes": 3359}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_archive_util.cpython-39.pyc", "path_type": "hardlink", "sha256": "213d413653b1a868a3c7b17005ae3baa1e55dfebc4cd7fd8a784e183698b90ae", "sha256_in_prefix": "213d413653b1a868a3c7b17005ae3baa1e55dfebc4cd7fd8a784e183698b90ae", "size_in_bytes": 1121}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_bdist_deprecations.cpython-39.pyc", "path_type": "hardlink", "sha256": "0dbbc5e45b3e0e94a5f130ce913e8aacf62ff61ece6c8be238e8fe455239e992", "sha256_in_prefix": "0dbbc5e45b3e0e94a5f130ce913e8aacf62ff61ece6c8be238e8fe455239e992", "size_in_bytes": 1010}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_bdist_egg.cpython-39.pyc", "path_type": "hardlink", "sha256": "1d026d13341143d265306e37430260aa4e28124cafc0a9d610d48ebb1dea4465", "sha256_in_prefix": "1d026d13341143d265306e37430260aa4e28124cafc0a9d610d48ebb1dea4465", "size_in_bytes": 2322}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_bdist_wheel.cpython-39.pyc", "path_type": "hardlink", "sha256": "3c095e0a80623d1faf0054e045bb593f8c8fdc1f758d397477a9d291222392d6", "sha256_in_prefix": "3c095e0a80623d1faf0054e045bb593f8c8fdc1f758d397477a9d291222392d6", "size_in_bytes": 23228}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_build.cpython-39.pyc", "path_type": "hardlink", "sha256": "444b22ec8127a224aafaf3bb4619c04cb424244c26b54ed633d09282ceff8810", "sha256_in_prefix": "444b22ec8127a224aafaf3bb4619c04cb424244c26b54ed633d09282ceff8810", "size_in_bytes": 1377}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_build_clib.cpython-39.pyc", "path_type": "hardlink", "sha256": "7c9665847403751ece5570d3d3b9abe0ce173a21e8331157065e9bb7903505a0", "sha256_in_prefix": "7c9665847403751ece5570d3d3b9abe0ce173a21e8331157065e9bb7903505a0", "size_in_bytes": 2236}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_build_ext.cpython-39.pyc", "path_type": "hardlink", "sha256": "690b83177ab2350da9b04edf6361922c0898ee7e417f91ffafb1ee799ad11421", "sha256_in_prefix": "690b83177ab2350da9b04edf6361922c0898ee7e417f91ffafb1ee799ad11421", "size_in_bytes": 9365}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_build_meta.cpython-39.pyc", "path_type": "hardlink", "sha256": "a5107deda393b3bdd7de82b0dcabdef261b61de65c438e02a5547e4d1122f3db", "sha256_in_prefix": "a5107deda393b3bdd7de82b0dcabdef261b61de65c438e02a5547e4d1122f3db", "size_in_bytes": 29259}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_build_py.cpython-39.pyc", "path_type": "hardlink", "sha256": "b90e471c64ef85419ebc4a17fe996f4d36456690a6b58ad4a540cae4ce0335cf", "sha256_in_prefix": "b90e471c64ef85419ebc4a17fe996f4d36456690a6b58ad4a540cae4ce0335cf", "size_in_bytes": 10816}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_config_discovery.cpython-39.pyc", "path_type": "hardlink", "sha256": "9809863e037730bee26926129c99d047f36dbe2e00ac6a399d160da78436cf0d", "sha256_in_prefix": "9809863e037730bee26926129c99d047f36dbe2e00ac6a399d160da78436cf0d", "size_in_bytes": 20452}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_core_metadata.cpython-39.pyc", "path_type": "hardlink", "sha256": "c22d79a001ae97870002391c998060b0f2222c5fb8e13d7136bc5e9df4f2be3a", "sha256_in_prefix": "c22d79a001ae97870002391c998060b0f2222c5fb8e13d7136bc5e9df4f2be3a", "size_in_bytes": 15737}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_depends.cpython-39.pyc", "path_type": "hardlink", "sha256": "21e010bb2f343e6d7de33ff4b9fdbaf4eba487c5f5db5ce2edd978f9279852e1", "sha256_in_prefix": "21e010bb2f343e6d7de33ff4b9fdbaf4eba487c5f5db5ce2edd978f9279852e1", "size_in_bytes": 744}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_develop.cpython-39.pyc", "path_type": "hardlink", "sha256": "5302b6cf9c3ed4c877ddef91c40d1b68d50c8e9ada19b57751456f10c539aa25", "sha256_in_prefix": "5302b6cf9c3ed4c877ddef91c40d1b68d50c8e9ada19b57751456f10c539aa25", "size_in_bytes": 5601}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_dist.cpython-39.pyc", "path_type": "hardlink", "sha256": "53a312cc00b854337aa13b0147fc35bf6aee9bf684e6d6a32f516437b3d292ed", "sha256_in_prefix": "53a312cc00b854337aa13b0147fc35bf6aee9bf684e6d6a32f516437b3d292ed", "size_in_bytes": 6883}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_dist_info.cpython-39.pyc", "path_type": "hardlink", "sha256": "35eea43f1d0b44f53ef5f7d58c8f3ca27b27b0a6ba07d8ae8c465ef4ae37dcfc", "sha256_in_prefix": "35eea43f1d0b44f53ef5f7d58c8f3ca27b27b0a6ba07d8ae8c465ef4ae37dcfc", "size_in_bytes": 6979}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_distutils_adoption.cpython-39.pyc", "path_type": "hardlink", "sha256": "8982f74efddd4cc2db3569af8435f709b5e66f758fbfa7a349c86c1daba295f1", "sha256_in_prefix": "8982f74efddd4cc2db3569af8435f709b5e66f758fbfa7a349c86c1daba295f1", "size_in_bytes": 5598}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_easy_install.cpython-39.pyc", "path_type": "hardlink", "sha256": "a435a3928d1d40ad17e6cf08bd82be48d6bd71719f7d694837b14bbb897a73c6", "sha256_in_prefix": "a435a3928d1d40ad17e6cf08bd82be48d6bd71719f7d694837b14bbb897a73c6", "size_in_bytes": 44495}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_editable_install.cpython-39.pyc", "path_type": "hardlink", "sha256": "2c13092261e43052e2e10d2fe4f4d88b855288ca0a502080f8204bedbe4b7ce4", "sha256_in_prefix": "2c13092261e43052e2e10d2fe4f4d88b855288ca0a502080f8204bedbe4b7ce4", "size_in_bytes": 36636}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_egg_info.cpython-39.pyc", "path_type": "hardlink", "sha256": "bf62b3fcf2b8e9a881dbcc46a9ee790f24ebca89f2b62fe82836339d1f1cad1c", "sha256_in_prefix": "bf62b3fcf2b8e9a881dbcc46a9ee790f24ebca89f2b62fe82836339d1f1cad1c", "size_in_bytes": 31233}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_extern.cpython-39.pyc", "path_type": "hardlink", "sha256": "ea066a4d12f020bd0131854467c8f8fabf267801d7ed230bbf7612c88732e1ec", "sha256_in_prefix": "ea066a4d12f020bd0131854467c8f8fabf267801d7ed230bbf7612c88732e1ec", "size_in_bytes": 592}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_find_packages.cpython-39.pyc", "path_type": "hardlink", "sha256": "dc0d02975d30275c794089c361cd43ebfb840b3f86cc05ea5dbeddeeff0b564d", "sha256_in_prefix": "dc0d02975d30275c794089c361cd43ebfb840b3f86cc05ea5dbeddeeff0b564d", "size_in_bytes": 8119}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_find_py_modules.cpython-39.pyc", "path_type": "hardlink", "sha256": "bae1fe994d9e5f0c9b388694fb1f4a7bc6cd8350b22e5b684b73d5b46496e8dc", "sha256_in_prefix": "bae1fe994d9e5f0c9b388694fb1f4a7bc6cd8350b22e5b684b73d5b46496e8dc", "size_in_bytes": 2657}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_glob.cpython-39.pyc", "path_type": "hardlink", "sha256": "55612127938e9579975befa83cbe73e88e9327fc20be07a4f4515c4307e1d248", "sha256_in_prefix": "55612127938e9579975befa83cbe73e88e9327fc20be07a4f4515c4307e1d248", "size_in_bytes": 949}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_install_scripts.cpython-39.pyc", "path_type": "hardlink", "sha256": "a48852879a7144ffff09a2d79efb1cd1de83fcde984b0bdecec4e77e9e423464", "sha256_in_prefix": "a48852879a7144ffff09a2d79efb1cd1de83fcde984b0bdecec4e77e9e423464", "size_in_bytes": 3797}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_logging.cpython-39.pyc", "path_type": "hardlink", "sha256": "c276a91971756b63b715730f764ca0dfc9c4f415bb8df6eea13dc6042162d13e", "sha256_in_prefix": "c276a91971756b63b715730f764ca0dfc9c4f415bb8df6eea13dc6042162d13e", "size_in_bytes": 2025}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_manifest.cpython-39.pyc", "path_type": "hardlink", "sha256": "6fde72c03dd2266348487d607af61f4fbba2ce1f7f251e0929fb869c4f38cc24", "sha256_in_prefix": "6fde72c03dd2266348487d607af61f4fbba2ce1f7f251e0929fb869c4f38cc24", "size_in_bytes": 16026}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_namespaces.cpython-39.pyc", "path_type": "hardlink", "sha256": "658cf01f5e2e6a89205e565ba25b6fba1295e9ca2eba5b50f4018f8ebad973f9", "sha256_in_prefix": "658cf01f5e2e6a89205e565ba25b6fba1295e9ca2eba5b50f4018f8ebad973f9", "size_in_bytes": 3445}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_packageindex.cpython-39.pyc", "path_type": "hardlink", "sha256": "0be644b4e7f212e47561205c1e58038bff59b83e0be415ba21b4f7257134937d", "sha256_in_prefix": "0be644b4e7f212e47561205c1e58038bff59b83e0be415ba21b4f7257134937d", "size_in_bytes": 13923}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_sandbox.cpython-39.pyc", "path_type": "hardlink", "sha256": "3cc04cfb3fff62fe92900188019cb340ef1d09694d928e2dcd23bbfe0df4eee9", "sha256_in_prefix": "3cc04cfb3fff62fe92900188019cb340ef1d09694d928e2dcd23bbfe0df4eee9", "size_in_bytes": 6258}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_sdist.cpython-39.pyc", "path_type": "hardlink", "sha256": "c8219081818381e30450843cc8bfe10ab1760138c18a8b0e3422f19171ed63d0", "sha256_in_prefix": "c8219081818381e30450843cc8bfe10ab1760138c18a8b0e3422f19171ed63d0", "size_in_bytes": 28531}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_setopt.cpython-39.pyc", "path_type": "hardlink", "sha256": "29070348259b7b4f312d99eef7105ced87634345fd9091aa67046efbd0d15e40", "sha256_in_prefix": "29070348259b7b4f312d99eef7105ced87634345fd9091aa67046efbd0d15e40", "size_in_bytes": 1871}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_setuptools.cpython-39.pyc", "path_type": "hardlink", "sha256": "1c6dc89bcb32c579d13d5550b6e148fbc59683644a0b56ce221ef6885479145b", "sha256_in_prefix": "1c6dc89bcb32c579d13d5550b6e148fbc59683644a0b56ce221ef6885479145b", "size_in_bytes": 9999}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_shutil_wrapper.cpython-39.pyc", "path_type": "hardlink", "sha256": "1299e1e4b9d4a913f8e24bb284da4b56a36c6163f72284b1319747c6bdb4e239", "sha256_in_prefix": "1299e1e4b9d4a913f8e24bb284da4b56a36c6163f72284b1319747c6bdb4e239", "size_in_bytes": 817}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_unicode_utils.cpython-39.pyc", "path_type": "hardlink", "sha256": "f14d90cc1819088b1e56d8e4cb8fd430797292ac9b5e16b5600564415b98568c", "sha256_in_prefix": "f14d90cc1819088b1e56d8e4cb8fd430797292ac9b5e16b5600564415b98568c", "size_in_bytes": 671}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_virtualenv.cpython-39.pyc", "path_type": "hardlink", "sha256": "355b5e26deebfad431c328fd4337cd1e3e843a24b19cf104707efebb727c5913", "sha256_in_prefix": "355b5e26deebfad431c328fd4337cd1e3e843a24b19cf104707efebb727c5913", "size_in_bytes": 2749}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_warnings.cpython-39.pyc", "path_type": "hardlink", "sha256": "9d3d1e16fe69b39763b2f36be755b5828eaddce3334da9c9f009e68f9110bdf8", "sha256_in_prefix": "9d3d1e16fe69b39763b2f36be755b5828eaddce3334da9c9f009e68f9110bdf8", "size_in_bytes": 3176}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_wheel.cpython-39.pyc", "path_type": "hardlink", "sha256": "9e1de3189be8a4d8762c55a5e04ea48f3add799b0f379d42e58e37687eea66a1", "sha256_in_prefix": "9e1de3189be8a4d8762c55a5e04ea48f3add799b0f379d42e58e37687eea66a1", "size_in_bytes": 13075}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_windows_wrappers.cpython-39.pyc", "path_type": "hardlink", "sha256": "8bb08dd4713eeedb260d28403afe631a55d07fa8af2a071d7bca06c159dc6537", "sha256_in_prefix": "8bb08dd4713eeedb260d28403afe631a55d07fa8af2a071d7bca06c159dc6537", "size_in_bytes": 7366}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/text.cpython-39.pyc", "path_type": "hardlink", "sha256": "25994a44b90039b94c5bb03afce2f18070e0a6157b7c9b65d654bd7e7d5343ba", "sha256_in_prefix": "25994a44b90039b94c5bb03afce2f18070e0a6157b7c9b65d654bd7e7d5343ba", "size_in_bytes": 379}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/textwrap.cpython-39.pyc", "path_type": "hardlink", "sha256": "cf16498964bdc69a6ef88527ced7c0fdca0c400e0a175285f1585d0003968e96", "sha256_in_prefix": "cf16498964bdc69a6ef88527ced7c0fdca0c400e0a175285f1585d0003968e96", "size_in_bytes": 306}, {"_path": "Lib/site-packages/setuptools/tests/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/tests/compat/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "8d6385e0d99b4f9f538c711e753ca45852ed2fc42dde41b144bbd601455fe982", "sha256_in_prefix": "8d6385e0d99b4f9f538c711e753ca45852ed2fc42dde41b144bbd601455fe982", "size_in_bytes": 144}, {"_path": "Lib/site-packages/setuptools/tests/compat/__pycache__/py39.cpython-39.pyc", "path_type": "hardlink", "sha256": "dd876b8e56b1d0aed8c169aa0b2d1ffad4c0b27238ea9decc011ff2e8a1a7395", "sha256_in_prefix": "dd876b8e56b1d0aed8c169aa0b2d1ffad4c0b27238ea9decc011ff2e8a1a7395", "size_in_bytes": 269}, {"_path": "Lib/site-packages/setuptools/tests/compat/py39.py", "path_type": "hardlink", "sha256": "794cbbfc5fba2914ce20a97ebdeb2152ee88d0475349d059321d04574959d7e8", "sha256_in_prefix": "794cbbfc5fba2914ce20a97ebdeb2152ee88d0475349d059321d04574959d7e8", "size_in_bytes": 135}, {"_path": "Lib/site-packages/setuptools/tests/config/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/tests/config/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "5e913a2b8c4fcd0fa648635cf1573c2e3afe103321ac8f34df6e856e50641a89", "sha256_in_prefix": "5e913a2b8c4fcd0fa648635cf1573c2e3afe103321ac8f34df6e856e50641a89", "size_in_bytes": 144}, {"_path": "Lib/site-packages/setuptools/tests/config/__pycache__/test_apply_pyprojecttoml.cpython-39.pyc", "path_type": "hardlink", "sha256": "47b5c97420749352e9d6ecb44b7f4b8eb7851afca6a9287ddbee6f19758892b3", "sha256_in_prefix": "47b5c97420749352e9d6ecb44b7f4b8eb7851afca6a9287ddbee6f19758892b3", "size_in_bytes": 26544}, {"_path": "Lib/site-packages/setuptools/tests/config/__pycache__/test_expand.cpython-39.pyc", "path_type": "hardlink", "sha256": "0ab57707cbb6729cc1dd05864a64582154ef5447606475cd867a6f890d4dd25e", "sha256_in_prefix": "0ab57707cbb6729cc1dd05864a64582154ef5447606475cd867a6f890d4dd25e", "size_in_bytes": 7780}, {"_path": "Lib/site-packages/setuptools/tests/config/__pycache__/test_pyprojecttoml.cpython-39.pyc", "path_type": "hardlink", "sha256": "2c147b14672d7a508841783c33f28efa2dcce380a48ee109feb6ddfd012a53bd", "sha256_in_prefix": "2c147b14672d7a508841783c33f28efa2dcce380a48ee109feb6ddfd012a53bd", "size_in_bytes": 11036}, {"_path": "Lib/site-packages/setuptools/tests/config/__pycache__/test_pyprojecttoml_dynamic_deps.cpython-39.pyc", "path_type": "hardlink", "sha256": "f26ad410649546cd18850fe2f7c89a80e77b80930bca8e0c1439b77e72b59435", "sha256_in_prefix": "f26ad410649546cd18850fe2f7c89a80e77b80930bca8e0c1439b77e72b59435", "size_in_bytes": 3350}, {"_path": "Lib/site-packages/setuptools/tests/config/__pycache__/test_setupcfg.cpython-39.pyc", "path_type": "hardlink", "sha256": "3db3d10710e31a968665d80e9b68116ed9943fb6b10942682048af38a431ea45", "sha256_in_prefix": "3db3d10710e31a968665d80e9b68116ed9943fb6b10942682048af38a431ea45", "size_in_bytes": 28270}, {"_path": "Lib/site-packages/setuptools/tests/config/downloads/__init__.py", "path_type": "hardlink", "sha256": "f62c670c47722ff6ab29b5337ee8897ed023f5b1b12b3f0cf5a94e159323c7d6", "sha256_in_prefix": "f62c670c47722ff6ab29b5337ee8897ed023f5b1b12b3f0cf5a94e159323c7d6", "size_in_bytes": 1827}, {"_path": "Lib/site-packages/setuptools/tests/config/downloads/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "fcb88c522195bba1021c328ec4e9fb109f8d1df68d2eb8095c7aad1d72bdf80f", "sha256_in_prefix": "fcb88c522195bba1021c328ec4e9fb109f8d1df68d2eb8095c7aad1d72bdf80f", "size_in_bytes": 2065}, {"_path": "Lib/site-packages/setuptools/tests/config/downloads/__pycache__/preload.cpython-39.pyc", "path_type": "hardlink", "sha256": "6a87ce94c89db7ec6dd3fd9ba38f7d46b75abbdc9f45f18c74fecb349363000d", "sha256_in_prefix": "6a87ce94c89db7ec6dd3fd9ba38f7d46b75abbdc9f45f18c74fecb349363000d", "size_in_bytes": 631}, {"_path": "Lib/site-packages/setuptools/tests/config/downloads/preload.py", "path_type": "hardlink", "sha256": "b081866696377263293308896186181c6da27d9264bc9804a2d445b62ba55752", "sha256_in_prefix": "b081866696377263293308896186181c6da27d9264bc9804a2d445b62ba55752", "size_in_bytes": 450}, {"_path": "Lib/site-packages/setuptools/tests/config/setupcfg_examples.txt", "path_type": "hardlink", "sha256": "7006d5bc26e4159b9350beb1451cd182ac81d2b2ef2537efc370f7d20968d8e1", "sha256_in_prefix": "7006d5bc26e4159b9350beb1451cd182ac81d2b2ef2537efc370f7d20968d8e1", "size_in_bytes": 1912}, {"_path": "Lib/site-packages/setuptools/tests/config/test_apply_pyprojecttoml.py", "path_type": "hardlink", "sha256": "97a9c4e1df162d4fde49646273b552a2a78abfd062ec26461dc12e0767a1936c", "sha256_in_prefix": "97a9c4e1df162d4fde49646273b552a2a78abfd062ec26461dc12e0767a1936c", "size_in_bytes": 28807}, {"_path": "Lib/site-packages/setuptools/tests/config/test_expand.py", "path_type": "hardlink", "sha256": "4b4a13e89be003fa2e8d1e184b8454b9fe6098eb75093415eba4500f357cc5de", "sha256_in_prefix": "4b4a13e89be003fa2e8d1e184b8454b9fe6098eb75093415eba4500f357cc5de", "size_in_bytes": 8933}, {"_path": "Lib/site-packages/setuptools/tests/config/test_pyprojecttoml.py", "path_type": "hardlink", "sha256": "d0b79f4a58d4840e8caad279015ccb8689aa65c62214a76eff57240de313d4b6", "sha256_in_prefix": "d0b79f4a58d4840e8caad279015ccb8689aa65c62214a76eff57240de313d4b6", "size_in_bytes": 12406}, {"_path": "Lib/site-packages/setuptools/tests/config/test_pyprojecttoml_dynamic_deps.py", "path_type": "hardlink", "sha256": "f56ef7fb22e16499af0a23b8ad3890a01a594f9c0d03dd176dde67d870ac85de", "sha256_in_prefix": "f56ef7fb22e16499af0a23b8ad3890a01a594f9c0d03dd176dde67d870ac85de", "size_in_bytes": 3271}, {"_path": "Lib/site-packages/setuptools/tests/config/test_setupcfg.py", "path_type": "hardlink", "sha256": "66f37e3bed838289f569da7aa0cea297c2567604fdcb5f7a7d1bea11253910b2", "sha256_in_prefix": "66f37e3bed838289f569da7aa0cea297c2567604fdcb5f7a7d1bea11253910b2", "size_in_bytes": 33427}, {"_path": "Lib/site-packages/setuptools/tests/contexts.py", "path_type": "hardlink", "sha256": "4c07592b19a6a1dc75131315a34d68e10a518e9229a385f72162aafc19e3c695", "sha256_in_prefix": "4c07592b19a6a1dc75131315a34d68e10a518e9229a385f72162aafc19e3c695", "size_in_bytes": 3480}, {"_path": "Lib/site-packages/setuptools/tests/environment.py", "path_type": "hardlink", "sha256": "f79fd4b536918aebf0602f5e5ca1076e7d36903b59cacbd9ab75192663d48f52", "sha256_in_prefix": "f79fd4b536918aebf0602f5e5ca1076e7d36903b59cacbd9ab75192663d48f52", "size_in_bytes": 3102}, {"_path": "Lib/site-packages/setuptools/tests/fixtures.py", "path_type": "hardlink", "sha256": "f95ee20fa05e136134470e9d56f4ce0a6dfa246f194d39eb5e13741884a582b8", "sha256_in_prefix": "f95ee20fa05e136134470e9d56f4ce0a6dfa246f194d39eb5e13741884a582b8", "size_in_bytes": 5197}, {"_path": "Lib/site-packages/setuptools/tests/indexes/test_links_priority/external.html", "path_type": "hardlink", "sha256": "78bf5eb8eb84f7724a65daa55f104e9476cac08b8db8876aec6051a6c68f31c5", "sha256_in_prefix": "78bf5eb8eb84f7724a65daa55f104e9476cac08b8db8876aec6051a6c68f31c5", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/tests/indexes/test_links_priority/simple/foobar/index.html", "path_type": "hardlink", "sha256": "0c3f932abed4538cc08c71f3e157b1603352033476ee57af4a1d5cfa4dd974b1", "sha256_in_prefix": "0c3f932abed4538cc08c71f3e157b1603352033476ee57af4a1d5cfa4dd974b1", "size_in_bytes": 174}, {"_path": "Lib/site-packages/setuptools/tests/integration/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/tests/integration/__pycache__/__init__.cpython-39.pyc", "path_type": "hardlink", "sha256": "b2ede06bbea2116bf89987b219903508a37a20c1226728be260f5d6ccdc76063", "sha256_in_prefix": "b2ede06bbea2116bf89987b219903508a37a20c1226728be260f5d6ccdc76063", "size_in_bytes": 149}, {"_path": "Lib/site-packages/setuptools/tests/integration/__pycache__/helpers.cpython-39.pyc", "path_type": "hardlink", "sha256": "4da9a8e594d0eaccab12b9e6b9e251731b8186fb3b8f923669e3824aded1903c", "sha256_in_prefix": "4da9a8e594d0eaccab12b9e6b9e251731b8186fb3b8f923669e3824aded1903c", "size_in_bytes": 3155}, {"_path": "Lib/site-packages/setuptools/tests/integration/__pycache__/test_pip_install_sdist.cpython-39.pyc", "path_type": "hardlink", "sha256": "f26869b958fa78238abcd73784687ba57186fbe4701730905b060ae775eeca62", "sha256_in_prefix": "f26869b958fa78238abcd73784687ba57186fbe4701730905b060ae775eeca62", "size_in_bytes": 6031}, {"_path": "Lib/site-packages/setuptools/tests/integration/helpers.py", "path_type": "hardlink", "sha256": "dcf1dc4bd48203e7f05499943f669de4d40eb6d240113239367a1cff1ae83b99", "sha256_in_prefix": "dcf1dc4bd48203e7f05499943f669de4d40eb6d240113239367a1cff1ae83b99", "size_in_bytes": 2522}, {"_path": "Lib/site-packages/setuptools/tests/integration/test_pip_install_sdist.py", "path_type": "hardlink", "sha256": "4856efb9817f843cede8eb6c4391a314d9f19a827f78495fbe962c8b2c8627e8", "sha256_in_prefix": "4856efb9817f843cede8eb6c4391a314d9f19a827f78495fbe962c8b2c8627e8", "size_in_bytes": 8256}, {"_path": "Lib/site-packages/setuptools/tests/mod_with_constant.py", "path_type": "hardlink", "sha256": "5ff2a3f34339e70d6d990e1feee658f7565300ba3884a553e841f1818a1c50c4", "sha256_in_prefix": "5ff2a3f34339e70d6d990e1feee658f7565300ba3884a553e841f1818a1c50c4", "size_in_bytes": 22}, {"_path": "Lib/site-packages/setuptools/tests/namespaces.py", "path_type": "hardlink", "sha256": "1cf708de74793021565e96800c82757f02b1ca671080192ec3cec87393d44804", "sha256_in_prefix": "1cf708de74793021565e96800c82757f02b1ca671080192ec3cec87393d44804", "size_in_bytes": 2774}, {"_path": "Lib/site-packages/setuptools/tests/script-with-bom.py", "path_type": "hardlink", "sha256": "851460222cc450b1a21bf653368318e3a1e12a1c6959fcb9146703e906e1e5f7", "sha256_in_prefix": "851460222cc450b1a21bf653368318e3a1e12a1c6959fcb9146703e906e1e5f7", "size_in_bytes": 18}, {"_path": "Lib/site-packages/setuptools/tests/server.py", "path_type": "hardlink", "sha256": "d050d97f471222708fe67d6168aec0c47a378c3dbad512bb0f7f918cff85e779", "sha256_in_prefix": "d050d97f471222708fe67d6168aec0c47a378c3dbad512bb0f7f918cff85e779", "size_in_bytes": 2397}, {"_path": "Lib/site-packages/setuptools/tests/test_archive_util.py", "path_type": "hardlink", "sha256": "6eeb8a758f17916dba3dedc8280a014461c6d0c0ee9a7b8da0f8365ac010cc88", "sha256_in_prefix": "6eeb8a758f17916dba3dedc8280a014461c6d0c0ee9a7b8da0f8365ac010cc88", "size_in_bytes": 845}, {"_path": "Lib/site-packages/setuptools/tests/test_bdist_deprecations.py", "path_type": "hardlink", "sha256": "ef95eade0627efd2c8232bac125b5b1da9f46c4800b767bf09a2fb28b4bcf8a4", "sha256_in_prefix": "ef95eade0627efd2c8232bac125b5b1da9f46c4800b767bf09a2fb28b4bcf8a4", "size_in_bytes": 775}, {"_path": "Lib/site-packages/setuptools/tests/test_bdist_egg.py", "path_type": "hardlink", "sha256": "e8f6983751772436c8875b8ad2eaefef2245731f7ccf9767f52389f0cbfdd65f", "sha256_in_prefix": "e8f6983751772436c8875b8ad2eaefef2245731f7ccf9767f52389f0cbfdd65f", "size_in_bytes": 1957}, {"_path": "Lib/site-packages/setuptools/tests/test_bdist_wheel.py", "path_type": "hardlink", "sha256": "759f5aece4ff53246f2e7a028b62861086edce11108ccdd8bad60c03a6427b3b", "sha256_in_prefix": "759f5aece4ff53246f2e7a028b62861086edce11108ccdd8bad60c03a6427b3b", "size_in_bytes": 23083}, {"_path": "Lib/site-packages/setuptools/tests/test_build.py", "path_type": "hardlink", "sha256": "c0980ccf68701c00dc2c583e9d7af045586eb3b8639807841a0ae9210c021a70", "sha256_in_prefix": "c0980ccf68701c00dc2c583e9d7af045586eb3b8639807841a0ae9210c021a70", "size_in_bytes": 798}, {"_path": "Lib/site-packages/setuptools/tests/test_build_clib.py", "path_type": "hardlink", "sha256": "6d7e755d101fe2e3bb22e1c5a6378f9e82bc984ef837682ca1e12a17ea1f830b", "sha256_in_prefix": "6d7e755d101fe2e3bb22e1c5a6378f9e82bc984ef837682ca1e12a17ea1f830b", "size_in_bytes": 3123}, {"_path": "Lib/site-packages/setuptools/tests/test_build_ext.py", "path_type": "hardlink", "sha256": "7b8652c6c60f079cead4a4aa184b804d9d2dd0f250ccc8638e4289fa12237207", "sha256_in_prefix": "7b8652c6c60f079cead4a4aa184b804d9d2dd0f250ccc8638e4289fa12237207", "size_in_bytes": 10099}, {"_path": "Lib/site-packages/setuptools/tests/test_build_meta.py", "path_type": "hardlink", "sha256": "21a929a7d32272f8718bdfc5d913f2636367081d46f746b7f2ce0ee40dc2ba21", "sha256_in_prefix": "21a929a7d32272f8718bdfc5d913f2636367081d46f746b7f2ce0ee40dc2ba21", "size_in_bytes": 34118}, {"_path": "Lib/site-packages/setuptools/tests/test_build_py.py", "path_type": "hardlink", "sha256": "8286cc13f0afcdfe94831abbd2259f5de91bff1cb24fad648708c5abcce4c1fc", "sha256_in_prefix": "8286cc13f0afcdfe94831abbd2259f5de91bff1cb24fad648708c5abcce4c1fc", "size_in_bytes": 14186}, {"_path": "Lib/site-packages/setuptools/tests/test_config_discovery.py", "path_type": "hardlink", "sha256": "16a57e94eb64a9a23e6b2cd4db3a1c49d0f94da4408026678b13438a5280e854", "sha256_in_prefix": "16a57e94eb64a9a23e6b2cd4db3a1c49d0f94da4408026678b13438a5280e854", "size_in_bytes": 22580}, {"_path": "Lib/site-packages/setuptools/tests/test_core_metadata.py", "path_type": "hardlink", "sha256": "bdb549e7f2ecc7f86c3bf19d07a9d01172518c0db2771ebfa926ebe4ba617800", "sha256_in_prefix": "bdb549e7f2ecc7f86c3bf19d07a9d01172518c0db2771ebfa926ebe4ba617800", "size_in_bytes": 20881}, {"_path": "Lib/site-packages/setuptools/tests/test_depends.py", "path_type": "hardlink", "sha256": "c90057a106cd425262b7a99b455a33e816f9e777f7b0daead369598a6373e576", "sha256_in_prefix": "c90057a106cd425262b7a99b455a33e816f9e777f7b0daead369598a6373e576", "size_in_bytes": 424}, {"_path": "Lib/site-packages/setuptools/tests/test_develop.py", "path_type": "hardlink", "sha256": "08bcd767cf9be7e5454ee6aee0fe325c474bc7551dc9315c39fad5d2ac9421d1", "sha256_in_prefix": "08bcd767cf9be7e5454ee6aee0fe325c474bc7551dc9315c39fad5d2ac9421d1", "size_in_bytes": 5142}, {"_path": "Lib/site-packages/setuptools/tests/test_dist.py", "path_type": "hardlink", "sha256": "1858f22f67ad031bd5337abb36114419c5d2e60c8a8fc5736ea71b2b3a6a6ce9", "sha256_in_prefix": "1858f22f67ad031bd5337abb36114419c5d2e60c8a8fc5736ea71b2b3a6a6ce9", "size_in_bytes": 8901}, {"_path": "Lib/site-packages/setuptools/tests/test_dist_info.py", "path_type": "hardlink", "sha256": "e640518fdb6e06c56b781b18db61f67de30efc9419b12a0e64c53f3097d47af6", "sha256_in_prefix": "e640518fdb6e06c56b781b18db61f67de30efc9419b12a0e64c53f3097d47af6", "size_in_bytes": 7077}, {"_path": "Lib/site-packages/setuptools/tests/test_distutils_adoption.py", "path_type": "hardlink", "sha256": "fdeca7ace7f212a5c51268d4261ce97bc1973f24d43ef35239bb38a80026072f", "sha256_in_prefix": "fdeca7ace7f212a5c51268d4261ce97bc1973f24d43ef35239bb38a80026072f", "size_in_bytes": 5987}, {"_path": "Lib/site-packages/setuptools/tests/test_easy_install.py", "path_type": "hardlink", "sha256": "8f1e25a45c9e7b41b8df671d9f0068c370242f889bc3ed1020bc25770bf94822", "sha256_in_prefix": "8f1e25a45c9e7b41b8df671d9f0068c370242f889bc3ed1020bc25770bf94822", "size_in_bytes": 53534}, {"_path": "Lib/site-packages/setuptools/tests/test_editable_install.py", "path_type": "hardlink", "sha256": "ede4c4b694f493b41e572660eb87a1de4667f928dc92e07d2dca243ae577ec32", "sha256_in_prefix": "ede4c4b694f493b41e572660eb87a1de4667f928dc92e07d2dca243ae577ec32", "size_in_bytes": 43383}, {"_path": "Lib/site-packages/setuptools/tests/test_egg_info.py", "path_type": "hardlink", "sha256": "402ce850e905a1c99b9304ba5d4ec5f16373284f02184311c5806a28b81f52b7", "sha256_in_prefix": "402ce850e905a1c99b9304ba5d4ec5f16373284f02184311c5806a28b81f52b7", "size_in_bytes": 44866}, {"_path": "Lib/site-packages/setuptools/tests/test_extern.py", "path_type": "hardlink", "sha256": "ae9294ea809c92cba62f07f94de2a50e5b854344d47db3f04cb41ba71705ac25", "sha256_in_prefix": "ae9294ea809c92cba62f07f94de2a50e5b854344d47db3f04cb41ba71705ac25", "size_in_bytes": 296}, {"_path": "Lib/site-packages/setuptools/tests/test_find_packages.py", "path_type": "hardlink", "sha256": "0932c0713cd619604b09c776680b14564bcede26eb96a7b114174328e58fa2af", "sha256_in_prefix": "0932c0713cd619604b09c776680b14564bcede26eb96a7b114174328e58fa2af", "size_in_bytes": 7819}, {"_path": "Lib/site-packages/setuptools/tests/test_find_py_modules.py", "path_type": "hardlink", "sha256": "cd08ee8481b94d03764893e2c7d011a380cbff0f382e7f10b070d48e36ebb404", "sha256_in_prefix": "cd08ee8481b94d03764893e2c7d011a380cbff0f382e7f10b070d48e36ebb404", "size_in_bytes": 2404}, {"_path": "Lib/site-packages/setuptools/tests/test_glob.py", "path_type": "hardlink", "sha256": "3f726fa47fa45d0e01677cef445fb32b13a0c325b3c08690233d161ddc52d249", "sha256_in_prefix": "3f726fa47fa45d0e01677cef445fb32b13a0c325b3c08690233d161ddc52d249", "size_in_bytes": 887}, {"_path": "Lib/site-packages/setuptools/tests/test_install_scripts.py", "path_type": "hardlink", "sha256": "b1c22b27a6bfb2c2aa838bc804d6948e600a1c460b51467d58a9cf78a9c4ea07", "sha256_in_prefix": "b1c22b27a6bfb2c2aa838bc804d6948e600a1c460b51467d58a9cf78a9c4ea07", "size_in_bytes": 3433}, {"_path": "Lib/site-packages/setuptools/tests/test_logging.py", "path_type": "hardlink", "sha256": "ce51390e595dba40bb25ce7814dbc357feeec7712b024adfacde424ac9cd3944", "sha256_in_prefix": "ce51390e595dba40bb25ce7814dbc357feeec7712b024adfacde424ac9cd3944", "size_in_bytes": 2099}, {"_path": "Lib/site-packages/setuptools/tests/test_manifest.py", "path_type": "hardlink", "sha256": "78c83ae69200e760e2cc1ea6a64b5253e6fc0a3c1a3424b931280bfd5d4bac52", "sha256_in_prefix": "78c83ae69200e760e2cc1ea6a64b5253e6fc0a3c1a3424b931280bfd5d4bac52", "size_in_bytes": 18562}, {"_path": "Lib/site-packages/setuptools/tests/test_namespaces.py", "path_type": "hardlink", "sha256": "63abada1ee4f1c7a8bfc39606b0a81f45f17a6c5033efbf0d6c40c7a72b4e1ed", "sha256_in_prefix": "63abada1ee4f1c7a8bfc39606b0a81f45f17a6c5033efbf0d6c40c7a72b4e1ed", "size_in_bytes": 4515}, {"_path": "Lib/site-packages/setuptools/tests/test_packageindex.py", "path_type": "hardlink", "sha256": "a848cb1e94aeda00247a0c04b2dcc7413f8e9b5b902188c0f3378dcc45fbf6ea", "sha256_in_prefix": "a848cb1e94aeda00247a0c04b2dcc7413f8e9b5b902188c0f3378dcc45fbf6ea", "size_in_bytes": 8775}, {"_path": "Lib/site-packages/setuptools/tests/test_sandbox.py", "path_type": "hardlink", "sha256": "b2151613b7cb4d67bb27375f8ba36178159ab86de852e91b515e3a700ac3d2ed", "sha256_in_prefix": "b2151613b7cb4d67bb27375f8ba36178159ab86de852e91b515e3a700ac3d2ed", "size_in_bytes": 4330}, {"_path": "Lib/site-packages/setuptools/tests/test_sdist.py", "path_type": "hardlink", "sha256": "4582ef3dafe77f20b5666a229f3a8ccc9ca74c31b846d3d80b5f7fd0b53aa6fb", "sha256_in_prefix": "4582ef3dafe77f20b5666a229f3a8ccc9ca74c31b846d3d80b5f7fd0b53aa6fb", "size_in_bytes": 32872}, {"_path": "Lib/site-packages/setuptools/tests/test_setopt.py", "path_type": "hardlink", "sha256": "dd5c713380137cff8fe001a70e3a160a71ebe7e8bd0921104c5614d7e1539ef2", "sha256_in_prefix": "dd5c713380137cff8fe001a70e3a160a71ebe7e8bd0921104c5614d7e1539ef2", "size_in_bytes": 1365}, {"_path": "Lib/site-packages/setuptools/tests/test_setuptools.py", "path_type": "hardlink", "sha256": "fde221a8a7f8e7e3ad1eac517f6d0a9dd39926525d4b43ee14b5c13b733e2cdf", "sha256_in_prefix": "fde221a8a7f8e7e3ad1eac517f6d0a9dd39926525d4b43ee14b5c13b733e2cdf", "size_in_bytes": 9008}, {"_path": "Lib/site-packages/setuptools/tests/test_shutil_wrapper.py", "path_type": "hardlink", "sha256": "835e44d753ed6711be227076056345c87facbce6d7c765dc32180c2c93ee1677", "sha256_in_prefix": "835e44d753ed6711be227076056345c87facbce6d7c765dc32180c2c93ee1677", "size_in_bytes": 641}, {"_path": "Lib/site-packages/setuptools/tests/test_unicode_utils.py", "path_type": "hardlink", "sha256": "c567c4125f239100adf68b615135c97c599dc804c0160809b36b53c636ee99bc", "sha256_in_prefix": "c567c4125f239100adf68b615135c97c599dc804c0160809b36b53c636ee99bc", "size_in_bytes": 316}, {"_path": "Lib/site-packages/setuptools/tests/test_virtualenv.py", "path_type": "hardlink", "sha256": "83e9e30bff494c0b35615c7fd5d189fd0e919489cee2a295bbdf9702035be936", "sha256_in_prefix": "83e9e30bff494c0b35615c7fd5d189fd0e919489cee2a295bbdf9702035be936", "size_in_bytes": 3730}, {"_path": "Lib/site-packages/setuptools/tests/test_warnings.py", "path_type": "hardlink", "sha256": "cf0476cdc9c2782783a882d994938f01cbb23c7a03bc6bb53ad3956222cc93be", "sha256_in_prefix": "cf0476cdc9c2782783a882d994938f01cbb23c7a03bc6bb53ad3956222cc93be", "size_in_bytes": 3347}, {"_path": "Lib/site-packages/setuptools/tests/test_wheel.py", "path_type": "hardlink", "sha256": "27ef375b529d5d38008c5644dc7fb2b68861bc31358aa75b139605e632d09464", "sha256_in_prefix": "27ef375b529d5d38008c5644dc7fb2b68861bc31358aa75b139605e632d09464", "size_in_bytes": 19370}, {"_path": "Lib/site-packages/setuptools/tests/test_windows_wrappers.py", "path_type": "hardlink", "sha256": "685e944e8c0ddf2cc281d061f670d056f6087d262882b4caefbe931325c406a8", "sha256_in_prefix": "685e944e8c0ddf2cc281d061f670d056f6087d262882b4caefbe931325c406a8", "size_in_bytes": 7881}, {"_path": "Lib/site-packages/setuptools/tests/text.py", "path_type": "hardlink", "sha256": "6b5db5f7ba4c553bc1e85016434ba34fc7c84222c8589945025d5409a0d40df8", "sha256_in_prefix": "6b5db5f7ba4c553bc1e85016434ba34fc7c84222c8589945025d5409a0d40df8", "size_in_bytes": 123}, {"_path": "Lib/site-packages/setuptools/tests/textwrap.py", "path_type": "hardlink", "sha256": "14d34dabf322684271f3c3e7b1b250211c668f5aa681c00e0975d1b0e0cf24de", "sha256_in_prefix": "14d34dabf322684271f3c3e7b1b250211c668f5aa681c00e0975d1b0e0cf24de", "size_in_bytes": 98}, {"_path": "Lib/site-packages/setuptools/unicode_utils.py", "path_type": "hardlink", "sha256": "ba430687ca44030e85fc4cdbf8ae43ddcfb4efc46003f19c174a16ea5838952b", "sha256_in_prefix": "ba430687ca44030e85fc4cdbf8ae43ddcfb4efc46003f19c174a16ea5838952b", "size_in_bytes": 3189}, {"_path": "Lib/site-packages/setuptools/version.py", "path_type": "hardlink", "sha256": "58909e52ecaaef80289364de2bdf8e7b164ebbc5eb950cbbfb2d0112e58da2f4", "sha256_in_prefix": "58909e52ecaaef80289364de2bdf8e7b164ebbc5eb950cbbfb2d0112e58da2f4", "size_in_bytes": 161}, {"_path": "Lib/site-packages/setuptools/warnings.py", "path_type": "hardlink", "sha256": "a18d127b978eaa37bf144ca34e0a2751cd171b082cac8e5c826d64930ba5cffc", "sha256_in_prefix": "a18d127b978eaa37bf144ca34e0a2751cd171b082cac8e5c826d64930ba5cffc", "size_in_bytes": 3796}, {"_path": "Lib/site-packages/setuptools/wheel.py", "path_type": "hardlink", "sha256": "c6402dbe09bbb8f4f2615db3a95990d3003c90bc0ec914f625eb35cc0cb4ecab", "sha256_in_prefix": "c6402dbe09bbb8f4f2615db3a95990d3003c90bc0ec914f625eb35cc0cb4ecab", "size_in_bytes": 8624}, {"_path": "Lib/site-packages/setuptools/windows_support.py", "path_type": "hardlink", "sha256": "c16e0860b33506fed9d4c69ab8fdb198f8f2cbec249909d7772bd7b1c01ff5fc", "sha256_in_prefix": "c16e0860b33506fed9d4c69ab8fdb198f8f2cbec249909d7772bd7b1c01ff5fc", "size_in_bytes": 726}], "paths_version": 1}, "requested_spec": "None", "sha256": "7a9e1dcfba5d7d5f3165145883aa5d46e67d336d40c8c4c9115093e65821eda3", "size": 1756477, "subdir": "win-64", "timestamp": 1746025283000, "url": "https://repo.anaconda.com/pkgs/main/win-64/setuptools-78.1.1-py39haa95532_0.conda", "version": "78.1.1"}