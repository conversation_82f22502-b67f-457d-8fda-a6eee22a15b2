{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": ["vs2017_win-64", "cuda-nvcc_win-64 12.8.93.*"], "extracted_package_dir": "", "files": [], "fn": "cuda-nvcc-12.8.93-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "", "type": 1}, "md5": "8814a6171021be05f578fea91f0c7664", "name": "cuda-nvcc", "package_tarball_full_path": "", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "7e4013900cd6f56049a473173396f6345664c0e1c7f3ef2d3373e7356fa51300", "size": 16914, "subdir": "win-64", "timestamp": 1740205557000, "url": "https://conda.anaconda.org/nvidia/win-64/cuda-nvcc-12.8.93-0.conda", "version": "12.8.93"}