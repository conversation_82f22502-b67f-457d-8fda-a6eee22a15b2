{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.8,<12.9.0a0", "libcusolver 11.7.3.90 0"], "extracted_package_dir": "", "files": ["Library/include/cusolverDn.h", "Library/include/cusolverMg.h", "Library/include/cusolverRf.h", "Library/include/cusolverSp.h", "Library/include/cusolverSp_LOWLEVEL_PREVIEW.h", "Library/include/cusolver_common.h", "Library/lib/cusolver.lib", "Library/lib/cusolverMg.lib"], "fn": "libcusolver-dev-11.7.3.90-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "", "type": 1}, "md5": "f5b832596acaf26d8eac7605f704c7c0", "name": "libcusolver-dev", "package_tarball_full_path": "", "paths_data": {"paths": [{"_path": "Library/include/cusolverDn.h", "path_type": "hardlink", "sha256": "20b911a09c718b88e33188d51e2e0e8d88a7ec47b3ec6c9c197048a09e2ae2e3", "sha256_in_prefix": "20b911a09c718b88e33188d51e2e0e8d88a7ec47b3ec6c9c197048a09e2ae2e3", "size_in_bytes": 157038}, {"_path": "Library/include/cusolverMg.h", "path_type": "hardlink", "sha256": "900833e0f067ac4fdc8934f6f8333ebe3e0169c3fa6c61faa286330bef01f536", "sha256_in_prefix": "900833e0f067ac4fdc8934f6f8333ebe3e0169c3fa6c61faa286330bef01f536", "size_in_bytes": 11867}, {"_path": "Library/include/cusolverRf.h", "path_type": "hardlink", "sha256": "a12cbe5e75a5794b9185f1a911b9bb87c4ea0da5bb0f3f68e79afdc340305bb4", "sha256_in_prefix": "a12cbe5e75a5794b9185f1a911b9bb87c4ea0da5bb0f3f68e79afdc340305bb4", "size_in_bytes": 14874}, {"_path": "Library/include/cusolverSp.h", "path_type": "hardlink", "sha256": "584d8e81ecb48f3980aea9cbc5bd8d0e8dcc10ce2dd5d933070cd91e04f25dad", "sha256_in_prefix": "584d8e81ecb48f3980aea9cbc5bd8d0e8dcc10ce2dd5d933070cd91e04f25dad", "size_in_bytes": 33808}, {"_path": "Library/include/cusolverSp_LOWLEVEL_PREVIEW.h", "path_type": "hardlink", "sha256": "ba6c516a506531176130a1383de8656f032591d7c205924e4210c3c391172d19", "sha256_in_prefix": "ba6c516a506531176130a1383de8656f032591d7c205924e4210c3c391172d19", "size_in_bytes": 40276}, {"_path": "Library/include/cusolver_common.h", "path_type": "hardlink", "sha256": "c683f86634a68554b6891ed5026c51be98fd6e5422ff511d470f626edc846768", "sha256_in_prefix": "c683f86634a68554b6891ed5026c51be98fd6e5422ff511d470f626edc846768", "size_in_bytes": 8993}, {"_path": "Library/lib/cusolver.lib", "path_type": "hardlink", "sha256": "c365c770b36f321e80974935359fe7342061b943d9824b4b8ef418883dcd020a", "sha256_in_prefix": "c365c770b36f321e80974935359fe7342061b943d9824b4b8ef418883dcd020a", "size_in_bytes": 225608}, {"_path": "Library/lib/cusolverMg.lib", "path_type": "hardlink", "sha256": "6a3a19367b9ce7974959e28b0bbfbae1fe809fbe7f60c400dea9a57613a84cc6", "sha256_in_prefix": "6a3a19367b9ce7974959e28b0bbfbae1fe809fbe7f60c400dea9a57613a84cc6", "size_in_bytes": 57764}], "paths_version": 1}, "requested_spec": "None", "sha256": "b9db09451189ef73656d54c04a761d269af76530576a9a4a8cfee510bbdeeb9f", "size": 56910, "subdir": "win-64", "timestamp": 1739459068000, "url": "https://conda.anaconda.org/nvidia/win-64/libcusolver-dev-11.7.3.90-0.conda", "version": "11.7.3.90"}