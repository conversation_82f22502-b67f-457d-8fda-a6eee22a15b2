{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": ["vc >=14.2"], "depends": ["cuda-cudart-dev", "cuda-version >=12.8,<12.9.0a0", "cuda-cudart >=12.8.57,<13.0a0", "cuda-nvcc-dev_win-64 12.8.93 0", "cuda-nvcc-tools 12.8.93 0", "cuda-nvvm-impl 12.8.93 0"], "extracted_package_dir": "", "files": [], "fn": "cuda-nvcc-impl-12.8.93-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "", "type": 1}, "md5": "fe6beb405cb04e7b334b9be8c7c85f81", "name": "cuda-nvcc-impl", "package_tarball_full_path": "", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "e64db7c01ceb60c3e660cc7e17d5e7d4e77f21b10ab30f603860f19d5ef07c26", "size": 17817, "subdir": "win-64", "timestamp": 1740205541000, "url": "https://conda.anaconda.org/nvidia/win-64/cuda-nvcc-impl-12.8.93-0.conda", "version": "12.8.93"}