{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.8,<12.9.0a0"], "extracted_package_dir": "", "files": ["Library/bin/nvfatbin_120_0.dll"], "fn": "libnvfatbin-12.8.90-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "", "type": 1}, "md5": "030710cca38655980d6fab5e3f004411", "name": "libnvfatbin", "package_tarball_full_path": "", "paths_data": {"paths": [{"_path": "Library/bin/nvfatbin_120_0.dll", "path_type": "hardlink", "sha256": "664d30436b75299a3ec4c28a59bbfa7c813b6e08cfbb4bca990af527f24f60ef", "sha256_in_prefix": "664d30436b75299a3ec4c28a59bbfa7c813b6e08cfbb4bca990af527f24f60ef", "size_in_bytes": 849920}], "paths_version": 1}, "requested_spec": "None", "sha256": "cdb891c51eaac5f6118804cbf8dfc5c5bbb802b66848f786881e6c50d69038d9", "size": 337445, "subdir": "win-64", "timestamp": 1739448742000, "url": "https://conda.anaconda.org/nvidia/win-64/libnvfatbin-12.8.90-0.conda", "version": "12.8.90"}