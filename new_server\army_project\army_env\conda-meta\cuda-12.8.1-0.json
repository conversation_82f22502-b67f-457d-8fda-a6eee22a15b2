{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/noarch", "constrains": [], "depends": ["__win", "cuda-runtime 12.8.1.*", "cuda-toolkit 12.8.1.*"], "extracted_package_dir": "", "files": [], "fn": "cuda-12.8.1-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "", "type": 1}, "md5": "7659234d61047f0ed191b786887668af", "name": "cuda", "noarch": "generic", "package_tarball_full_path": "", "package_type": "noarch_generic", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "cuda=12.8", "sha256": "b2e630748fc6214c5b9a71dac49d7012d01878e5076ecfe5c73e3df833c1995b", "size": 17154, "subdir": "noarch", "timestamp": 1741063893000, "url": "https://conda.anaconda.org/nvidia/noarch/cuda-12.8.1-0.conda", "version": "12.8.1"}