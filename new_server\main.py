from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
import uvicorn
import asyncio
from contextlib import asynccontextmanager

# 导入路由模块
from army_project.detected import router as detected_router
from army_project.sample import router as sample_router
from utils.db import db_manager

# 定义应用生命周期事件处理器
@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时执行
    await db_manager.init_database()
    print("数据库初始化完成")
    yield
    # 关闭时执行（如果需要的话）
    print("应用关闭")

# 创建FastAPI应用实例
app = FastAPI(
    title="目标检测后端API",
    description="支持跨域通信的目标检测和样本管理API",
    version="1.0.0",
    lifespan=lifespan
)

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 开发环境允许所有来源，生产环境应限制具体域名
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有HTTP方法
    allow_headers=["*"],  # 允许所有请求头
)

# 注册路由
app.include_router(detected_router)
app.include_router(sample_router)

# 挂载静态文件目录
import os
if not os.path.exists("workspace"):
    os.makedirs("workspace")

# 创建支持CORS的静态文件应用
from fastapi.responses import FileResponse
from fastapi import Request

@app.get("/workspace/{file_path:path}")
async def serve_workspace_file(file_path: str, request: Request):
    """
    提供workspace目录下的静态文件，支持CORS
    """
    full_path = os.path.join("workspace", file_path)
    if os.path.exists(full_path) and os.path.isfile(full_path):
        response = FileResponse(full_path)
        # 添加CORS头
        response.headers["Access-Control-Allow-Origin"] = "*"
        response.headers["Access-Control-Allow-Methods"] = "GET, HEAD, OPTIONS"
        response.headers["Access-Control-Allow-Headers"] = "*"
        return response
    else:
        from fastapi import HTTPException
        raise HTTPException(status_code=404, detail="File not found")

# 备用静态文件挂载（如果上面的路由不工作）
app.mount("/workspace-static", StaticFiles(directory="workspace"), name="workspace-static")



# 应用启动配置
if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="127.0.0.1",
        port=8083,
        reload=True,  # 开发模式，代码变更自动重载
        log_level="info"
    )