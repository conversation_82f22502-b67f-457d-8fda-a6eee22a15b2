{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": ["cuda-cccl_win-64 12.8.90.*", "cuda-cudart-dev 12.8.90.*", "cuda-nvrtc-dev 12.8.93.*", "cuda-opencl-dev 12.8.90.*", "cuda-profiler-api 12.8.90.*", "libcublas-dev 12.8.4.1.*", "libcufft-dev 11.3.3.83.*", "libcurand-dev 10.3.9.90.*", "libcusolver-dev 11.7.3.90.*", "libcusparse-dev 12.5.8.93.*", "libnpp-dev 12.3.3.100.*", "libnvfatbin-dev 12.8.90.*", "libnvjitlink-dev 12.8.93.*", "libnvjpeg-dev 12.3.5.92.*"], "extracted_package_dir": "", "files": [], "fn": "cuda-libraries-dev-12.8.1-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "", "type": 1}, "md5": "9cf42239f86a7ac1914b0779828cfce9", "name": "cuda-libraries-dev", "package_tarball_full_path": "", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "30358ed3be46caedf759ea6566f9ef85c8d915d678ebe5c8767c9cb4ce00fe2c", "size": 17142, "subdir": "win-64", "timestamp": 1741063727000, "url": "https://conda.anaconda.org/nvidia/win-64/cuda-libraries-dev-12.8.1-0.conda", "version": "12.8.1"}