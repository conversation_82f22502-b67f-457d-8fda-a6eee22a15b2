import { get, post, upload } from './request';
import { serverAddress } from '@/api/config';
// 样本库接口

// 取得类别树，读取后台类别树json文件，并获取对应类别的样本数量
export const APIGetCategoryTree = () => get(serverAddress +'api/exp/getCategoryTree');

// 保存类别树，将确定的类别树传到后端保存到json文件中
export const APISaveCategoryTree = (params) => post(serverAddress +'api/exp/saveCategoryTree', params);

// 获取样本列表,传递层级目录，分页返回样本列表（文件名、路径、上传时间、id），创建不同类别对应的数据库
export const APIGetSamplesListByName = (params) => get(serverAddress +'api/exp/getArmExpsByName', params);

// 删除样本，传递id，在数据库获取路径，删除文件
export const APIDelSamples = (params) => post(serverAddress +'api/exp/delArmExpsByName', params);

// 批量删除，传递id数组，在数据库遍历查询，删除文件
export const APIDelSamplesBatch = (params) => post(serverAddress +'api/exp/delArmExpsBatch', params);

// 查看样本详情，传递id，在数据库获取路径，读取文件，调用labelimg
export const APIGetTXTContentByName = (param) => get(serverAddress +'api/exp/getArmTXTByName', param);

// 添加样本，传递层级目录，传递文件数据，保存到对应目录，更新数据库
export const APIAddSamples = (formData) => upload(serverAddress +'api/exp/addArmExps', formData);

// 重命名样本，传递id和新名称，更新数据库
export const APIRenameSample = (params) => post(serverAddress +'api/exp/renameArmExp', params);

// 查看样本，传递id，在数据库获取路径，读取标注文件，并返回前端
export const APIViewSample = (param) => get(serverAddress +'api/exp/viewArmExp', param);

// 搜索样本，传递搜索词，当前分页数，在数据库中模糊查询，返回样本列表，
export const APISearchSamples = (params) => get(serverAddress +'api/exp/searchArmExps', params);

// 检测识别接口
// 统一的上传接口,传入的参数有：文件类型，文件数据，选择完对应的文件后，将文件上传到后端，后端将文件保存到对应的目录下
export const APIUploadData = (formData) => upload(serverAddress +'api/detected/upload', formData);

// 预处理，将数据输入框的文件相对url传到后端，后端根据url查找指定文件，调用预处理库，返回处理后的文件url
export const APIPreprocessData = (params) => post(serverAddress +'api/detected/preprocess', params);

// 识别，将当前要检测的数据类别发送到后端（image, video, folder）查看我的workspace中的process目录下的文件如果是image就将image灰度化，然后将路径返回前端，并且需要将结果在前端的检测结果中展示（可以模拟数据），返回识别结果，以及对应的标框信息，让我能够在前端显示
export const APIDetectData = (params) => post(serverAddress +'api/detected/detect', params);

// 修正，
export const APICorrectData = (params) => post(serverAddress +'api/detected/correct', params);

// RTSP流创建接口
export const APICreateRTSPStream = (params) => post(serverAddress + 'api/detected/rtsp/stream', params);

// 帧处理接口
export const APIProcessFrame = (formData) => upload(serverAddress + 'api/detected/process/frame', formData);

// 停止RTSP流接口
export const APIStopRTSPStream = (params) => post(serverAddress + 'api/detected/rtsp/stop', params);