{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": ["cuda-nvdisasm", "vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.8,<12.9.0a0"], "extracted_package_dir": "", "files": ["Library/bin/cuobjdump.exe"], "fn": "cuda-cuobjdump-12.8.90-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "", "type": 1}, "md5": "eac941d6080054935e21cf5a26f228be", "name": "cuda-cuob<PERSON><PERSON><PERSON>", "package_tarball_full_path": "", "paths_data": {"paths": [{"_path": "Library/bin/cuobjdump.exe", "path_type": "hardlink", "sha256": "7a1e6144ad3e9445f54fd8772272a7837a455f7fa7a3df39aad0169084a7583d", "sha256_in_prefix": "7a1e6144ad3e9445f54fd8772272a7837a455f7fa7a3df39aad0169084a7583d", "size_in_bytes": 16404480}], "paths_version": 1}, "requested_spec": "None", "sha256": "2f54f35c3a6bd30516fd4dbdce600f529533ac34eb8fd23780f287abf428f958", "size": 4635253, "subdir": "win-64", "timestamp": 1739448576000, "url": "https://conda.anaconda.org/nvidia/win-64/cuda-cuobjdump-12.8.90-0.conda", "version": "12.8.90"}