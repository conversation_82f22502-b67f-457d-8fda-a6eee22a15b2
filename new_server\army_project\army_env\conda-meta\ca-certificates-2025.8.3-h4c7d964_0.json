{"build": "h4c7d964_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["__win"], "extracted_package_dir": "", "files": ["Library/ssl/cacert.pem", "Library/ssl/cert.pem"], "fn": "ca-certificates-2025.8.3-h4c7d964_0.conda", "license": "ISC", "link": {"source": "", "type": 1}, "md5": "c9e0c0f82f6e63323827db462b40ede8", "name": "ca-certificates", "noarch": "generic", "package_tarball_full_path": "", "package_type": "noarch_generic", "paths_data": {"paths": [{"_path": "Library/ssl/cacert.pem", "path_type": "hardlink", "sha256": "9102e6a3644a071ba6cdbd4a53698f291c4a64b18450a08bc046548b6db5cc8b", "sha256_in_prefix": "9102e6a3644a071ba6cdbd4a53698f291c4a64b18450a08bc046548b6db5cc8b", "size_in_bytes": 287634}, {"_path": "Library/ssl/cert.pem", "path_type": "hardlink", "sha256": "9102e6a3644a071ba6cdbd4a53698f291c4a64b18450a08bc046548b6db5cc8b", "sha256_in_prefix": "9102e6a3644a071ba6cdbd4a53698f291c4a64b18450a08bc046548b6db5cc8b", "size_in_bytes": 287634}], "paths_version": 1}, "requested_spec": "None", "sha256": "3b82f62baad3fd33827b01b0426e8203a2786c8f452f633740868296bcbe8485", "size": 154489, "subdir": "noarch", "timestamp": 1754210967000, "url": "https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.8.3-h4c7d964_0.conda", "version": "2025.8.3"}