const { defineConfig } = require('@vue/cli-service')
module.exports = defineConfig({
  transpileDependencies: true,
  chainWebpack: (config) => {
    config.plugin('define').tap((definitions) => {
      Object.assign(definitions[0], {
        __VUE_OPTIONS_API__: 'true',
        __VUE_PROD_DEVTOOLS__: 'false',
        __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: 'false'
      })
      return definitions
    })
  },

devServer: {
    proxy: {
      '/api': {  // 匹配所有以 /api 开头的请求
        target: 'http://localhost:8083',  // 后端实际地址
        changeOrigin: true,  // 允许跨域
        pathRewrite: {
          '^/api': '/workspace'  // 重写路径：将 /api 替换为 /workspace
        }
      }
    }
  }
})

