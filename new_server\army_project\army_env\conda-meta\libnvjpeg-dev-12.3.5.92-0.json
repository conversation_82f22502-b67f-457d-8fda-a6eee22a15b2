{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": ["cuda-cudart-dev", "cuda-version >=12.8,<12.9.0a0", "libnvjpeg 12.3.5.92 0"], "extracted_package_dir": "", "files": ["Library/include/nvjpeg.h", "Library/lib/nvjpeg.lib"], "fn": "libnvjpeg-dev-12.3.5.92-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "", "type": 1}, "md5": "be44987bb52794784c52c806a636e6ef", "name": "libnvjpeg-dev", "package_tarball_full_path": "", "paths_data": {"paths": [{"_path": "Library/include/nvjpeg.h", "path_type": "hardlink", "sha256": "e60c17275967abb0fc2db0ffe85cca574aad385b5c023bacd1d82f7822619a5b", "sha256_in_prefix": "e60c17275967abb0fc2db0ffe85cca574aad385b5c023bacd1d82f7822619a5b", "size_in_bytes": 34986}, {"_path": "Library/lib/nvjpeg.lib", "path_type": "hardlink", "sha256": "f1ffdc798abb5e26dd9ecc469afcff9fe1fb2eecf22a11ec1ab6ad850edbac2c", "sha256_in_prefix": "f1ffdc798abb5e26dd9ecc469afcff9fe1fb2eecf22a11ec1ab6ad850edbac2c", "size_in_bytes": 22030}], "paths_version": 1}, "requested_spec": "None", "sha256": "a08da44b496ab0d25c42fa45e2755b22e09701831781297a513d140a0e3dfa11", "size": 26956, "subdir": "win-64", "timestamp": 1739448913000, "url": "https://conda.anaconda.org/nvidia/win-64/libnvjpeg-dev-12.3.5.92-0.conda", "version": "12.3.5.92"}