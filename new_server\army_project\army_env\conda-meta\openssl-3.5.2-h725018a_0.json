{"build": "h725018a_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/win-64", "constrains": [], "depends": ["ca-certificates", "ucrt >=10.0.20348.0", "vc >=14.3,<15", "vc14_runtime >=14.44.35208"], "extracted_package_dir": "", "files": ["Library/bin/c_rehash.pl", "Library/bin/libcrypto-3-x64.dll", "Library/bin/libcrypto-3-x64.pdb", "Library/bin/libssl-3-x64.dll", "Library/bin/libssl-3-x64.pdb", "Library/bin/openssl.exe", "Library/bin/openssl.pdb", "Library/include/openssl/__DECC_INCLUDE_EPILOGUE.H", "Library/include/openssl/__DECC_INCLUDE_PROLOGUE.H", "Library/include/openssl/aes.h", "Library/include/openssl/applink.c", "Library/include/openssl/asn1.h", "Library/include/openssl/asn1err.h", "Library/include/openssl/asn1t.h", "Library/include/openssl/async.h", "Library/include/openssl/asyncerr.h", "Library/include/openssl/bio.h", "Library/include/openssl/bioerr.h", "Library/include/openssl/blowfish.h", "Library/include/openssl/bn.h", "Library/include/openssl/bnerr.h", "Library/include/openssl/buffer.h", "Library/include/openssl/buffererr.h", "Library/include/openssl/byteorder.h", "Library/include/openssl/camellia.h", "Library/include/openssl/cast.h", "Library/include/openssl/cmac.h", "Library/include/openssl/cmp.h", "Library/include/openssl/cmp_util.h", "Library/include/openssl/cmperr.h", "Library/include/openssl/cms.h", "Library/include/openssl/cmserr.h", "Library/include/openssl/comp.h", "Library/include/openssl/comperr.h", "Library/include/openssl/conf.h", "Library/include/openssl/conf_api.h", "Library/include/openssl/conferr.h", "Library/include/openssl/configuration.h", "Library/include/openssl/conftypes.h", "Library/include/openssl/core.h", "Library/include/openssl/core_dispatch.h", "Library/include/openssl/core_names.h", "Library/include/openssl/core_object.h", "Library/include/openssl/crmf.h", "Library/include/openssl/crmferr.h", "Library/include/openssl/crypto.h", "Library/include/openssl/cryptoerr.h", "Library/include/openssl/cryptoerr_legacy.h", "Library/include/openssl/ct.h", "Library/include/openssl/cterr.h", "Library/include/openssl/decoder.h", "Library/include/openssl/decodererr.h", "Library/include/openssl/des.h", "Library/include/openssl/dh.h", "Library/include/openssl/dherr.h", "Library/include/openssl/dsa.h", "Library/include/openssl/dsaerr.h", "Library/include/openssl/dtls1.h", "Library/include/openssl/e_os2.h", "Library/include/openssl/e_ostime.h", "Library/include/openssl/ebcdic.h", "Library/include/openssl/ec.h", "Library/include/openssl/ecdh.h", "Library/include/openssl/ecdsa.h", "Library/include/openssl/ecerr.h", "Library/include/openssl/encoder.h", "Library/include/openssl/encodererr.h", "Library/include/openssl/engine.h", "Library/include/openssl/engineerr.h", "Library/include/openssl/err.h", "Library/include/openssl/ess.h", "Library/include/openssl/esserr.h", "Library/include/openssl/evp.h", "Library/include/openssl/evperr.h", "Library/include/openssl/fips_names.h", "Library/include/openssl/fipskey.h", "Library/include/openssl/hmac.h", "Library/include/openssl/hpke.h", "Library/include/openssl/http.h", "Library/include/openssl/httperr.h", "Library/include/openssl/idea.h", "Library/include/openssl/indicator.h", "Library/include/openssl/kdf.h", "Library/include/openssl/kdferr.h", "Library/include/openssl/lhash.h", "Library/include/openssl/macros.h", "Library/include/openssl/md2.h", "Library/include/openssl/md4.h", "Library/include/openssl/md5.h", "Library/include/openssl/mdc2.h", "Library/include/openssl/ml_kem.h", "Library/include/openssl/modes.h", "Library/include/openssl/obj_mac.h", "Library/include/openssl/objects.h", "Library/include/openssl/objectserr.h", "Library/include/openssl/ocsp.h", "Library/include/openssl/ocsperr.h", "Library/include/openssl/opensslconf.h", "Library/include/openssl/opensslv.h", "Library/include/openssl/ossl_typ.h", "Library/include/openssl/param_build.h", "Library/include/openssl/params.h", "Library/include/openssl/pem.h", "Library/include/openssl/pem2.h", "Library/include/openssl/pemerr.h", "Library/include/openssl/pkcs12.h", "Library/include/openssl/pkcs12err.h", "Library/include/openssl/pkcs7.h", "Library/include/openssl/pkcs7err.h", "Library/include/openssl/prov_ssl.h", "Library/include/openssl/proverr.h", "Library/include/openssl/provider.h", "Library/include/openssl/quic.h", "Library/include/openssl/rand.h", "Library/include/openssl/randerr.h", "Library/include/openssl/rc2.h", "Library/include/openssl/rc4.h", "Library/include/openssl/rc5.h", "Library/include/openssl/ripemd.h", "Library/include/openssl/rsa.h", "Library/include/openssl/rsaerr.h", "Library/include/openssl/safestack.h", "Library/include/openssl/seed.h", "Library/include/openssl/self_test.h", "Library/include/openssl/sha.h", "Library/include/openssl/srp.h", "Library/include/openssl/srtp.h", "Library/include/openssl/ssl.h", "Library/include/openssl/ssl2.h", "Library/include/openssl/ssl3.h", "Library/include/openssl/sslerr.h", "Library/include/openssl/sslerr_legacy.h", "Library/include/openssl/stack.h", "Library/include/openssl/store.h", "Library/include/openssl/storeerr.h", "Library/include/openssl/symhacks.h", "Library/include/openssl/thread.h", "Library/include/openssl/tls1.h", "Library/include/openssl/trace.h", "Library/include/openssl/ts.h", "Library/include/openssl/tserr.h", "Library/include/openssl/txt_db.h", "Library/include/openssl/types.h", "Library/include/openssl/ui.h", "Library/include/openssl/uierr.h", "Library/include/openssl/whrlpool.h", "Library/include/openssl/x509.h", "Library/include/openssl/x509_acert.h", "Library/include/openssl/x509_vfy.h", "Library/include/openssl/x509err.h", "Library/include/openssl/x509v3.h", "Library/include/openssl/x509v3err.h", "Library/lib/cmake/OpenSSL/OpenSSLConfig.cmake", "Library/lib/cmake/OpenSSL/OpenSSLConfigVersion.cmake", "Library/lib/libcrypto.lib", "Library/lib/libssl.lib", "Library/lib/pkgconfig/libcrypto.pc", "Library/lib/pkgconfig/libssl.pc", "Library/lib/pkgconfig/openssl.pc", "Library/ssl/certs/.keep", "etc/conda/activate.d/openssl_activate-win.bat", "etc/conda/activate.d/openssl_activate-win.ps1", "etc/conda/activate.d/openssl_activate-win.sh", "etc/conda/deactivate.d/openssl_deactivate-win.bat", "etc/conda/deactivate.d/openssl_deactivate-win.ps1", "etc/conda/deactivate.d/openssl_deactivate-win.sh"], "fn": "openssl-3.5.2-h725018a_0.conda", "license": "Apache-2.0", "link": {"source": "", "type": 1}, "md5": "150d3920b420a27c0848acca158f94dc", "name": "openssl", "package_tarball_full_path": "", "paths_data": {"paths": [{"_path": "Library/bin/c_rehash.pl", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "D:\\\\bld\\\\openssl_split_1754464650477\\\\_h_env", "sha256": "0ff8a46753af064c7b6efb847aaeacd73cf599442b8db5ffe347f9fdeffd4de6", "sha256_in_prefix": "3d157f4b41a020072e1848be397e0273c367d2d4a289ed942b26cbe39bea01a6", "size_in_bytes": 7242}, {"_path": "Library/bin/libcrypto-3-x64.dll", "path_type": "hardlink", "sha256": "f8b6aeea92f90e90d7ce983d3040fade009e2fca11ad22c8fded8b79ac481ea9", "sha256_in_prefix": "f8b6aeea92f90e90d7ce983d3040fade009e2fca11ad22c8fded8b79ac481ea9", "size_in_bytes": 7314432}, {"_path": "Library/bin/libcrypto-3-x64.pdb", "path_type": "hardlink", "sha256": "585b1138855afa5da000c87bdf9c51f473adb0d606b0e1a4e1c05418cb54f977", "sha256_in_prefix": "585b1138855afa5da000c87bdf9c51f473adb0d606b0e1a4e1c05418cb54f977", "size_in_bytes": 25153536}, {"_path": "Library/bin/libssl-3-x64.dll", "path_type": "hardlink", "sha256": "4f3cf2bb98d1a54ba0171affeb985b16502d1698f1203a4e60c5351348a3909c", "sha256_in_prefix": "4f3cf2bb98d1a54ba0171affeb985b16502d1698f1203a4e60c5351348a3909c", "size_in_bytes": 1313792}, {"_path": "Library/bin/libssl-3-x64.pdb", "path_type": "hardlink", "sha256": "617fbea9ee736bae7aac2072bd3659a6a88600c2b453be3a6dc00a22fb6ed093", "sha256_in_prefix": "617fbea9ee736bae7aac2072bd3659a6a88600c2b453be3a6dc00a22fb6ed093", "size_in_bytes": 5918720}, {"_path": "Library/bin/openssl.exe", "path_type": "hardlink", "sha256": "01afe88d521ae55e1c63f464b2fb78e3aa66cca63d3995cbfba173ff9d0b0479", "sha256_in_prefix": "01afe88d521ae55e1c63f464b2fb78e3aa66cca63d3995cbfba173ff9d0b0479", "size_in_bytes": 806400}, {"_path": "Library/bin/openssl.pdb", "path_type": "hardlink", "sha256": "124af3c5c2d1b72bd596ca63e04cd35b574668396649737de8a2f1e3c6a2a7ca", "sha256_in_prefix": "124af3c5c2d1b72bd596ca63e04cd35b574668396649737de8a2f1e3c6a2a7ca", "size_in_bytes": 3944448}, {"_path": "Library/include/openssl/__DECC_INCLUDE_EPILOGUE.H", "path_type": "hardlink", "sha256": "3d837d015f23ad248d7e0c74b5b8ca102d81525f166a0a4b7c19900eea982644", "sha256_in_prefix": "3d837d015f23ad248d7e0c74b5b8ca102d81525f166a0a4b7c19900eea982644", "size_in_bytes": 729}, {"_path": "Library/include/openssl/__DECC_INCLUDE_PROLOGUE.H", "path_type": "hardlink", "sha256": "e66be3418a7b707f09fa011c85b0b3fdfcfa1740c46da11385abf23fe9983529", "sha256_in_prefix": "e66be3418a7b707f09fa011c85b0b3fdfcfa1740c46da11385abf23fe9983529", "size_in_bytes": 801}, {"_path": "Library/include/openssl/aes.h", "path_type": "hardlink", "sha256": "27aaa89367b022b12b66cf52c3c2d68f6761965ac36f3f1153202fa44692ad0e", "sha256_in_prefix": "27aaa89367b022b12b66cf52c3c2d68f6761965ac36f3f1153202fa44692ad0e", "size_in_bytes": 3752}, {"_path": "Library/include/openssl/applink.c", "path_type": "hardlink", "sha256": "6862805c3d7fed06df62c5309f5c24798725b58bba144a4f02db985ff9965347", "sha256_in_prefix": "6862805c3d7fed06df62c5309f5c24798725b58bba144a4f02db985ff9965347", "size_in_bytes": 3988}, {"_path": "Library/include/openssl/asn1.h", "path_type": "hardlink", "sha256": "fc7fcc4dbe7c92eb630ce766bb1aed46d9807f075a815025beb949ff0048c1c7", "sha256_in_prefix": "fc7fcc4dbe7c92eb630ce766bb1aed46d9807f075a815025beb949ff0048c1c7", "size_in_bytes": 62286}, {"_path": "Library/include/openssl/asn1err.h", "path_type": "hardlink", "sha256": "50703d11cce3d6defab8d664d3d917c6aaaa9ee677538757b811bbb288d60407", "sha256_in_prefix": "50703d11cce3d6defab8d664d3d917c6aaaa9ee677538757b811bbb288d60407", "size_in_bytes": 7855}, {"_path": "Library/include/openssl/asn1t.h", "path_type": "hardlink", "sha256": "03fcf37af6248fad3421306aa87d1bb2365a4b29f4f7be035d87651e42ed012c", "sha256_in_prefix": "03fcf37af6248fad3421306aa87d1bb2365a4b29f4f7be035d87651e42ed012c", "size_in_bytes": 36883}, {"_path": "Library/include/openssl/async.h", "path_type": "hardlink", "sha256": "f0112bd2d6f7ef9d2192f614c7d43bf6a0b3cc8be8f3116ba539b7a6579698a7", "sha256_in_prefix": "f0112bd2d6f7ef9d2192f614c7d43bf6a0b3cc8be8f3116ba539b7a6579698a7", "size_in_bytes": 3504}, {"_path": "Library/include/openssl/asyncerr.h", "path_type": "hardlink", "sha256": "154f003cfbf49040a04d9aac459cf5009a5a1d76298b222d66ba5b5a4e3721af", "sha256_in_prefix": "154f003cfbf49040a04d9aac459cf5009a5a1d76298b222d66ba5b5a4e3721af", "size_in_bytes": 842}, {"_path": "Library/include/openssl/bio.h", "path_type": "hardlink", "sha256": "f301444ceff92439aa07390326c0494903793804aeab8125d62e604ca308f72f", "sha256_in_prefix": "f301444ceff92439aa07390326c0494903793804aeab8125d62e604ca308f72f", "size_in_bytes": 47487}, {"_path": "Library/include/openssl/bioerr.h", "path_type": "hardlink", "sha256": "fab33e1a3a6d998634e31e86556c7badfae58876c753d10c841e3506edb9bb3e", "sha256_in_prefix": "fab33e1a3a6d998634e31e86556c7badfae58876c753d10c841e3506edb9bb3e", "size_in_bytes": 3515}, {"_path": "Library/include/openssl/blowfish.h", "path_type": "hardlink", "sha256": "fb4b19b7730d1cc7ff2b9da1435a506ad0ef50263bd168c5ff24214a06580282", "sha256_in_prefix": "fb4b19b7730d1cc7ff2b9da1435a506ad0ef50263bd168c5ff24214a06580282", "size_in_bytes": 2693}, {"_path": "Library/include/openssl/bn.h", "path_type": "hardlink", "sha256": "ee24f408eb0e8cdf72e94d6d7fd4a411d3e12824592e493b72764957cf75a58b", "sha256_in_prefix": "ee24f408eb0e8cdf72e94d6d7fd4a411d3e12824592e493b72764957cf75a58b", "size_in_bytes": 24183}, {"_path": "Library/include/openssl/bnerr.h", "path_type": "hardlink", "sha256": "f0dfac26985a7ae40174e90173df9f95b15bba4d3768290746d7258ff1b0ae64", "sha256_in_prefix": "f0dfac26985a7ae40174e90173df9f95b15bba4d3768290746d7258ff1b0ae64", "size_in_bytes": 1949}, {"_path": "Library/include/openssl/buffer.h", "path_type": "hardlink", "sha256": "c87b52702746e224e6242f4a2a2070b007502ea92063b41df2c4f6bec11c37ca", "sha256_in_prefix": "c87b52702746e224e6242f4a2a2070b007502ea92063b41df2c4f6bec11c37ca", "size_in_bytes": 1658}, {"_path": "Library/include/openssl/buffererr.h", "path_type": "hardlink", "sha256": "73f33a7b4406477a0eaf9d0ec42f43b2594167b1d6b84175f378cf5b0de07c12", "sha256_in_prefix": "73f33a7b4406477a0eaf9d0ec42f43b2594167b1d6b84175f378cf5b0de07c12", "size_in_bytes": 594}, {"_path": "Library/include/openssl/byteorder.h", "path_type": "hardlink", "sha256": "7ea79e5a67443c807526cd77a081484dbcabe09bb4eb743454a0064e037cbaaa", "sha256_in_prefix": "7ea79e5a67443c807526cd77a081484dbcabe09bb4eb743454a0064e037cbaaa", "size_in_bytes": 8631}, {"_path": "Library/include/openssl/camellia.h", "path_type": "hardlink", "sha256": "d1cee6e44668fba0e46c38db7394aa094c6cd2a25b97dbcfcc6f0ff4414f8ebf", "sha256_in_prefix": "d1cee6e44668fba0e46c38db7394aa094c6cd2a25b97dbcfcc6f0ff4414f8ebf", "size_in_bytes": 5069}, {"_path": "Library/include/openssl/cast.h", "path_type": "hardlink", "sha256": "654ac650ae74ca5e9a87ab46c1205157a7489097d005fdccc4c52912cfcefa55", "sha256_in_prefix": "654ac650ae74ca5e9a87ab46c1205157a7489097d005fdccc4c52912cfcefa55", "size_in_bytes": 2066}, {"_path": "Library/include/openssl/cmac.h", "path_type": "hardlink", "sha256": "b26f8ddb9f60eef2601a84a5455c11060e028d8ce700cae682c4a02ffe2f2ca2", "sha256_in_prefix": "b26f8ddb9f60eef2601a84a5455c11060e028d8ce700cae682c4a02ffe2f2ca2", "size_in_bytes": 1608}, {"_path": "Library/include/openssl/cmp.h", "path_type": "hardlink", "sha256": "00f90c0b9ae41c5a033cdfc471349b0d5944c808389c2c8a591dbe2eaa83da9b", "sha256_in_prefix": "00f90c0b9ae41c5a033cdfc471349b0d5944c808389c2c8a591dbe2eaa83da9b", "size_in_bytes": 51336}, {"_path": "Library/include/openssl/cmp_util.h", "path_type": "hardlink", "sha256": "7a982bac5840812b486176102b1fe8b48dda8cce0fe94f2d35aff5057a99004e", "sha256_in_prefix": "7a982bac5840812b486176102b1fe8b48dda8cce0fe94f2d35aff5057a99004e", "size_in_bytes": 1742}, {"_path": "Library/include/openssl/cmperr.h", "path_type": "hardlink", "sha256": "b1c17cf0b79dccd69f05f362bde67d13588dab20614e50500e604dbabb18f2ee", "sha256_in_prefix": "b1c17cf0b79dccd69f05f362bde67d13588dab20614e50500e604dbabb18f2ee", "size_in_bytes": 7299}, {"_path": "Library/include/openssl/cms.h", "path_type": "hardlink", "sha256": "13bf747be6a2c40de3a25ae08ae47f259c4c65232086251280e19932fe919081", "sha256_in_prefix": "13bf747be6a2c40de3a25ae08ae47f259c4c65232086251280e19932fe919081", "size_in_bytes": 35674}, {"_path": "Library/include/openssl/cmserr.h", "path_type": "hardlink", "sha256": "2e4c3a676298da7be496b4629b1e90e5bab3ce4bf0cd6662c0fb41cc620ae9cc", "sha256_in_prefix": "2e4c3a676298da7be496b4629b1e90e5bab3ce4bf0cd6662c0fb41cc620ae9cc", "size_in_bytes": 6794}, {"_path": "Library/include/openssl/comp.h", "path_type": "hardlink", "sha256": "4388d300c658b9523ce561d3c289646596aca4f65d4465d4a45f13894da69653", "sha256_in_prefix": "4388d300c658b9523ce561d3c289646596aca4f65d4465d4a45f13894da69653", "size_in_bytes": 4747}, {"_path": "Library/include/openssl/comperr.h", "path_type": "hardlink", "sha256": "851f81212d489813f368757bc9511ccfa76b9cb66024607f3f0d4846a42eb085", "sha256_in_prefix": "851f81212d489813f368757bc9511ccfa76b9cb66024607f3f0d4846a42eb085", "size_in_bytes": 1254}, {"_path": "Library/include/openssl/conf.h", "path_type": "hardlink", "sha256": "7624a2978f9a051aa7a769937c95bb2fd9e3a7bb0117d2499fb6cc5e1ac914df", "sha256_in_prefix": "7624a2978f9a051aa7a769937c95bb2fd9e3a7bb0117d2499fb6cc5e1ac914df", "size_in_bytes": 10887}, {"_path": "Library/include/openssl/conf_api.h", "path_type": "hardlink", "sha256": "a66bcc69464235679980efc4a687a4fe036388da91173809ca45c0a3cfe47a5b", "sha256_in_prefix": "a66bcc69464235679980efc4a687a4fe036388da91173809ca45c0a3cfe47a5b", "size_in_bytes": 1420}, {"_path": "Library/include/openssl/conferr.h", "path_type": "hardlink", "sha256": "ee8aaa36553894d836b728ce9a52234d22b5d812bbbb75fa09645e7b1011346a", "sha256_in_prefix": "ee8aaa36553894d836b728ce9a52234d22b5d812bbbb75fa09645e7b1011346a", "size_in_bytes": 2265}, {"_path": "Library/include/openssl/configuration.h", "path_type": "hardlink", "sha256": "e9ec1c8e0733250ad81ce56d33a1362e84eeadef2a7c7d57a0973a4e4261bd62", "sha256_in_prefix": "e9ec1c8e0733250ad81ce56d33a1362e84eeadef2a7c7d57a0973a4e4261bd62", "size_in_bytes": 4563}, {"_path": "Library/include/openssl/conftypes.h", "path_type": "hardlink", "sha256": "e8f6697076d2464eaecfe2cdae8d2045388c53da2372fd52df5f6cfdc4d63375", "sha256_in_prefix": "e8f6697076d2464eaecfe2cdae8d2045388c53da2372fd52df5f6cfdc4d63375", "size_in_bytes": 1190}, {"_path": "Library/include/openssl/core.h", "path_type": "hardlink", "sha256": "a4a8e73bd642913e2ec268d13460f7bb97aceea59152430483e013775328eb3d", "sha256_in_prefix": "a4a8e73bd642913e2ec268d13460f7bb97aceea59152430483e013775328eb3d", "size_in_bytes": 8177}, {"_path": "Library/include/openssl/core_dispatch.h", "path_type": "hardlink", "sha256": "9d3cfc1ce5ffee3187ddadd85bb777bd6c386a607fddbf3b34e94f123733f7df", "sha256_in_prefix": "9d3cfc1ce5ffee3187ddadd85bb777bd6c386a607fddbf3b34e94f123733f7df", "size_in_bytes": 58561}, {"_path": "Library/include/openssl/core_names.h", "path_type": "hardlink", "sha256": "a38b769c347de045aa8468f49b35fbb156594318113a706e4c064eaaea070c77", "sha256_in_prefix": "a38b769c347de045aa8468f49b35fbb156594318113a706e4c064eaaea070c77", "size_in_bytes": 30335}, {"_path": "Library/include/openssl/core_object.h", "path_type": "hardlink", "sha256": "7a7172d30597e3a3e06c4e67a049d1335aa6f7d5b49641abba8fd4d5a1c07563", "sha256_in_prefix": "7a7172d30597e3a3e06c4e67a049d1335aa6f7d5b49641abba8fd4d5a1c07563", "size_in_bytes": 1126}, {"_path": "Library/include/openssl/crmf.h", "path_type": "hardlink", "sha256": "b2bbfa32fe9c11e0e059cc3c7dcad2eba4ab169f4be4f8494ee490982b4a7a27", "sha256_in_prefix": "b2bbfa32fe9c11e0e059cc3c7dcad2eba4ab169f4be4f8494ee490982b4a7a27", "size_in_bytes": 21055}, {"_path": "Library/include/openssl/crmferr.h", "path_type": "hardlink", "sha256": "71a65c9e51d2b168dbe50948b35917cc2d96a4135701c43f0f16a85ce4e6013a", "sha256_in_prefix": "71a65c9e51d2b168dbe50948b35917cc2d96a4135701c43f0f16a85ce4e6013a", "size_in_bytes": 2452}, {"_path": "Library/include/openssl/crypto.h", "path_type": "hardlink", "sha256": "a2256501b84f876c520838fb769991e1a06d97e7444bb3c5b6ca5555db660cf7", "sha256_in_prefix": "a2256501b84f876c520838fb769991e1a06d97e7444bb3c5b6ca5555db660cf7", "size_in_bytes": 25907}, {"_path": "Library/include/openssl/cryptoerr.h", "path_type": "hardlink", "sha256": "b4a370d355fbfaad54f3241044f755c93b2265490f188e877150ec7550fe59ff", "sha256_in_prefix": "b4a370d355fbfaad54f3241044f755c93b2265490f188e877150ec7550fe59ff", "size_in_bytes": 2529}, {"_path": "Library/include/openssl/cryptoerr_legacy.h", "path_type": "hardlink", "sha256": "870042252331e89723d31079469104cafd676f0fedcbe0d99f56f3e8862fff8d", "sha256_in_prefix": "870042252331e89723d31079469104cafd676f0fedcbe0d99f56f3e8862fff8d", "size_in_bytes": 80396}, {"_path": "Library/include/openssl/ct.h", "path_type": "hardlink", "sha256": "6ccbacdb973371c4a93638e14b0096494d8a22df7c84e60a5433aac8a2f00bb5", "sha256_in_prefix": "6ccbacdb973371c4a93638e14b0096494d8a22df7c84e60a5433aac8a2f00bb5", "size_in_bytes": 23283}, {"_path": "Library/include/openssl/cterr.h", "path_type": "hardlink", "sha256": "562bfe4ac984ebfef4fb91bdbe0a649d157f5057ab61ffee3a844d23f7c72c0a", "sha256_in_prefix": "562bfe4ac984ebfef4fb91bdbe0a649d157f5057ab61ffee3a844d23f7c72c0a", "size_in_bytes": 1688}, {"_path": "Library/include/openssl/decoder.h", "path_type": "hardlink", "sha256": "8419fd9e4e333fd477238bbad4ff875d5657b02cc39635c3a5c15f3a5bc7f0f2", "sha256_in_prefix": "8419fd9e4e333fd477238bbad4ff875d5657b02cc39635c3a5c15f3a5bc7f0f2", "size_in_bytes": 5760}, {"_path": "Library/include/openssl/decodererr.h", "path_type": "hardlink", "sha256": "a785fb95930e8b4a18054f77b7d5143d44673f4ca57682899bc2bf3464cafccf", "sha256_in_prefix": "a785fb95930e8b4a18054f77b7d5143d44673f4ca57682899bc2bf3464cafccf", "size_in_bytes": 791}, {"_path": "Library/include/openssl/des.h", "path_type": "hardlink", "sha256": "bb13c7c5e13f3402d674fa88994b92ed72d6cdc1116707765d28bd7e0de31285", "sha256_in_prefix": "bb13c7c5e13f3402d674fa88994b92ed72d6cdc1116707765d28bd7e0de31285", "size_in_bytes": 8525}, {"_path": "Library/include/openssl/dh.h", "path_type": "hardlink", "sha256": "d834937ef536956fffe4f567745a1736714ffec4c4cf248d94c910b1748b14cf", "sha256_in_prefix": "d834937ef536956fffe4f567745a1736714ffec4c4cf248d94c910b1748b14cf", "size_in_bytes": 15475}, {"_path": "Library/include/openssl/dherr.h", "path_type": "hardlink", "sha256": "9a9878ebd561a4fb1d38c8157d511f5de0893ac7b928f33b5cc52450bcc41a9d", "sha256_in_prefix": "9a9878ebd561a4fb1d38c8157d511f5de0893ac7b928f33b5cc52450bcc41a9d", "size_in_bytes": 2570}, {"_path": "Library/include/openssl/dsa.h", "path_type": "hardlink", "sha256": "3e9b65a16899dd737b4c8fa99bd94f0cf94dcfb6ebea4a24e7b21fc92e409e46", "sha256_in_prefix": "3e9b65a16899dd737b4c8fa99bd94f0cf94dcfb6ebea4a24e7b21fc92e409e46", "size_in_bytes": 12532}, {"_path": "Library/include/openssl/dsaerr.h", "path_type": "hardlink", "sha256": "69c2ecff5f62898461bc521ea918abd2a673206dd5e8d43288ad25d2c012f163", "sha256_in_prefix": "69c2ecff5f62898461bc521ea918abd2a673206dd5e8d43288ad25d2c012f163", "size_in_bytes": 1629}, {"_path": "Library/include/openssl/dtls1.h", "path_type": "hardlink", "sha256": "1d1f404032a9eb31408c1f10bdff554d5740fb345b64b86fb74da8df95fbd901", "sha256_in_prefix": "1d1f404032a9eb31408c1f10bdff554d5740fb345b64b86fb74da8df95fbd901", "size_in_bytes": 1465}, {"_path": "Library/include/openssl/e_os2.h", "path_type": "hardlink", "sha256": "3eb5b2ed396dfb68e10acb59f714732be4941d17a97ebb659768c570da6fad0a", "sha256_in_prefix": "3eb5b2ed396dfb68e10acb59f714732be4941d17a97ebb659768c570da6fad0a", "size_in_bytes": 8849}, {"_path": "Library/include/openssl/e_ostime.h", "path_type": "hardlink", "sha256": "587a7593925c8d5b2f0dc3060904e3a4a7472be0ebc7dbfe2f6af6e40eb8bcc7", "sha256_in_prefix": "587a7593925c8d5b2f0dc3060904e3a4a7472be0ebc7dbfe2f6af6e40eb8bcc7", "size_in_bytes": 1188}, {"_path": "Library/include/openssl/ebcdic.h", "path_type": "hardlink", "sha256": "75a668c25c97853d5ba37ebce060a15152573242e3729d42830eba1daa642404", "sha256_in_prefix": "75a668c25c97853d5ba37ebce060a15152573242e3729d42830eba1daa642404", "size_in_bytes": 1042}, {"_path": "Library/include/openssl/ec.h", "path_type": "hardlink", "sha256": "0b028cf04ba8769a693f022725c91ef902a9e31f12a950180d1b4e8fa6952ff2", "sha256_in_prefix": "0b028cf04ba8769a693f022725c91ef902a9e31f12a950180d1b4e8fa6952ff2", "size_in_bytes": 68440}, {"_path": "Library/include/openssl/ecdh.h", "path_type": "hardlink", "sha256": "5b99fdd1dfea38640ed8a506fb9b66db381cc26a1254448a81cc6b161e41850f", "sha256_in_prefix": "5b99fdd1dfea38640ed8a506fb9b66db381cc26a1254448a81cc6b161e41850f", "size_in_bytes": 361}, {"_path": "Library/include/openssl/ecdsa.h", "path_type": "hardlink", "sha256": "5b99fdd1dfea38640ed8a506fb9b66db381cc26a1254448a81cc6b161e41850f", "sha256_in_prefix": "5b99fdd1dfea38640ed8a506fb9b66db381cc26a1254448a81cc6b161e41850f", "size_in_bytes": 361}, {"_path": "Library/include/openssl/ecerr.h", "path_type": "hardlink", "sha256": "ce4fec7ee41de25a20abb7a9f00fe93305793a7bd2023d434b9aa6f64f91058a", "sha256_in_prefix": "ce4fec7ee41de25a20abb7a9f00fe93305793a7bd2023d434b9aa6f64f91058a", "size_in_bytes": 5405}, {"_path": "Library/include/openssl/encoder.h", "path_type": "hardlink", "sha256": "907d2f061c2972447d3f0c1cfc149c78791b1e4bdc131ad5a3eed1d084c76b41", "sha256_in_prefix": "907d2f061c2972447d3f0c1cfc149c78791b1e4bdc131ad5a3eed1d084c76b41", "size_in_bytes": 5450}, {"_path": "Library/include/openssl/encodererr.h", "path_type": "hardlink", "sha256": "63504766e9fcf36fe1527d95fe21460574896da187c60707bfa68254a35693b7", "sha256_in_prefix": "63504766e9fcf36fe1527d95fe21460574896da187c60707bfa68254a35693b7", "size_in_bytes": 791}, {"_path": "Library/include/openssl/engine.h", "path_type": "hardlink", "sha256": "9fd9863831c6871d927103abf61bd11a7ba3059ff8b0f8f1db03f0d83cde31e1", "sha256_in_prefix": "9fd9863831c6871d927103abf61bd11a7ba3059ff8b0f8f1db03f0d83cde31e1", "size_in_bytes": 38823}, {"_path": "Library/include/openssl/engineerr.h", "path_type": "hardlink", "sha256": "8616a93b1b1bd8d1221844834817c28b7da78be1649a5b1780d9ea65fba8807c", "sha256_in_prefix": "8616a93b1b1bd8d1221844834817c28b7da78be1649a5b1780d9ea65fba8807c", "size_in_bytes": 2838}, {"_path": "Library/include/openssl/err.h", "path_type": "hardlink", "sha256": "ed10bc0f932f8e3509da3bbfe3f077e448476eb92f988f0da0afcb378e4b2f78", "sha256_in_prefix": "ed10bc0f932f8e3509da3bbfe3f077e448476eb92f988f0da0afcb378e4b2f78", "size_in_bytes": 22910}, {"_path": "Library/include/openssl/ess.h", "path_type": "hardlink", "sha256": "494f87fe22195a9756db7e603b7e53f2c26145da37ab6e274400929e7bf3cc50", "sha256_in_prefix": "494f87fe22195a9756db7e603b7e53f2c26145da37ab6e274400929e7bf3cc50", "size_in_bytes": 9096}, {"_path": "Library/include/openssl/esserr.h", "path_type": "hardlink", "sha256": "e791193e891b0784670d5410539aeea9d2a8591de71495b4add6e7dbf9dc22cd", "sha256_in_prefix": "e791193e891b0784670d5410539aeea9d2a8591de71495b4add6e7dbf9dc22cd", "size_in_bytes": 1144}, {"_path": "Library/include/openssl/evp.h", "path_type": "hardlink", "sha256": "13c66729b8a4a87d998a1dc2f2794545540e76bcf0c6913fe6a88d696935fc9b", "sha256_in_prefix": "13c66729b8a4a87d998a1dc2f2794545540e76bcf0c6913fe6a88d696935fc9b", "size_in_bytes": 111845}, {"_path": "Library/include/openssl/evperr.h", "path_type": "hardlink", "sha256": "609afaf232dd280328b65b95d2e1ae45d428d12de16e74acc21e36a7673e28ff", "sha256_in_prefix": "609afaf232dd280328b65b95d2e1ae45d428d12de16e74acc21e36a7673e28ff", "size_in_bytes": 8224}, {"_path": "Library/include/openssl/fips_names.h", "path_type": "hardlink", "sha256": "dbf5cbac0cae8cc273cc3fd0d9a855194bcd8d1110c3f5a9456d3f2edfe2770b", "sha256_in_prefix": "dbf5cbac0cae8cc273cc3fd0d9a855194bcd8d1110c3f5a9456d3f2edfe2770b", "size_in_bytes": 1662}, {"_path": "Library/include/openssl/fipskey.h", "path_type": "hardlink", "sha256": "fa2962d72f558a6e419e25082ded303ed675015d30d4fb0cdd0b10d95c712ea6", "sha256_in_prefix": "fa2962d72f558a6e419e25082ded303ed675015d30d4fb0cdd0b10d95c712ea6", "size_in_bytes": 1164}, {"_path": "Library/include/openssl/hmac.h", "path_type": "hardlink", "sha256": "e49fbe0086f8fbefa5648eef70bc84e8090a9226a1e3c6e856499373004aed0a", "sha256_in_prefix": "e49fbe0086f8fbefa5648eef70bc84e8090a9226a1e3c6e856499373004aed0a", "size_in_bytes": 2141}, {"_path": "Library/include/openssl/hpke.h", "path_type": "hardlink", "sha256": "99947ae58970eacf9b8453d7ef6c2c908cf1a61a28eaf5c3456eb95bd0aefd93", "sha256_in_prefix": "99947ae58970eacf9b8453d7ef6c2c908cf1a61a28eaf5c3456eb95bd0aefd93", "size_in_bytes": 6983}, {"_path": "Library/include/openssl/http.h", "path_type": "hardlink", "sha256": "ee11e0cdbdf1b54dabeef885cc577708e5d7e58770fc7eafb5724e47a87fa72a", "sha256_in_prefix": "ee11e0cdbdf1b54dabeef885cc577708e5d7e58770fc7eafb5724e47a87fa72a", "size_in_bytes": 5667}, {"_path": "Library/include/openssl/httperr.h", "path_type": "hardlink", "sha256": "7b8903c4048411d4541a64f47e9479eeb4715d03b835f2244206648a48422c97", "sha256_in_prefix": "7b8903c4048411d4541a64f47e9479eeb4715d03b835f2244206648a48422c97", "size_in_bytes": 2513}, {"_path": "Library/include/openssl/idea.h", "path_type": "hardlink", "sha256": "239122df15e738d7552dd76850c55a9ffe0136f33506c23d9058215a1255af66", "sha256_in_prefix": "239122df15e738d7552dd76850c55a9ffe0136f33506c23d9058215a1255af66", "size_in_bytes": 3010}, {"_path": "Library/include/openssl/indicator.h", "path_type": "hardlink", "sha256": "a978c859885ddbb45a2da177bf13e47c91b49930e6c03464f8a8104aa6b12dff", "sha256_in_prefix": "a978c859885ddbb45a2da177bf13e47c91b49930e6c03464f8a8104aa6b12dff", "size_in_bytes": 917}, {"_path": "Library/include/openssl/kdf.h", "path_type": "hardlink", "sha256": "41756fe038443d1d270458d53d6e42ea78d12d980728b6a9284fa259958ea00a", "sha256_in_prefix": "41756fe038443d1d270458d53d6e42ea78d12d980728b6a9284fa259958ea00a", "size_in_bytes": 5619}, {"_path": "Library/include/openssl/kdferr.h", "path_type": "hardlink", "sha256": "3d9f27fffdb49e0ece9d5a62adbb9cc42c56262b00cc8ce7f956b2cb05a2a22d", "sha256_in_prefix": "3d9f27fffdb49e0ece9d5a62adbb9cc42c56262b00cc8ce7f956b2cb05a2a22d", "size_in_bytes": 482}, {"_path": "Library/include/openssl/lhash.h", "path_type": "hardlink", "sha256": "d38353268d77a96cdadb629b6d7e36a2e002b88d5de5e411e240a7c156afcd97", "sha256_in_prefix": "d38353268d77a96cdadb629b6d7e36a2e002b88d5de5e411e240a7c156afcd97", "size_in_bytes": 18839}, {"_path": "Library/include/openssl/macros.h", "path_type": "hardlink", "sha256": "26d8f21f1dd19cbaaf22b1c32769a220dd3847011372661c41aa632631d69cb1", "sha256_in_prefix": "26d8f21f1dd19cbaaf22b1c32769a220dd3847011372661c41aa632631d69cb1", "size_in_bytes": 11478}, {"_path": "Library/include/openssl/md2.h", "path_type": "hardlink", "sha256": "4add77ed047736979dc442a49d42921cce21e654a2dceef058d0191aa2d3c941", "sha256_in_prefix": "4add77ed047736979dc442a49d42921cce21e654a2dceef058d0191aa2d3c941", "size_in_bytes": 1461}, {"_path": "Library/include/openssl/md4.h", "path_type": "hardlink", "sha256": "0472e597d139b44dd7d78d9093a5d8109417d18e9955fc940f1ea3e2e892ab44", "sha256_in_prefix": "0472e597d139b44dd7d78d9093a5d8109417d18e9955fc940f1ea3e2e892ab44", "size_in_bytes": 1699}, {"_path": "Library/include/openssl/md5.h", "path_type": "hardlink", "sha256": "308c901ec1a28f9b0098717f689ca63e104ce805050802d38b8f122d85ab2c78", "sha256_in_prefix": "308c901ec1a28f9b0098717f689ca63e104ce805050802d38b8f122d85ab2c78", "size_in_bytes": 1696}, {"_path": "Library/include/openssl/mdc2.h", "path_type": "hardlink", "sha256": "42b844c9ae9e00e7c0b0e28858b8b3db7b8abf7e514e5e63f43456371ed3384b", "sha256_in_prefix": "42b844c9ae9e00e7c0b0e28858b8b3db7b8abf7e514e5e63f43456371ed3384b", "size_in_bytes": 1441}, {"_path": "Library/include/openssl/ml_kem.h", "path_type": "hardlink", "sha256": "07460b4349fa545b1b7590e3a194a6b097a7c0af92ead1e15201dfac6611af82", "sha256_in_prefix": "07460b4349fa545b1b7590e3a194a6b097a7c0af92ead1e15201dfac6611af82", "size_in_bytes": 1042}, {"_path": "Library/include/openssl/modes.h", "path_type": "hardlink", "sha256": "4a8b3b1dafc15798a3b2bef0e3885275746e7fae73a0d96e55da55261554ba52", "sha256_in_prefix": "4a8b3b1dafc15798a3b2bef0e3885275746e7fae73a0d96e55da55261554ba52", "size_in_bytes": 10786}, {"_path": "Library/include/openssl/obj_mac.h", "path_type": "hardlink", "sha256": "a78716985355946f23ff0376bc546249a40af14b983fb6e0f081fbff959b24cc", "sha256_in_prefix": "a78716985355946f23ff0376bc546249a40af14b983fb6e0f081fbff959b24cc", "size_in_bytes": 289901}, {"_path": "Library/include/openssl/objects.h", "path_type": "hardlink", "sha256": "9f04a6f480aa7af7a6cd6563c712818799cbb12e99061f57b46a72060909ef90", "sha256_in_prefix": "9f04a6f480aa7af7a6cd6563c712818799cbb12e99061f57b46a72060909ef90", "size_in_bytes": 6894}, {"_path": "Library/include/openssl/objectserr.h", "path_type": "hardlink", "sha256": "e17a8d7f62a1ef257fd90e604d4293bf02d5f81ae8198efe1e197c5b27baeb8c", "sha256_in_prefix": "e17a8d7f62a1ef257fd90e604d4293bf02d5f81ae8198efe1e197c5b27baeb8c", "size_in_bytes": 782}, {"_path": "Library/include/openssl/ocsp.h", "path_type": "hardlink", "sha256": "01aa2aa17ccad22ebc1a1701ad27b67a165a0c23f9e50fe5ad86b4e90ef190b9", "sha256_in_prefix": "01aa2aa17ccad22ebc1a1701ad27b67a165a0c23f9e50fe5ad86b4e90ef190b9", "size_in_bytes": 29835}, {"_path": "Library/include/openssl/ocsperr.h", "path_type": "hardlink", "sha256": "178329cfc042d3f1eb6e179206d844de41ba05ee4ac0ed9e3e6c861fb49d68ea", "sha256_in_prefix": "178329cfc042d3f1eb6e179206d844de41ba05ee4ac0ed9e3e6c861fb49d68ea", "size_in_bytes": 2200}, {"_path": "Library/include/openssl/opensslconf.h", "path_type": "hardlink", "sha256": "890184233890bacd52fd420fef07befad411b9a318b97efbf36f46673d3e7841", "sha256_in_prefix": "890184233890bacd52fd420fef07befad411b9a318b97efbf36f46673d3e7841", "size_in_bytes": 515}, {"_path": "Library/include/openssl/opensslv.h", "path_type": "hardlink", "sha256": "cf9dd11dbca60d672aa3487f5c8811c7124812aefd2a87069cf07459de5bea77", "sha256_in_prefix": "cf9dd11dbca60d672aa3487f5c8811c7124812aefd2a87069cf07459de5bea77", "size_in_bytes": 3298}, {"_path": "Library/include/openssl/ossl_typ.h", "path_type": "hardlink", "sha256": "76cb203ef3bcd305f4171e1d33f3f3319dee6354c2433493e5e9068aa79672fd", "sha256_in_prefix": "76cb203ef3bcd305f4171e1d33f3f3319dee6354c2433493e5e9068aa79672fd", "size_in_bytes": 562}, {"_path": "Library/include/openssl/param_build.h", "path_type": "hardlink", "sha256": "3bf39b1037256466f1a89868621b2b62f1d05e63064159e60727041b170d55e3", "sha256_in_prefix": "3bf39b1037256466f1a89868621b2b62f1d05e63064159e60727041b170d55e3", "size_in_bytes": 2809}, {"_path": "Library/include/openssl/params.h", "path_type": "hardlink", "sha256": "e7741ef8db1cf077729e4f8e9a020e5a49375a2cf6586fe14dcff8d479304c12", "sha256_in_prefix": "e7741ef8db1cf077729e4f8e9a020e5a49375a2cf6586fe14dcff8d479304c12", "size_in_bytes": 7440}, {"_path": "Library/include/openssl/pem.h", "path_type": "hardlink", "sha256": "7f599796f0e300ce58455f148238f93168692a845f5548b16ecdad5ed60ae957", "sha256_in_prefix": "7f599796f0e300ce58455f148238f93168692a845f5548b16ecdad5ed60ae957", "size_in_bytes": 26259}, {"_path": "Library/include/openssl/pem2.h", "path_type": "hardlink", "sha256": "a34a1607983b5f32be8ca49e75c3b41f1c9413b4eb777af144958283ecbd3922", "sha256_in_prefix": "a34a1607983b5f32be8ca49e75c3b41f1c9413b4eb777af144958283ecbd3922", "size_in_bytes": 531}, {"_path": "Library/include/openssl/pemerr.h", "path_type": "hardlink", "sha256": "9ce74017ece7f9c852421ef8c8fb93e2475fdf263ed6eeeccc4c3faae8f6d2ae", "sha256_in_prefix": "9ce74017ece7f9c852421ef8c8fb93e2475fdf263ed6eeeccc4c3faae8f6d2ae", "size_in_bytes": 2696}, {"_path": "Library/include/openssl/pkcs12.h", "path_type": "hardlink", "sha256": "aea29238ee34ec1aad28bf2da5bbb51758958c6c1ac0ac779aae0fa62bdf4a98", "sha256_in_prefix": "aea29238ee34ec1aad28bf2da5bbb51758958c6c1ac0ac779aae0fa62bdf4a98", "size_in_bytes": 20762}, {"_path": "Library/include/openssl/pkcs12err.h", "path_type": "hardlink", "sha256": "fa281e5b93652e6c2c31393f62539d5252c125a4b1c4214f21fa321bd033da10", "sha256_in_prefix": "fa281e5b93652e6c2c31393f62539d5252c125a4b1c4214f21fa321bd033da10", "size_in_bytes": 1899}, {"_path": "Library/include/openssl/pkcs7.h", "path_type": "hardlink", "sha256": "7e7b9a8af81c2db5c1340c47381a44115d8ab374be13ad8eac9b2d586fb01ad4", "sha256_in_prefix": "7e7b9a8af81c2db5c1340c47381a44115d8ab374be13ad8eac9b2d586fb01ad4", "size_in_bytes": 23089}, {"_path": "Library/include/openssl/pkcs7err.h", "path_type": "hardlink", "sha256": "9fe7a51f3de13b1fd03b319c64b8bd287164eb6ce7d3481994141c0be51396d5", "sha256_in_prefix": "9fe7a51f3de13b1fd03b319c64b8bd287164eb6ce7d3481994141c0be51396d5", "size_in_bytes": 2952}, {"_path": "Library/include/openssl/prov_ssl.h", "path_type": "hardlink", "sha256": "31ded7f804f341c01c2f305187d1cf76daebe4426a1c6b4d2abc2b12d6e2d090", "sha256_in_prefix": "31ded7f804f341c01c2f305187d1cf76daebe4426a1c6b4d2abc2b12d6e2d090", "size_in_bytes": 1139}, {"_path": "Library/include/openssl/proverr.h", "path_type": "hardlink", "sha256": "92aaff8463dbd3f6d0e6c86d981ad8e44ef1683f32ad06b02c268ab37b031352", "sha256_in_prefix": "92aaff8463dbd3f6d0e6c86d981ad8e44ef1683f32ad06b02c268ab37b031352", "size_in_bytes": 9524}, {"_path": "Library/include/openssl/provider.h", "path_type": "hardlink", "sha256": "9ed112fde545a4a6cac897fc05b57e70b8e9de6ed9e75ae7f20847c569bebe74", "sha256_in_prefix": "9ed112fde545a4a6cac897fc05b57e70b8e9de6ed9e75ae7f20847c569bebe74", "size_in_bytes": 3916}, {"_path": "Library/include/openssl/quic.h", "path_type": "hardlink", "sha256": "1ccf7673a2b8396defcc7c6523c31a4e0404596d1dff281049424c78c3b180cf", "sha256_in_prefix": "1ccf7673a2b8396defcc7c6523c31a4e0404596d1dff281049424c78c3b180cf", "size_in_bytes": 2311}, {"_path": "Library/include/openssl/rand.h", "path_type": "hardlink", "sha256": "7fd9cdba02991e6747a193cb5b0d9626b6921c42d4c0332617d53262b8a5db65", "sha256_in_prefix": "7fd9cdba02991e6747a193cb5b0d9626b6921c42d4c0332617d53262b8a5db65", "size_in_bytes": 4181}, {"_path": "Library/include/openssl/randerr.h", "path_type": "hardlink", "sha256": "8a8a64b3e322dfbf5108d457631a2ac2fd61db0274f1f6047a01d15b22afe8a2", "sha256_in_prefix": "8a8a64b3e322dfbf5108d457631a2ac2fd61db0274f1f6047a01d15b22afe8a2", "size_in_bytes": 3381}, {"_path": "Library/include/openssl/rc2.h", "path_type": "hardlink", "sha256": "08c6865d169a300e8bc818bd810f80ffb8a21d69e97dad88e400b586d0f3e965", "sha256_in_prefix": "08c6865d169a300e8bc818bd810f80ffb8a21d69e97dad88e400b586d0f3e965", "size_in_bytes": 2382}, {"_path": "Library/include/openssl/rc4.h", "path_type": "hardlink", "sha256": "ea45836c253246c1d6f1b16b360dbb59322e26e28bfc54881d698e7cd5057666", "sha256_in_prefix": "ea45836c253246c1d6f1b16b360dbb59322e26e28bfc54881d698e7cd5057666", "size_in_bytes": 1194}, {"_path": "Library/include/openssl/rc5.h", "path_type": "hardlink", "sha256": "968c96ead08204edb8148981094700cbc3338ed0613c4469da5ab4675fa1ce29", "sha256_in_prefix": "968c96ead08204edb8148981094700cbc3338ed0613c4469da5ab4675fa1ce29", "size_in_bytes": 2861}, {"_path": "Library/include/openssl/ripemd.h", "path_type": "hardlink", "sha256": "2e28edeb6613516db89e28c9d962301f4fe7b38366ebdd1d35933f3491d57b9d", "sha256_in_prefix": "2e28edeb6613516db89e28c9d962301f4fe7b38366ebdd1d35933f3491d57b9d", "size_in_bytes": 1717}, {"_path": "Library/include/openssl/rsa.h", "path_type": "hardlink", "sha256": "c30eeea9aef005afac36c1ec5565d4069194c088cb7a22930d7ba0ed814d7402", "sha256_in_prefix": "c30eeea9aef005afac36c1ec5565d4069194c088cb7a22930d7ba0ed814d7402", "size_in_bytes": 28478}, {"_path": "Library/include/openssl/rsaerr.h", "path_type": "hardlink", "sha256": "a745e6b2835af7bb933e78870a270d51ab33778fe10a5cd377422d4b9587dcf0", "sha256_in_prefix": "a745e6b2835af7bb933e78870a270d51ab33778fe10a5cd377422d4b9587dcf0", "size_in_bytes": 5681}, {"_path": "Library/include/openssl/safestack.h", "path_type": "hardlink", "sha256": "1089ec732df2ababf7185ecf93660a5a8e2cf6d84eee3097afa514086cde7cb5", "sha256_in_prefix": "1089ec732df2ababf7185ecf93660a5a8e2cf6d84eee3097afa514086cde7cb5", "size_in_bytes": 18736}, {"_path": "Library/include/openssl/seed.h", "path_type": "hardlink", "sha256": "0d6d206f240f7bd6fa28cd4ec66b2b878f199af3ce6eda172af9fe31ebb71586", "sha256_in_prefix": "0d6d206f240f7bd6fa28cd4ec66b2b878f199af3ce6eda172af9fe31ebb71586", "size_in_bytes": 3964}, {"_path": "Library/include/openssl/self_test.h", "path_type": "hardlink", "sha256": "8c49f364dd609f2905305b81f3d390d305792aa958c370561c1ab907df5b306f", "sha256_in_prefix": "8c49f364dd609f2905305b81f3d390d305792aa958c370561c1ab907df5b306f", "size_in_bytes": 5193}, {"_path": "Library/include/openssl/sha.h", "path_type": "hardlink", "sha256": "553407b2787ef08f69396973063de06340097cb7e4c1569265a533b3567e1856", "sha256_in_prefix": "553407b2787ef08f69396973063de06340097cb7e4c1569265a533b3567e1856", "size_in_bytes": 4695}, {"_path": "Library/include/openssl/srp.h", "path_type": "hardlink", "sha256": "8b4982b2f881ef4234279e1fe31634848a64db40d66762c2e396a4f8beafb296", "sha256_in_prefix": "8b4982b2f881ef4234279e1fe31634848a64db40d66762c2e396a4f8beafb296", "size_in_bytes": 15772}, {"_path": "Library/include/openssl/srtp.h", "path_type": "hardlink", "sha256": "20ddd75f9579087b24339e12c14b11939bca462e3cbc2e4b1867773407d6162a", "sha256_in_prefix": "20ddd75f9579087b24339e12c14b11939bca462e3cbc2e4b1867773407d6162a", "size_in_bytes": 2180}, {"_path": "Library/include/openssl/ssl.h", "path_type": "hardlink", "sha256": "0ed5709d7ff65197fd22b0fba12f201bfab1f9e2e4529f91fc2d7542fb7e2a39", "sha256_in_prefix": "0ed5709d7ff65197fd22b0fba12f201bfab1f9e2e4529f91fc2d7542fb7e2a39", "size_in_bytes": 141621}, {"_path": "Library/include/openssl/ssl2.h", "path_type": "hardlink", "sha256": "92e3330e2867bf17d3b305ba0f6fe6b073ad4bdb9db519e4224bbd993f1e9cb7", "sha256_in_prefix": "92e3330e2867bf17d3b305ba0f6fe6b073ad4bdb9db519e4224bbd993f1e9cb7", "size_in_bytes": 658}, {"_path": "Library/include/openssl/ssl3.h", "path_type": "hardlink", "sha256": "6934487804f24a34fedc461a222d85198fd5dfc3280130cfd48db1404aab9366", "sha256_in_prefix": "6934487804f24a34fedc461a222d85198fd5dfc3280130cfd48db1404aab9366", "size_in_bytes": 15224}, {"_path": "Library/include/openssl/sslerr.h", "path_type": "hardlink", "sha256": "409b3e6ca25d2a74313e0d544cca5f13c6601bbc014236d94ffde44d528a3810", "sha256_in_prefix": "409b3e6ca25d2a74313e0d544cca5f13c6601bbc014236d94ffde44d528a3810", "size_in_bytes": 22759}, {"_path": "Library/include/openssl/sslerr_legacy.h", "path_type": "hardlink", "sha256": "4323bb82ce04ab284a35826707dcd4b838109344a1bc12d09e29ba1ed8bfd197", "sha256_in_prefix": "4323bb82ce04ab284a35826707dcd4b838109344a1bc12d09e29ba1ed8bfd197", "size_in_bytes": 26944}, {"_path": "Library/include/openssl/stack.h", "path_type": "hardlink", "sha256": "69f94382a15a3c4cfd1dda32108db5234727b36ed0e25f1fb12e0993c7b5ac95", "sha256_in_prefix": "69f94382a15a3c4cfd1dda32108db5234727b36ed0e25f1fb12e0993c7b5ac95", "size_in_bytes": 3284}, {"_path": "Library/include/openssl/store.h", "path_type": "hardlink", "sha256": "233e1f210c4757fc5e221a0727c938429078bc04e22376528b0fcf3f7307ac9b", "sha256_in_prefix": "233e1f210c4757fc5e221a0727c938429078bc04e22376528b0fcf3f7307ac9b", "size_in_bytes": 15461}, {"_path": "Library/include/openssl/storeerr.h", "path_type": "hardlink", "sha256": "370277e107a1b979ff5e0bd28f5adb92e066d41831ac37ce7108d2a1b84376f6", "sha256_in_prefix": "370277e107a1b979ff5e0bd28f5adb92e066d41831ac37ce7108d2a1b84376f6", "size_in_bytes": 2092}, {"_path": "Library/include/openssl/symhacks.h", "path_type": "hardlink", "sha256": "68b54776fa15943f3f018be6c7dc7a8847c9f512fb5eeec4f093804197dc2dfa", "sha256_in_prefix": "68b54776fa15943f3f018be6c7dc7a8847c9f512fb5eeec4f093804197dc2dfa", "size_in_bytes": 1290}, {"_path": "Library/include/openssl/thread.h", "path_type": "hardlink", "sha256": "9390db912ff47887ff9cfba47b982379dc4a965fb2d085a2f34dc27141c07406", "sha256_in_prefix": "9390db912ff47887ff9cfba47b982379dc4a965fb2d085a2f34dc27141c07406", "size_in_bytes": 871}, {"_path": "Library/include/openssl/tls1.h", "path_type": "hardlink", "sha256": "6ac9841cbc4ee173345e2654a61d1007b9b1d296eb431bc3e9f376bf079d2712", "sha256_in_prefix": "6ac9841cbc4ee173345e2654a61d1007b9b1d296eb431bc3e9f376bf079d2712", "size_in_bytes": 73090}, {"_path": "Library/include/openssl/trace.h", "path_type": "hardlink", "sha256": "afefe0f0495b69abebdc15a8244a8d6a596479622ef69deba6f1b51e4dc1038e", "sha256_in_prefix": "afefe0f0495b69abebdc15a8244a8d6a596479622ef69deba6f1b51e4dc1038e", "size_in_bytes": 10802}, {"_path": "Library/include/openssl/ts.h", "path_type": "hardlink", "sha256": "939496fec1e3d8c592f11ec49bd6d0dd13b0ba190b79b6c566a487196102585c", "sha256_in_prefix": "939496fec1e3d8c592f11ec49bd6d0dd13b0ba190b79b6c566a487196102585c", "size_in_bytes": 20600}, {"_path": "Library/include/openssl/tserr.h", "path_type": "hardlink", "sha256": "0d851cb9db84c48bb8a9871a988950fd0b62ecc854b11641e3e9a07fa191a6f6", "sha256_in_prefix": "0d851cb9db84c48bb8a9871a988950fd0b62ecc854b11641e3e9a07fa191a6f6", "size_in_bytes": 3074}, {"_path": "Library/include/openssl/txt_db.h", "path_type": "hardlink", "sha256": "1a6a6b331ef3cc6c632f782e8da2fa81aaeeac56e4d0b2fb3016f936805be257", "sha256_in_prefix": "1a6a6b331ef3cc6c632f782e8da2fa81aaeeac56e4d0b2fb3016f936805be257", "size_in_bytes": 1784}, {"_path": "Library/include/openssl/types.h", "path_type": "hardlink", "sha256": "74d6844ff863e5697c1af0a37f69727ecb93fe39a3f5ccacc2521e7069068e93", "sha256_in_prefix": "74d6844ff863e5697c1af0a37f69727ecb93fe39a3f5ccacc2521e7069068e93", "size_in_bytes": 7509}, {"_path": "Library/include/openssl/ui.h", "path_type": "hardlink", "sha256": "1ec7da15b464387449827771eb3884b3a0f2a66001703809ba4d519e0ba4636a", "sha256_in_prefix": "1ec7da15b464387449827771eb3884b3a0f2a66001703809ba4d519e0ba4636a", "size_in_bytes": 19658}, {"_path": "Library/include/openssl/uierr.h", "path_type": "hardlink", "sha256": "6f46dc9509b4d10802aaa1ad3c84763a2843312fdc8dd8add5c7b24e7f0c877f", "sha256_in_prefix": "6f46dc9509b4d10802aaa1ad3c84763a2843312fdc8dd8add5c7b24e7f0c877f", "size_in_bytes": 1391}, {"_path": "Library/include/openssl/whrlpool.h", "path_type": "hardlink", "sha256": "bb8f9f6ad1960e87f78363793130a0c1bee89b64a12eb32e939791fb0ca61016", "sha256_in_prefix": "bb8f9f6ad1960e87f78363793130a0c1bee89b64a12eb32e939791fb0ca61016", "size_in_bytes": 1853}, {"_path": "Library/include/openssl/x509.h", "path_type": "hardlink", "sha256": "0dbb8f3e00db586c27cbadd808c3e1fb3869a62b38af80d59396d8ba81b7957b", "sha256_in_prefix": "0dbb8f3e00db586c27cbadd808c3e1fb3869a62b38af80d59396d8ba81b7957b", "size_in_bytes": 74205}, {"_path": "Library/include/openssl/x509_acert.h", "path_type": "hardlink", "sha256": "6774173aaaf7bbd3b7125b27f31c78411d5737b90f69d823b670322da3a8d4c4", "sha256_in_prefix": "6774173aaaf7bbd3b7125b27f31c78411d5737b90f69d823b670322da3a8d4c4", "size_in_bytes": 22717}, {"_path": "Library/include/openssl/x509_vfy.h", "path_type": "hardlink", "sha256": "4b56c8ed4a49527df706915ec6891dec9ca26c40b5423aa1144ca47976f84331", "sha256_in_prefix": "4b56c8ed4a49527df706915ec6891dec9ca26c40b5423aa1144ca47976f84331", "size_in_bytes": 53485}, {"_path": "Library/include/openssl/x509err.h", "path_type": "hardlink", "sha256": "4e9a934927f37a70ee71b8ad7dcb6da3de409b0acc9eafb861a3183ba73578eb", "sha256_in_prefix": "4e9a934927f37a70ee71b8ad7dcb6da3de409b0acc9eafb861a3183ba73578eb", "size_in_bytes": 3381}, {"_path": "Library/include/openssl/x509v3.h", "path_type": "hardlink", "sha256": "51e96a16e2455e0aadd9cdfb07fc32f553e8539dbaabfbec9f5aaa136ab54366", "sha256_in_prefix": "51e96a16e2455e0aadd9cdfb07fc32f553e8539dbaabfbec9f5aaa136ab54366", "size_in_bytes": 133147}, {"_path": "Library/include/openssl/x509v3err.h", "path_type": "hardlink", "sha256": "60e959d315cd8d50eb2fe5991ed3985b665a61b90cb9029693466abf6fa4bba9", "sha256_in_prefix": "60e959d315cd8d50eb2fe5991ed3985b665a61b90cb9029693466abf6fa4bba9", "size_in_bytes": 5067}, {"_path": "Library/lib/cmake/OpenSSL/OpenSSLConfig.cmake", "path_type": "hardlink", "sha256": "3ca02e4703036b201b8edbd5512b186c215582f8f6d99ca3bcb2db1c2030c72a", "sha256_in_prefix": "3ca02e4703036b201b8edbd5512b186c215582f8f6d99ca3bcb2db1c2030c72a", "size_in_bytes": 6683}, {"_path": "Library/lib/cmake/OpenSSL/OpenSSLConfigVersion.cmake", "path_type": "hardlink", "sha256": "15647db7f4508d29eb0a9a13146861f6a2782187047777df1d8a0256fe7eeb75", "sha256_in_prefix": "15647db7f4508d29eb0a9a13146861f6a2782187047777df1d8a0256fe7eeb75", "size_in_bytes": 537}, {"_path": "Library/lib/libcrypto.lib", "path_type": "hardlink", "sha256": "1e0d7fb59e18da73b790e15bc2942da04cc6bb28476583ea3f1f7bf13d9d5e49", "sha256_in_prefix": "1e0d7fb59e18da73b790e15bc2942da04cc6bb28476583ea3f1f7bf13d9d5e49", "size_in_bytes": 1377216}, {"_path": "Library/lib/libssl.lib", "path_type": "hardlink", "sha256": "d7269587e100004cc8ca3aa06737d594eff20bc6d078b31ef3a953aa025cbb65", "sha256_in_prefix": "d7269587e100004cc8ca3aa06737d594eff20bc6d078b31ef3a953aa025cbb65", "size_in_bytes": 147386}, {"_path": "Library/lib/pkgconfig/libcrypto.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "D:/bld/openssl_split_1754464650477/_h_env", "sha256": "b5b748b59ad18d5dea9520f85d7f02dba5bf8902bbef382cb41b929e38ec9af3", "sha256_in_prefix": "4db0cb7f9d86905b5528513a2322a88fdc66e2c538ce205d38355448d7b844ff", "size_in_bytes": 320}, {"_path": "Library/lib/pkgconfig/libssl.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "D:/bld/openssl_split_1754464650477/_h_env", "sha256": "434445697c6492b8da2c61643e8554fbd8b76764d3f1eeac173d79c819c50c64", "sha256_in_prefix": "89aab3a3199b877709ea7fbb4d656ae14b290bb3bc1f0a40090c19a0fe885c6c", "size_in_bytes": 320}, {"_path": "Library/lib/pkgconfig/openssl.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "D:/bld/openssl_split_1754464650477/_h_env", "sha256": "28e4ab0389823792e9b988e7a6d4e424e0bceb2ee48dab7e185f5b40e5b12eb7", "sha256_in_prefix": "ff1b7243442ce5223c6862ba417bb83b1e3da77bf90433bf30858069fa3f4c94", "size_in_bytes": 273}, {"_path": "Library/ssl/certs/.keep", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "etc/conda/activate.d/openssl_activate-win.bat", "path_type": "hardlink", "sha256": "4d8a81a3adaa5530d3887b8564b7dbb417abc6806027c35a1314972f287ac39f", "sha256_in_prefix": "4d8a81a3adaa5530d3887b8564b7dbb417abc6806027c35a1314972f287ac39f", "size_in_bytes": 277}, {"_path": "etc/conda/activate.d/openssl_activate-win.ps1", "path_type": "hardlink", "sha256": "3949fb8c03c1455d40dad37617f10c8d9f9992ce5b59aa8847a6fb51a1c9a7f5", "sha256_in_prefix": "3949fb8c03c1455d40dad37617f10c8d9f9992ce5b59aa8847a6fb51a1c9a7f5", "size_in_bytes": 292}, {"_path": "etc/conda/activate.d/openssl_activate-win.sh", "path_type": "hardlink", "sha256": "5ec78b73cfd2233288a35c464470b62e4a2b08c0dcf8ec3c48969deae24b0e06", "sha256_in_prefix": "5ec78b73cfd2233288a35c464470b62e4a2b08c0dcf8ec3c48969deae24b0e06", "size_in_bytes": 310}, {"_path": "etc/conda/deactivate.d/openssl_deactivate-win.bat", "path_type": "hardlink", "sha256": "c8d3299f7c2495c4591726336d94de91d9b8d25a4d85186b43c6b9bbe3e5b3ee", "sha256_in_prefix": "c8d3299f7c2495c4591726336d94de91d9b8d25a4d85186b43c6b9bbe3e5b3ee", "size_in_bytes": 236}, {"_path": "etc/conda/deactivate.d/openssl_deactivate-win.ps1", "path_type": "hardlink", "sha256": "0c7b217451fade4d5faf97e6ff2e7fe38aa7e173c54d24b4b89933e22211b972", "sha256_in_prefix": "0c7b217451fade4d5faf97e6ff2e7fe38aa7e173c54d24b4b89933e22211b972", "size_in_bytes": 305}, {"_path": "etc/conda/deactivate.d/openssl_deactivate-win.sh", "path_type": "hardlink", "sha256": "ee47af6120be7738e01e715096555d3453134517f67443597c02c2a25d7a2c5a", "sha256_in_prefix": "ee47af6120be7738e01e715096555d3453134517f67443597c02c2a25d7a2c5a", "size_in_bytes": 249}], "paths_version": 1}, "requested_spec": "None", "sha256": "2413f3b4606018aea23acfa2af3c4c46af786739ab4020422e9f0c2aec75321b", "size": 9275175, "subdir": "win-64", "timestamp": 1754467904000, "url": "https://conda.anaconda.org/conda-forge/win-64/openssl-3.5.2-h725018a_0.conda", "version": "3.5.2"}