{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.8,<12.9.0a0", "cuda-opencl 12.8.90 0"], "extracted_package_dir": "", "files": ["Library/include/CL/cl.hpp", "Library/include/CL/cl_d3d10_ext.h", "Library/include/CL/cl_d3d11_ext.h", "Library/include/CL/cl_d3d9_ext.h"], "fn": "cuda-opencl-dev-12.8.90-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "", "type": 1}, "md5": "4bd3d305905d23a5e82a5f1734663f0d", "name": "cuda-opencl-dev", "package_tarball_full_path": "", "paths_data": {"paths": [{"_path": "Library/include/CL/cl.hpp", "path_type": "hardlink", "sha256": "e71c53ef0dac07912d77964a1756069f3ffd7ed488669d9f8b920c1efb1ea8b1", "sha256_in_prefix": "e71c53ef0dac07912d77964a1756069f3ffd7ed488669d9f8b920c1efb1ea8b1", "size_in_bytes": 408372}, {"_path": "Library/include/CL/cl_d3d10_ext.h", "path_type": "hardlink", "sha256": "aa02353d5842eb1651a549823b1faaf797e147537816d9332d0695ee9b2e632e", "sha256_in_prefix": "aa02353d5842eb1651a549823b1faaf797e147537816d9332d0695ee9b2e632e", "size_in_bytes": 4724}, {"_path": "Library/include/CL/cl_d3d11_ext.h", "path_type": "hardlink", "sha256": "616971611bd6a7677da2a624cb888952ce80a228ddcef04f44d87f6cf73bc304", "sha256_in_prefix": "616971611bd6a7677da2a624cb888952ce80a228ddcef04f44d87f6cf73bc304", "size_in_bytes": 4724}, {"_path": "Library/include/CL/cl_d3d9_ext.h", "path_type": "hardlink", "sha256": "e6d72271ebd002dda8598723910c666962d4c561da560d18164f7c9d40f03cae", "sha256_in_prefix": "e6d72271ebd002dda8598723910c666962d4c561da560d18164f7c9d40f03cae", "size_in_bytes": 5635}], "paths_version": 1}, "requested_spec": "None", "sha256": "839698ae6e6b5a0750a089c676001f4d2d5cfda0bbb8e487992c303dfae0ba59", "size": 63134, "subdir": "win-64", "timestamp": 1739447967000, "url": "https://conda.anaconda.org/nvidia/win-64/cuda-opencl-dev-12.8.90-0.conda", "version": "12.8.90"}