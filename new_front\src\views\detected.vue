<template>
  <div class="detected">
    <HeaderComponent title="智能判读" :showBackButton="true" @back="goBack" />
    <!-- 主体三分区布局：左上、左下、右侧 -->
    <div class="content">
      <div class="left">
        <!-- 修改 top-left 区域 -->
        <div class="pane top-left">
          <div class="pane-title">
            输入区域
            <select v-model="inputMode" @change="resetInput">
              <option value="image">Image</option>
              <option value="video">Video</option>
              <option value="files">Files</option>
              <option value="realtime">Realtime</option>
            </select>
            <button @click="handleInputAction">
              {{ inputMode === 'realtime' ? '配置RTSP' : '上传' }}
            </button>
          </div>

          <div class="pane-body">
            <!-- Image 模式 -->
            <div v-if="inputMode === 'image'" class="preview-container">
              <div class="preview-wrapper">
                <img v-if="currentFile.url" :src="currentFile.url" alt="预览图" class="preview-content">
                <div v-else class="upload-placeholder">
                  <p>请上传图片文件</p>
                </div>
              </div>
            </div>

            <!-- Video/Files 模式 -->
            <div v-if="['video', 'files'].includes(inputMode)" class="preview-container">
              <div class="preview-wrapper">
                <video v-if="inputMode === 'video' && currentFile.url" controls :src="currentFile.url"
                  class="preview-content"></video>
                <img v-else-if="inputMode === 'files' && filesList.length > 0" :src="filesList[currentFileIndex].url"
                  alt="文件预览" class="preview-content">
                <div v-else class="upload-placeholder">
                  <p v-if="inputMode === 'video'">请上传视频文件</p>
                  <p v-else>请上传图片文件</p>
                </div>
              </div>
            </div>

            <!-- Realtime 模式 -->
            <div v-if="inputMode === 'realtime'" class="preview-container">
              <div class="preview-wrapper">
                <video v-if="rtspConnected" ref="rtspPlayer" autoplay class="preview-content"></video>
                <div v-else class="upload-placeholder">
                  <p>请配置RTSP流地址</p>
                </div>
              </div>
            </div>
          </div>

          <!-- 底部操作区 -->
          <div class="pane-footer">
            <div v-if="inputMode !== 'realtime'" class="file-info">
              <template v-if="currentFile.name">
                {{ currentFile.name }} ({{ formatFileSize(currentFile.size) }})
              </template>
            </div>
            <div v-else class="file-info">
              {{ rtspUrl || '未配置RTSP地址' }}
            </div>

            <div class="action-buttons">
              <!-- Files 模式特有按钮 -->
              <template v-if="inputMode === 'files' && filesList.length > 0">
                <button @click="prevFile">上一张</button>
                <button @click="nextFile">下一张</button>
                <button @click="deleteCurrent">删除</button>
              </template>

              <!-- 通用按钮 -->
              <button v-if="inputMode === 'realtime'" @click="toggleRtspConnection">
                {{ rtspConnected ? '断开连接' : '连接' }}
              </button>
              <button v-if="hasFile" @click="resetInput">重置</button>
            </div>
          </div>
        </div>
        <!-- 修改 bottom-left 区域 -->
        <div class="pane bottom-left">
          <div class="pane-title">
            处理区域
            <select v-model="preprocessMethod">
              <option value="">无预处理</option>
              <option value="dehaze">去雾</option>
              <option value="light">去光照</option>
              <option value="motion">去运动模糊</option>
              <option value="denoise">去噪</option>
              <option value="superres">超分辨</option>
            </select>

            <select v-model="detectionMethod">
              <option value="detection">检测识别</option>
              <option value="fewshot">小样本</option>
            </select>

            <button @click="processContent">处理</button>
            <button v-if="detectionMethod === 'fewshot'" @click="preTrain">样本预训练</button>
          </div>

          <div class="pane-body">
            <!-- 结果展示区 -->
            <div class="result-container">
              <!-- 普通模式 -->
              <template v-if="inputMode !== 'realtime'">
                <img v-if="processedResult && isImageType" :src="processedResult" alt="处理结果">
                <video v-else-if="processedResult && isVideoType" controls :src="processedResult"></video>
                <div v-else class="result-placeholder">
                  <p>处理结果将显示在此处</p>
                </div>
              </template>

              <!-- 实时模式 -->
              <canvas v-else ref="resultCanvas"></canvas>
            </div>
          </div>

          <!-- 底部操作区 -->
          <div class="pane-footer">
            <button v-if="inputMode !== 'realtime' && processedResult" @click="showDetailDialog" class="detail-btn">
              查看详情
            </button>
          </div>
        </div>
      </div>
      <div class="pane right">
        <!-- 右侧区域内容 -->
        <div class="pane-title">右侧区域</div>
        <div class="pane-body"></div>
      </div>
    </div>
  </div>
</template>

<script>
import HeaderComponent from '@/components/header.vue'
import { APIUploadData } from '@/api/api'
import { serverAddress } from '@/api/config';

export default {
  name: 'DetectedView',
  components: {
    HeaderComponent
  },
  data() {
    return {
      // 输入相关
      inputMode: 'image',
      currentFile: { name: '', size: 0, url: '' },
      filesList: [],
      currentFileIndex: 0,
      rtspUrl: '',
      rtspConnected: false,

      // 处理相关
      preprocessMethod: '',
      detectionMethod: 'detection',
      processedResult: null,

      // 弹窗控制
      detailDialogVisible: false
    }
  },
  computed: {
    hasFile() {
      return this.currentFile.url || this.filesList.length > 0
    },
    isImageType() {
      return this.inputMode === 'image' || this.inputMode === 'files'
    },
    isVideoType() {
      return this.inputMode === 'video'
    }
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    // 添加文件大小格式化方法
    formatFileSize(bytes) {
      if (!bytes) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    // 添加文件操作方法
    prevFile() {
      if (this.currentFileIndex > 0) {
        this.currentFileIndex--;
        this.currentFile = this.filesList[this.currentFileIndex];
      }
    },
    nextFile() {
      if (this.currentFileIndex < this.filesList.length - 1) {
        this.currentFileIndex++;
        this.currentFile = this.filesList[this.currentFileIndex];
      }
    },
    deleteCurrent() {
      if (this.filesList.length > 0) {
        this.filesList.splice(this.currentFileIndex, 1);
        if (this.filesList.length === 0) {
          this.currentFile = {};
          this.currentFileIndex = 0;
        } else {
          if (this.currentFileIndex >= this.filesList.length) {
            this.currentFileIndex = this.filesList.length - 1;
          }
          this.currentFile = this.filesList[this.currentFileIndex];
        }
      }
    },
    // 上传文件处理
    async handleInputAction() {
      if (this.inputMode === 'realtime') {
        this.showRtspDialog();
        return;
      }

      const input = document.createElement('input');
      input.type = 'file';

      // 设置文件类型
      if (this.inputMode === 'image') {
        input.accept = 'image/*';
      } else if (this.inputMode === 'video') {
        input.accept = 'video/*';
      } else if (this.inputMode === 'files') {
        input.accept = 'image/*';
        input.multiple = true;
      }

      input.onchange = async (e) => {
        const files = Array.from(e.target.files);
        if (files.length === 0) return;

        // 重置处理结果
        this.processedResult = null;

        if (this.inputMode === 'files') {
          this.filesList = [];
          for (const file of files) {
            const url = await this.uploadFile(file);
            this.filesList.push({
              name: file.name,
              size: file.size,
              url
            });
          }
          this.currentFileIndex = 0;
          this.currentFile = this.filesList[0] || {};
        } else {
          const file = files[0];
          const url = await this.uploadFile(file);
          this.currentFile = {
            name: file.name,
            size: file.size,
            url
          };
        }
      };

      input.click();
    },

    // 文件上传API
    async uploadFile(file) {
      const formData = new FormData();
      formData.append('file', file);

      try {
        const response = await APIUploadData(formData);

        return serverAddress + response.url; // 后端返回的文件访问路径
      } catch (error) {
        console.error('上传失败:', error);
        return null;
      }
    },

    // 重置当前输入
    resetInput() {
      if (this.inputMode === 'files') {
        this.filesList = [];
        this.currentFileIndex = 0;
      }
      this.currentFile = {};
      this.processedResult = null;

      if (this.inputMode === 'realtime' && this.rtspConnected) {
        this.toggleRtspConnection();
      }
    },

    // RTSP配置弹窗
    showRtspDialog() {
      this.$prompt('请输入RTSP流地址', '配置RTSP', {
        inputValue: this.rtspUrl,
        inputValidator: (value) => {
          if (!value) return '请输入RTSP地址';
          if (!value.startsWith('rtsp://')) return '请输入有效的RTSP地址';
          return true;
        },
        confirmButtonText: '确认连接',
        cancelButtonText: '取消',
        showTestButton: true,
        testButtonText: '测试连接',
        beforeTest: (action, instance, done) => {
          this.testRtspConnection(instance.inputValue)
            .then(success => {
              if (success) {
                this.$message.success('连接测试成功');
              } else {
                this.$message.error('连接测试失败');
              }
              done();
            });
          return false;
        }
      }).then(({ value }) => {
        this.rtspUrl = value;
        this.toggleRtspConnection();
      }).catch(error => {
        // 处理取消操作
        if (error === 'cancel') {
          console.log('用户取消配置');
        }
      });
    },

    // 测试RTSP连接
    async testRtspConnection(url) {
      try {
        const response = await this.$http.post('/rtsp/test', { url });
        return response.data.success;
      } catch (error) {
        return false;
      }
    },

    // 切换RTSP连接状态
    toggleRtspConnection() {
      if (this.rtspConnected) {
        this.stopRtspStream();
      } else if (this.rtspUrl) {
        this.startRtspStream();
      }
      this.rtspConnected = !this.rtspConnected;
    },

    // 开始RTSP流
    async startRtspStream() {
      try {
        // 创建视频元素
        const video = this.$refs.rtspPlayer;

        // 获取WebRTC流或使用MSE
        const streamUrl = await this.getRtspStreamUrl();
        video.src = streamUrl;

        // 开始播放
        await video.play();
      } catch (error) {
        console.error('RTSP连接失败:', error);
        this.rtspConnected = false;
      }
    },

    // 停止RTSP流
    stopRtspStream() {
      const video = this.$refs.rtspPlayer;
      if (video) {
        video.pause();
        video.src = '';
      }
    },

    // 处理内容
    async processContent() {
      if (!this.hasFile && !this.rtspConnected) {
        this.$message.warning('请先选择输入内容');
        return;
      }

      let processData = {};

      if (this.inputMode === 'files') {
        processData = {
          file_urls: this.filesList.map(f => f.url),
          current_index: this.currentFileIndex
        };
      } else if (this.inputMode === 'realtime') {
        processData = { rtsp_url: this.rtspUrl };
      } else {
        processData = { file_url: this.currentFile.url };
      }

      // 添加处理参数
      processData.preprocess = this.preprocessMethod;
      processData.detection = this.detectionMethod;

      try {
        const response = await this.$http.post('/process', processData);
        this.processedResult = response.data.result_url;

        // 实时模式需要特殊处理
        if (this.inputMode === 'realtime') {
          this.startRealTimeProcessing();
        }
      } catch (error) {
        console.error('处理失败:', error);
      }
    },

    // 开始实时处理
    startRealTimeProcessing() {
      const canvas = this.$refs.resultCanvas;
      const ctx = canvas.getContext('2d');

      // 设置Canvas尺寸
      const video = this.$refs.rtspPlayer;
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      // 处理帧的函数
      const processFrame = () => {
        if (!this.rtspConnected) return;

        // 绘制视频帧到Canvas
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

        // 获取图像数据并发送到后端处理
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        this.sendFrameForProcessing(imageData);

        // 循环处理
        requestAnimationFrame(processFrame);
      };

      processFrame();
    },

    // 发送帧数据到后端处理
    async sendFrameForProcessing(imageData) {
      try {
        // 创建Blob发送
        const blob = new Blob([imageData.data], { type: 'application/octet-stream' });
        const formData = new FormData();
        formData.append('frame', blob);
        formData.append('width', imageData.width);
        formData.append('height', imageData.height);

        const response = await this.$http.post('/process/frame', formData);
        const result = response.data;

        // 在Canvas上绘制处理结果
        this.drawProcessingResult(result);
      } catch (error) {
        console.error('帧处理失败:', error);
      }
    },

    // 查看详情弹窗
    showDetailDialog() {
      this.detailDialogVisible = true;
    }
  }

}
</script>

<style scoped>
@import '@/assets/css/variables.css';

.detected {
  overflow: hidden;
  background-size: cover;
  position: relative;
  box-sizing: border-box;
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  background-image: url("../assets/imgs/backimg.png");
  display: flex;
  flex-direction: column;
}

/* 主体区域占满 Header 之外的空间 */
.content {
  flex: 1;
  min-height: 0;
  /* 避免子元素溢出 */
  display: flex;
  gap: 12px;
  padding: 12px;
  box-sizing: border-box;
}

/* 左侧列（包含左上/左下） */
.left {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 50%;
  min-width: 360px;
}

/* 右侧全高区域 */
.right {
  flex: 1;
}

/* 面板基础样式 */
.pane {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(6px);
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.top-left,
.bottom-left {
  flex: 1;
  /* 左上与左下均分高度 */
}

.pane-title {
  font-weight: 600;
  padding: 10px 12px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  color: #333;
  display: flex;
  align-items: center;
  gap: 10px
}

.pane-body {
  flex: 1;
  min-height: 0;
  padding: 12px;
  overflow: auto;
}

.preview-container,
.result-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.preview-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.preview-content {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  display: block;
}

.upload-placeholder,
.result-placeholder {
  border: 2px dashed #ccc;
  border-radius: 8px;
  width: 90%;
  height: 80%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #666;
}

.pane-footer {
  display: flex;
  justify-content: space-between;
  padding: 10px 12px;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.file-info {
  font-size: 0.9rem;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 60%;
}

.detail-btn {
  margin-left: auto;
}

canvas {
  width: 100%;
  height: 100%;
  background: #00000000;
}

/* 响应式：窄屏下改为上下排列 */
@media (max-width: 1200px) {
  .content {
    flex-direction: column;
  }

  .left,
  .right {
    width: 100%;
    min-width: 0;
  }
}
</style>
