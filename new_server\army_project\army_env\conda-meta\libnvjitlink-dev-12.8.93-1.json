{"build": "1", "build_number": 1, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": ["libnvjitlink-static >=12.8.93"], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.8,<12.9.0a0", "libnvjitlink 12.8.93 1"], "extracted_package_dir": "", "files": ["Library/include/nvJitLink.h", "Library/lib/nvJitLink.lib"], "fn": "libnvjitlink-dev-12.8.93-1.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "", "type": 1}, "md5": "3bc6e4bcf34931025b3df44800330ea2", "name": "libnvjitlink-dev", "package_tarball_full_path": "", "paths_data": {"paths": [{"_path": "Library/include/nvJitLink.h", "path_type": "hardlink", "sha256": "13b857231e8d552ad518414bde841e4dfe15a64d88c01ae1ad6a392aa46a34c5", "sha256_in_prefix": "13b857231e8d552ad518414bde841e4dfe15a64d88c01ae1ad6a392aa46a34c5", "size_in_bytes": 17612}, {"_path": "Library/lib/nvJitLink.lib", "path_type": "hardlink", "sha256": "1f25e3c12c8cc70e686886fc176eac415f4358e308e6c0d844431dd8c322224f", "sha256_in_prefix": "1f25e3c12c8cc70e686886fc176eac415f4358e308e6c0d844431dd8c322224f", "size_in_bytes": 36684}], "paths_version": 1}, "requested_spec": "None", "sha256": "8f97d75df032c47a36e8cb604199bc6243793e55c2ee815e279879b3ad194e72", "size": 24617, "subdir": "win-64", "timestamp": 1740205349000, "url": "https://conda.anaconda.org/nvidia/win-64/libnvjitlink-dev-12.8.93-1.conda", "version": "12.8.93"}