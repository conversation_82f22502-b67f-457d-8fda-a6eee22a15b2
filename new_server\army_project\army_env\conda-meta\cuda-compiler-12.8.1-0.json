{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/noarch", "constrains": [], "depends": ["__win", "cuda-cuobjdump 12.8.90.*", "cuda-cuxxfilt 12.8.90.*", "cuda-nvcc 12.8.93.*", "cuda-nvprune 12.8.90.*"], "extracted_package_dir": "", "files": [], "fn": "cuda-compiler-12.8.1-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "", "type": 1}, "md5": "696a42563cb1f8f41840d176a0a07324", "name": "cuda-compiler", "noarch": "generic", "package_tarball_full_path": "", "package_type": "noarch_generic", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "050ebffa305212e926acdc99d26b34f2145cc7c68ff31b31e906cc280ca47de1", "size": 17178, "subdir": "noarch", "timestamp": 1741063676000, "url": "https://conda.anaconda.org/nvidia/noarch/cuda-compiler-12.8.1-0.conda", "version": "12.8.1"}