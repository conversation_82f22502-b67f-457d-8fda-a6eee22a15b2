{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": ["cuda-cudart 12.8.90.*", "cuda-nvrtc 12.8.93.*", "cuda-opencl 12.8.90.*", "libcublas 12.8.4.1.*", "libcufft 11.3.3.83.*", "libcurand 10.3.9.90.*", "libcusolver 11.7.3.90.*", "libcusparse 12.5.8.93.*", "libnpp 12.3.3.100.*", "libnvfatbin 12.8.90.*", "libnvjitlink 12.8.93.*", "libnvjpeg 12.3.5.92.*"], "extracted_package_dir": "", "files": [], "fn": "cuda-libraries-12.8.1-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "", "type": 1}, "md5": "c43f637e934c57686e02259ce4e3c357", "name": "cuda-libraries", "package_tarball_full_path": "", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "c64a0e4ed88eb7e25137d9ea568b4b450317d884d7dcf413f19184036e7f91f1", "size": 17106, "subdir": "win-64", "timestamp": 1741063700000, "url": "https://conda.anaconda.org/nvidia/win-64/cuda-libraries-12.8.1-0.conda", "version": "12.8.1"}