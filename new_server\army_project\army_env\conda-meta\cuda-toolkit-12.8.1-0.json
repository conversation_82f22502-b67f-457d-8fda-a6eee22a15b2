{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/noarch", "constrains": [], "depends": ["__win", "cuda-compiler 12.8.1.*", "cuda-libraries 12.8.1.*", "cuda-libraries-dev 12.8.1.*", "cuda-nvml-dev 12.8.90.*", "cuda-tools 12.8.1.*"], "extracted_package_dir": "", "files": [], "fn": "cuda-toolkit-12.8.1-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "", "type": 1}, "md5": "08a685c91241fa0ed84b7b2c94763d9d", "name": "cuda-toolkit", "noarch": "generic", "package_tarball_full_path": "", "package_type": "noarch_generic", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "60f9d6c9aa9a6f47f0e083b14465f6431bd364e03c6ab801cea4f885299d17e0", "size": 17111, "subdir": "noarch", "timestamp": 1741063869000, "url": "https://conda.anaconda.org/nvidia/noarch/cuda-toolkit-12.8.1-0.conda", "version": "12.8.1"}