<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="20">
            <item index="0" class="java.lang.String" itemvalue="opencv-contrib-python" />
            <item index="1" class="java.lang.String" itemvalue="numpy" />
            <item index="2" class="java.lang.String" itemvalue="onnxruntime" />
            <item index="3" class="java.lang.String" itemvalue="transformers" />
            <item index="4" class="java.lang.String" itemvalue="torch" />
            <item index="5" class="java.lang.String" itemvalue="torchvision" />
            <item index="6" class="java.lang.String" itemvalue="onnx" />
            <item index="7" class="java.lang.String" itemvalue="pycocotools" />
            <item index="8" class="java.lang.String" itemvalue="albumentations" />
            <item index="9" class="java.lang.String" itemvalue="onnxruntime-gpu" />
            <item index="10" class="java.lang.String" itemvalue="scipy" />
            <item index="11" class="java.lang.String" itemvalue="timm" />
            <item index="12" class="java.lang.String" itemvalue="onnxslim" />
            <item index="13" class="java.lang.String" itemvalue="gradio" />
            <item index="14" class="java.lang.String" itemvalue="opencv-python" />
            <item index="15" class="java.lang.String" itemvalue="huggingface-hub" />
            <item index="16" class="java.lang.String" itemvalue="PyYAML" />
            <item index="17" class="java.lang.String" itemvalue="safetensors" />
            <item index="18" class="java.lang.String" itemvalue="py-cpuinfo" />
            <item index="19" class="java.lang.String" itemvalue="psutil" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>