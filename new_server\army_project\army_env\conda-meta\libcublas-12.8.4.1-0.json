{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": ["cuda-nvrtc", "vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.8,<12.9.0a0"], "extracted_package_dir": "", "files": ["Library/bin/cublas64_12.dll", "Library/bin/cublasLt64_12.dll", "Library/bin/nvblas64_12.dll"], "fn": "libcublas-12.8.4.1-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "", "type": 1}, "md5": "869ad962c0de81f3d4d8ef6e31fa3c94", "name": "libcublas", "package_tarball_full_path": "", "paths_data": {"paths": [{"_path": "Library/bin/cublas64_12.dll", "path_type": "hardlink", "sha256": "9513540e4ec4c51ee9e7304138c2cc255c29a8c181f9e80c38efa25738becd99", "sha256_in_prefix": "9513540e4ec4c51ee9e7304138c2cc255c29a8c181f9e80c38efa25738becd99", "size_in_bytes": 113716224}, {"_path": "Library/bin/cublasLt64_12.dll", "path_type": "hardlink", "sha256": "b199d1ff892a81b7fd3d57ba1781549609b41500b36008fef326038393ad46c7", "sha256_in_prefix": "b199d1ff892a81b7fd3d57ba1781549609b41500b36008fef326038393ad46c7", "size_in_bytes": 674667520}, {"_path": "Library/bin/nvblas64_12.dll", "path_type": "hardlink", "sha256": "99f3fae7b729417a3d2778e746f51258cd179c8df0c654d68fcafdad23d34779", "sha256_in_prefix": "99f3fae7b729417a3d2778e746f51258cd179c8df0c654d68fcafdad23d34779", "size_in_bytes": 335360}], "paths_version": 1}, "requested_spec": "None", "sha256": "ffe77109144127e0d9caa6eb3fc6d017b87c271d9b03564e0d985c571c493c7e", "size": 464909661, "subdir": "win-64", "timestamp": 1740561563000, "url": "https://conda.anaconda.org/nvidia/win-64/libcublas-12.8.4.1-0.conda", "version": "12.8.4.1"}