{"ast": null, "code": "import { get, post, upload } from './request';\nimport { serverAddress } from '@/api/config';\n// 样本库接口\n\n// 取得类别树，读取后台类别树json文件，并获取对应类别的样本数量\nexport const APIGetCategoryTree = () => get(serverAddress + 'api/exp/getCategoryTree');\n\n// 保存类别树，将确定的类别树传到后端保存到json文件中\nexport const APISaveCategoryTree = params => post(serverAddress + 'api/exp/saveCategoryTree', params);\n\n// 获取样本列表,传递层级目录，分页返回样本列表（文件名、路径、上传时间、id），创建不同类别对应的数据库\nexport const APIGetSamplesListByName = params => get(serverAddress + 'api/exp/getArmExpsByName', params);\n\n// 删除样本，传递id，在数据库获取路径，删除文件\nexport const APIDelSamples = params => post(serverAddress + 'api/exp/delArmExpsByName', params);\n\n// 批量删除，传递id数组，在数据库遍历查询，删除文件\nexport const APIDelSamplesBatch = params => post(serverAddress + 'api/exp/delArmExpsBatch', params);\n\n// 查看样本详情，传递id，在数据库获取路径，读取文件，调用labelimg\nexport const APIGetTXTContentByName = param => get(serverAddress + 'api/exp/getArmTXTByName', param);\n\n// 添加样本，传递层级目录，传递文件数据，保存到对应目录，更新数据库\nexport const APIAddSamples = formData => upload(serverAddress + 'api/exp/addArmExps', formData);\n\n// 重命名样本，传递id和新名称，更新数据库\nexport const APIRenameSample = params => post(serverAddress + 'api/exp/renameArmExp', params);\n\n// 查看样本，传递id，在数据库获取路径，读取标注文件，并返回前端\nexport const APIViewSample = param => get(serverAddress + 'api/exp/viewArmExp', param);\n\n// 搜索样本，传递搜索词，当前分页数，在数据库中模糊查询，返回样本列表，\nexport const APISearchSamples = params => get(serverAddress + 'api/exp/searchArmExps', params);\n\n// 检测识别接口\n// 统一的上传接口,传入的参数有：文件类型，文件数据，选择完对应的文件后，将文件上传到后端，后端将文件保存到对应的目录下\nexport const APIUploadData = formData => upload(serverAddress + 'api/detected/upload', formData);\n\n// 预处理，将数据输入框的文件相对url传到后端，后端根据url查找指定文件，调用预处理库，返回处理后的文件url\nexport const APIPreprocessData = params => post(serverAddress + 'api/detected/preprocess', params);\n\n// 识别，将当前要检测的数据类别发送到后端（image, video, folder）查看我的workspace中的process目录下的文件如果是image就将image灰度化，然后将路径返回前端，并且需要将结果在前端的检测结果中展示（可以模拟数据），返回识别结果，以及对应的标框信息，让我能够在前端显示\nexport const APIDetectData = params => post(serverAddress + 'api/detected/detect', params);\n\n// 修正，\nexport const APICorrectData = params => post(serverAddress + 'api/detected/correct', params);\n\n// RTSP流创建接口\nexport const APICreateRTSPStream = params => post(serverAddress + 'api/detected/rtsp/stream', params);\n\n// 帧处理接口\nexport const APIProcessFrame = formData => upload(serverAddress + 'api/detected/process/frame', formData);\n\n// 停止RTSP流接口\nexport const APIStopRTSPStream = params => post(serverAddress + 'api/detected/rtsp/stop', params);", "map": {"version": 3, "names": ["get", "post", "upload", "serverAddress", "APIGetCategoryTree", "APISaveCategoryTree", "params", "APIGetSamplesListByName", "APIDelSamples", "APIDelSamplesBatch", "APIGetTXTContentByName", "param", "APIAddSamples", "formData", "APIRenameSample", "APIViewSample", "APISearchSamples", "APIUploadData", "APIPreprocessData", "APIDetectData", "APICorrectData", "APICreateRTSPStream", "APIProcessFrame", "APIStopRTSPStream"], "sources": ["D:/Projs/lujun/new_proj/new_proj/new_front/src/api/api.js"], "sourcesContent": ["import { get, post, upload } from './request';\r\nimport { serverAddress } from '@/api/config';\r\n// 样本库接口\r\n\r\n// 取得类别树，读取后台类别树json文件，并获取对应类别的样本数量\r\nexport const APIGetCategoryTree = () => get(serverAddress +'api/exp/getCategoryTree');\r\n\r\n// 保存类别树，将确定的类别树传到后端保存到json文件中\r\nexport const APISaveCategoryTree = (params) => post(serverAddress +'api/exp/saveCategoryTree', params);\r\n\r\n// 获取样本列表,传递层级目录，分页返回样本列表（文件名、路径、上传时间、id），创建不同类别对应的数据库\r\nexport const APIGetSamplesListByName = (params) => get(serverAddress +'api/exp/getArmExpsByName', params);\r\n\r\n// 删除样本，传递id，在数据库获取路径，删除文件\r\nexport const APIDelSamples = (params) => post(serverAddress +'api/exp/delArmExpsByName', params);\r\n\r\n// 批量删除，传递id数组，在数据库遍历查询，删除文件\r\nexport const APIDelSamplesBatch = (params) => post(serverAddress +'api/exp/delArmExpsBatch', params);\r\n\r\n// 查看样本详情，传递id，在数据库获取路径，读取文件，调用labelimg\r\nexport const APIGetTXTContentByName = (param) => get(serverAddress +'api/exp/getArmTXTByName', param);\r\n\r\n// 添加样本，传递层级目录，传递文件数据，保存到对应目录，更新数据库\r\nexport const APIAddSamples = (formData) => upload(serverAddress +'api/exp/addArmExps', formData);\r\n\r\n// 重命名样本，传递id和新名称，更新数据库\r\nexport const APIRenameSample = (params) => post(serverAddress +'api/exp/renameArmExp', params);\r\n\r\n// 查看样本，传递id，在数据库获取路径，读取标注文件，并返回前端\r\nexport const APIViewSample = (param) => get(serverAddress +'api/exp/viewArmExp', param);\r\n\r\n// 搜索样本，传递搜索词，当前分页数，在数据库中模糊查询，返回样本列表，\r\nexport const APISearchSamples = (params) => get(serverAddress +'api/exp/searchArmExps', params);\r\n\r\n// 检测识别接口\r\n// 统一的上传接口,传入的参数有：文件类型，文件数据，选择完对应的文件后，将文件上传到后端，后端将文件保存到对应的目录下\r\nexport const APIUploadData = (formData) => upload(serverAddress +'api/detected/upload', formData);\r\n\r\n// 预处理，将数据输入框的文件相对url传到后端，后端根据url查找指定文件，调用预处理库，返回处理后的文件url\r\nexport const APIPreprocessData = (params) => post(serverAddress +'api/detected/preprocess', params);\r\n\r\n// 识别，将当前要检测的数据类别发送到后端（image, video, folder）查看我的workspace中的process目录下的文件如果是image就将image灰度化，然后将路径返回前端，并且需要将结果在前端的检测结果中展示（可以模拟数据），返回识别结果，以及对应的标框信息，让我能够在前端显示\r\nexport const APIDetectData = (params) => post(serverAddress +'api/detected/detect', params);\r\n\r\n// 修正，\r\nexport const APICorrectData = (params) => post(serverAddress +'api/detected/correct', params);\r\n\r\n// RTSP流创建接口\r\nexport const APICreateRTSPStream = (params) => post(serverAddress + 'api/detected/rtsp/stream', params);\r\n\r\n// 帧处理接口\r\nexport const APIProcessFrame = (formData) => upload(serverAddress + 'api/detected/process/frame', formData);\r\n\r\n// 停止RTSP流接口\r\nexport const APIStopRTSPStream = (params) => post(serverAddress + 'api/detected/rtsp/stop', params);"], "mappings": "AAAA,SAASA,GAAG,EAAEC,IAAI,EAAEC,MAAM,QAAQ,WAAW;AAC7C,SAASC,aAAa,QAAQ,cAAc;AAC5C;;AAEA;AACA,OAAO,MAAMC,kBAAkB,GAAGA,CAAA,KAAMJ,GAAG,CAACG,aAAa,GAAE,yBAAyB,CAAC;;AAErF;AACA,OAAO,MAAME,mBAAmB,GAAIC,MAAM,IAAKL,IAAI,CAACE,aAAa,GAAE,0BAA0B,EAAEG,MAAM,CAAC;;AAEtG;AACA,OAAO,MAAMC,uBAAuB,GAAID,MAAM,IAAKN,GAAG,CAACG,aAAa,GAAE,0BAA0B,EAAEG,MAAM,CAAC;;AAEzG;AACA,OAAO,MAAME,aAAa,GAAIF,MAAM,IAAKL,IAAI,CAACE,aAAa,GAAE,0BAA0B,EAAEG,MAAM,CAAC;;AAEhG;AACA,OAAO,MAAMG,kBAAkB,GAAIH,MAAM,IAAKL,IAAI,CAACE,aAAa,GAAE,yBAAyB,EAAEG,MAAM,CAAC;;AAEpG;AACA,OAAO,MAAMI,sBAAsB,GAAIC,KAAK,IAAKX,GAAG,CAACG,aAAa,GAAE,yBAAyB,EAAEQ,KAAK,CAAC;;AAErG;AACA,OAAO,MAAMC,aAAa,GAAIC,QAAQ,IAAKX,MAAM,CAACC,aAAa,GAAE,oBAAoB,EAAEU,QAAQ,CAAC;;AAEhG;AACA,OAAO,MAAMC,eAAe,GAAIR,MAAM,IAAKL,IAAI,CAACE,aAAa,GAAE,sBAAsB,EAAEG,MAAM,CAAC;;AAE9F;AACA,OAAO,MAAMS,aAAa,GAAIJ,KAAK,IAAKX,GAAG,CAACG,aAAa,GAAE,oBAAoB,EAAEQ,KAAK,CAAC;;AAEvF;AACA,OAAO,MAAMK,gBAAgB,GAAIV,MAAM,IAAKN,GAAG,CAACG,aAAa,GAAE,uBAAuB,EAAEG,MAAM,CAAC;;AAE/F;AACA;AACA,OAAO,MAAMW,aAAa,GAAIJ,QAAQ,IAAKX,MAAM,CAACC,aAAa,GAAE,qBAAqB,EAAEU,QAAQ,CAAC;;AAEjG;AACA,OAAO,MAAMK,iBAAiB,GAAIZ,MAAM,IAAKL,IAAI,CAACE,aAAa,GAAE,yBAAyB,EAAEG,MAAM,CAAC;;AAEnG;AACA,OAAO,MAAMa,aAAa,GAAIb,MAAM,IAAKL,IAAI,CAACE,aAAa,GAAE,qBAAqB,EAAEG,MAAM,CAAC;;AAE3F;AACA,OAAO,MAAMc,cAAc,GAAId,MAAM,IAAKL,IAAI,CAACE,aAAa,GAAE,sBAAsB,EAAEG,MAAM,CAAC;;AAE7F;AACA,OAAO,MAAMe,mBAAmB,GAAIf,MAAM,IAAKL,IAAI,CAACE,aAAa,GAAG,0BAA0B,EAAEG,MAAM,CAAC;;AAEvG;AACA,OAAO,MAAMgB,eAAe,GAAIT,QAAQ,IAAKX,MAAM,CAACC,aAAa,GAAG,4BAA4B,EAAEU,QAAQ,CAAC;;AAE3G;AACA,OAAO,MAAMU,iBAAiB,GAAIjB,MAAM,IAAKL,IAAI,CAACE,aAAa,GAAG,wBAAwB,EAAEG,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}