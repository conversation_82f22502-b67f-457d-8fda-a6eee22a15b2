from __future__ import annotations

import pathlib, sys

ROOT = pathlib.Path(__file__).resolve().parent
sys.path.insert(0, str(ROOT))
WORK = ROOT.parent
import cv2
from .ToolBox import ToolBox
from detector.Detector_124 import Detector_YOLO, Detector_Small
from detector.RTDETR.Detector_RTDETR import Detector_RTDE<PERSON>
from detector.Detector_YOLOE import run_this

from fastapi import APIRouter, File, Form, HTTPException, UploadFile, status
from fastapi.responses import JSONResponse, FileResponse
from pydantic import BaseModel, Field
import os
import uuid
import numpy as np
import time
import shutil
from typing import List, Dict, Any

router = APIRouter(prefix="/api/detected", tags=["detected"])

# 创建工作区目录结构
WORKSPACE_DIR = WORK / "workspace"
WORKSPACE_DIR.mkdir(parents=True, exist_ok=True)

# 确保上传目录存在
UPLOAD_DIR = WORKSPACE_DIR / "uploads"
UPLOAD_DIR.mkdir(parents=True, exist_ok=True)

# 创建预处理和检测目录
PREPROCESSED_DIR = WORKSPACE_DIR / "preprocessed"
PREPROCESSED_DIR.mkdir(exist_ok=True)

DETECTED_DIR = WORKSPACE_DIR / "detected"
DETECTED_DIR.mkdir(exist_ok=True)

# 创建预处理子目录
PREPROCESS_SUBDIRS = {
    "dehaze": "去雾",
    "light": "去光照",
    "motion": "去运动模糊",
    "denoise": "去噪",
    "superres": "超分辨"
}

for subdir in PREPROCESS_SUBDIRS.values():
    (PREPROCESSED_DIR / subdir).mkdir(exist_ok=True)

# 创建检测子目录
DETECTION_SUBDIRS = {
    "detection": "检测识别",
    "fewshot": "小样本"
}

for subdir in DETECTION_SUBDIRS.values():
    (DETECTED_DIR / subdir).mkdir(exist_ok=True)

# 预处理方法映射
PREPROCESS_METHODS = {
    "dehaze": "去雾处理",
    "light": "去光照处理",
    "motion": "去运动模糊",
    "denoise": "去噪处理",
    "superres": "超分辨率"
}

# 检测方法映射
DETECTION_METHODS = {
    "detection": "常规检测",
    "fewshot": "小样本检测"
}


# 文件上传端点
@router.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    # 生成唯一文件名防止冲突
    file_ext = os.path.splitext(file.filename)[1]
    unique_filename = f"{uuid.uuid4().hex}{file_ext}"
    file_path = UPLOAD_DIR / unique_filename

    # 保存文件
    with open(file_path, "wb") as f:
        content = await file.read()
        f.write(content)

    # 返回可访问的URL
    return {"url": f"workspace/uploads/{unique_filename}"}


# 处理请求模型
class ProcessRequest(BaseModel):
    file_url: str = Field(None, description="单文件URL")
    file_urls: List[str] = Field(None, description="多文件URL列表")
    current_index: int = Field(0, description="当前文件索引")
    rtsp_url: str = Field(None, description="RTSP流地址")
    preprocess: str = Field("", description="预处理方法")
    detection: str = Field("detection", description="检测方法")


# 处理端点
@router.post("/process")
async def process_content(request: ProcessRequest):
    # 根据输入模式和处理参数执行处理
    if request.rtsp_url:
        # 实时视频流处理
        return handle_realtime(request)
    elif request.file_urls:
        # 多文件处理
        return handle_files(request)
    else:
        # 单文件处理
        return handle_single(request)


# 处理单文件
def handle_single(request: ProcessRequest):
    file_path = ROOT / request.file_url.lstrip("/")

    if not file_path.exists():
        raise HTTPException(status_code=404, detail="文件不存在")

    # 根据文件类型执行处理
    if file_path.suffix.lower() in [".jpg", ".jpeg", ".png"]:
        return handle_image(file_path, request)
    elif file_path.suffix.lower() in [".mp4", ".avi", ".mov"]:
        return handle_video(file_path, request)
    else:
        raise HTTPException(status_code=400, detail="不支持的文件类型")


# 处理多文件
def handle_files(request: ProcessRequest):
    if not request.file_urls:
        raise HTTPException(status_code=400, detail="文件列表为空")

    if request.current_index >= len(request.file_urls) or request.current_index < 0:
        raise HTTPException(status_code=400, detail="无效的文件索引")

    file_url = request.file_urls[request.current_index]
    request.file_url = file_url
    return handle_single(request)


# 处理实时视频流
def handle_realtime(request: ProcessRequest):
    # 在实际应用中，这里会启动RTSP流处理
    # 返回模拟结果用于测试
    return {
        "result_url": None,
        "message": f"正在处理RTSP流: {request.rtsp_url}",
        "status": "processing"
    }


# 处理图片
def handle_image(file_path: pathlib.Path, request: ProcessRequest):
    # 模拟处理过程
    process_steps = []

    # 确定保存目录
    save_dir = WORKSPACE_DIR

    # 如果有预处理，保存到预处理目录
    if request.preprocess and request.preprocess in PREPROCESS_SUBDIRS:
        save_dir = PREPROCESSED_DIR / PREPROCESS_SUBDIRS[request.preprocess]
        process_steps.append(PREPROCESS_METHODS.get(request.preprocess, "预处理"))

    # 如果有检测，保存到检测目录
    if request.detection and request.detection in DETECTION_SUBDIRS:
        # 如果已经有预处理目录，优先保存到检测目录
        if save_dir == WORKSPACE_DIR:
            save_dir = DETECTED_DIR / DETECTION_SUBDIRS[request.detection]
        process_steps.append(DETECTION_METHODS.get(request.detection, "检测"))

    # 确保目录存在
    save_dir.mkdir(parents=True, exist_ok=True)

    # 生成结果文件名
    result_filename = f"processed_{file_path.stem}_{int(time.time())}{file_path.suffix}"
    result_path = save_dir / result_filename

    # 在实际应用中，这里会执行实际的图像处理
    # 现在只是复制文件作为模拟
    shutil.copy(file_path, result_path)

    # 返回结果URL，注意URL路径要能访问到文件
    relative_path = result_path.relative_to(ROOT)
    result_url = f"/{relative_path}"

    return {
        "result_url": result_url,
        "process_steps": process_steps,
        "save_path": str(result_path)
    }


# 处理视频
def handle_video(file_path: pathlib.Path, request: ProcessRequest):
    # 模拟处理过程
    process_steps = []

    # 确定保存目录
    save_dir = WORKSPACE_DIR

    # 如果有预处理，保存到预处理目录
    if request.preprocess and request.preprocess in PREPROCESS_SUBDIRS:
        save_dir = PREPROCESSED_DIR / PREPROCESS_SUBDIRS[request.preprocess]
        process_steps.append(PREPROCESS_METHODS.get(request.preprocess, "预处理"))

    # 如果有检测，保存到检测目录
    if request.detection and request.detection in DETECTION_SUBDIRS:
        # 如果已经有预处理目录，优先保存到检测目录
        if save_dir == WORKSPACE_DIR:
            save_dir = DETECTED_DIR / DETECTION_SUBDIRS[request.detection]
        process_steps.append(DETECTION_METHODS.get(request.detection, "检测"))

    # 确保目录存在
    save_dir.mkdir(parents=True, exist_ok=True)

    # 生成结果文件名
    result_filename = f"processed_{file_path.stem}_{int(time.time())}{file_path.suffix}"
    result_path = save_dir / result_filename

    # 在实际应用中，这里会执行实际的视频处理
    # 现在只是复制文件作为模拟
    shutil.copy(file_path, result_path)

    # 返回结果URL
    relative_path = result_path.relative_to(ROOT)
    result_url = f"/{relative_path}"

    return {
        "result_url": result_url,
        "process_steps": process_steps,
        "save_path": str(result_path)
    }


# RTSP测试端点
def verify_rtsp_stream(url: str) -> bool:
    """验证RTSP流是否可访问"""
    cap = cv2.VideoCapture(url)
    if not cap.isOpened():
        return False

    # 尝试读取一帧
    ret, _ = cap.read()
    cap.release()
    return ret


@router.post("/rtsp/test")
async def test_rtsp(rtsp: dict):
    url = rtsp.get("url")
    if not url:
        return {"success": False, "message": "URL不能为空"}

    # 验证RTSP流是否可访问
    success = verify_rtsp_stream(url)
    return {"success": success}


# 帧处理请求模型
class FrameProcessRequest(BaseModel):
    width: int
    height: int


# 帧处理端点
@router.post("/process/frame")
async def process_frame(
        frame: bytes = File(...),
        width: int = Form(...),
        height: int = Form(...)
):
    """处理视频帧"""
    try:
        # 将字节数据转换为numpy数组
        frame_data = np.frombuffer(frame, dtype=np.uint8)
        frame_array = frame_data.reshape((height, width, 4))  # RGBA格式

        # 在实际应用中，这里会执行帧处理
        # 现在只是返回一个模拟的处理结果
        processed_frame = frame_array.copy()

        # 添加处理标记
        cv2.putText(
            processed_frame,
            "Processed Frame",
            (50, 50),
            cv2.FONT_HERSHEY_SIMPLEX,
            1,
            (0, 255, 0),
            2
        )

        # 转换回字节数据
        _, encoded_frame = cv2.imencode(".png", processed_frame)
        return encoded_frame.tobytes()

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"帧处理错误: {str(e)}"
        )