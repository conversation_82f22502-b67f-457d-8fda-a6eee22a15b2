<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="127a1c25-99a0-434b-b10a-d0dc75521bfb" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/MotionDeblur/Figure/Intra_Inter.PNG" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MotionDeblur/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MotionDeblur/aug.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MotionDeblur/dataset.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MotionDeblur/datasets/datasets.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MotionDeblur/evaluate_RealBlur_J.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MotionDeblur/evaluate_RealBlur_R.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MotionDeblur/evaluation_GoPro.m" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MotionDeblur/evaluation_HIDE.m" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MotionDeblur/license" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MotionDeblur/metric_counter.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MotionDeblur/out/Results.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MotionDeblur/predict_GoPro_test_results.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MotionDeblur/predict_HIDE_results.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MotionDeblur/predict_RealBlur_J_test_results.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MotionDeblur/predict_RealBlur_R_test_results.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MotionDeblur/schedulers.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MotionDeblur/train_Stripformer_gopro.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MotionDeblur/train_Stripformer_pretrained.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MotionDeblur/util/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MotionDeblur/util/html.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MotionDeblur/util/image_pool.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MotionDeblur/util/metrics.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MotionDeblur/util/util.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/MotionDeblur/util/visualizer.py" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/MotionDeblur" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/demo_for_preprocessing.py" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2zXsZPXPt8iFQN1eB8uIihFwdzG" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;Python tests.Python tests in test.py.executor&quot;: &quot;Run&quot;,
    &quot;Python.Dehaze.executor&quot;: &quot;Run&quot;,
    &quot;Python.Detector_YOLOE.executor&quot;: &quot;Run&quot;,
    &quot;Python.LowlightEnhancer.executor&quot;: &quot;Run&quot;,
    &quot;Python.dehazeIR_arch.executor&quot;: &quot;Run&quot;,
    &quot;Python.demo.executor&quot;: &quot;Run&quot;,
    &quot;Python.demo_for_detection.executor&quot;: &quot;Run&quot;,
    &quot;Python.demo_for_preprocessing.executor&quot;: &quot;Run&quot;,
    &quot;Python.train_yolo.executor&quot;: &quot;Run&quot;,
    &quot;Python.train_yolov8.executor&quot;: &quot;Run&quot;,
    &quot;Python.upsize.executor&quot;: &quot;Run&quot;,
    &quot;Python.yolo_test.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager.252&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;/home/<USER>/Wolf/army_project&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;com.jetbrains.python.configuration.PyActiveSdkModuleConfigurable&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$" />
      <recent name="$PROJECT_DIR$/detector/weights" />
      <recent name="$PROJECT_DIR$/model" />
      <recent name="$PROJECT_DIR$/detector" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/detector" />
      <recent name="$PROJECT_DIR$/detector/YOLOE" />
      <recent name="$PROJECT_DIR$/detector/weights" />
      <recent name="$PROJECT_DIR$/IPC-Dehaze" />
    </key>
  </component>
  <component name="RunManager" selected="Python.demo_for_preprocessing">
    <configuration name="demo_for_detection" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="army_project" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/demo_for_detection.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="demo_for_preprocessing" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="army_project" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/demo_for_preprocessing.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.demo_for_preprocessing" />
        <item itemvalue="Python.demo_for_detection" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-python-sdk-7e47963ff851-f0eec537fc84-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-252.23892.515" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="127a1c25-99a0-434b-b10a-d0dc75521bfb" name="Changes" comment="" />
      <created>1751888806917</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751888806917</updated>
      <workItem from="1755062466971" duration="16797000" />
      <workItem from="1755091940864" duration="3000" />
      <workItem from="1755219304639" duration="2162000" />
      <workItem from="1755229980801" duration="2299000" />
      <workItem from="1755239774358" duration="1247000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <ignored-roots>
      <path value="$PROJECT_DIR$/IPC-Dehaze" />
      <path value="$PROJECT_DIR$/MotionDeblur" />
    </ignored-roots>
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/Dehaze/basicsr/utils/diffjpeg.py</url>
          <line>6</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/army_project$demo_for_preprocessing.coverage" NAME="demo_for_preprocessing 覆盖结果" MODIFIED="1755219863095" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/army_project$demo_for_detection.coverage" NAME="demo_for_detection 覆盖结果" MODIFIED="1755219711951" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
  </component>
</project>