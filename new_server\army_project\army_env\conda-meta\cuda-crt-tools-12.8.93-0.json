{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": ["cuda-version >=12.8,<12.9.0a0"], "extracted_package_dir": "", "files": ["Library/bin/crt/link.stub", "Library/bin/crt/prelink.stub"], "fn": "cuda-crt-tools-12.8.93-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "", "type": 1}, "md5": "46f8b2fe64534f9b544e1d76793afad9", "name": "cuda-crt-tools", "package_tarball_full_path": "", "paths_data": {"paths": [{"_path": "Library/bin/crt/link.stub", "path_type": "hardlink", "sha256": "aed0e3c39c4e3051695d18bc1079cf5c12be247859d73b62c00891672b4f191e", "sha256_in_prefix": "aed0e3c39c4e3051695d18bc1079cf5c12be247859d73b62c00891672b4f191e", "size_in_bytes": 6637}, {"_path": "Library/bin/crt/prelink.stub", "path_type": "hardlink", "sha256": "f91db9f00463d557ceb1fbd4ce05b6d1257677dd449cfb5f02244e2a51183f13", "sha256_in_prefix": "f91db9f00463d557ceb1fbd4ce05b6d1257677dd449cfb5f02244e2a51183f13", "size_in_bytes": 4069}], "paths_version": 1}, "requested_spec": "None", "sha256": "0a7acc643b80af6f706c1af3395b4b19c7e74f5d243cfc64a07ca3f72811946b", "size": 19275, "subdir": "win-64", "timestamp": 1740205155000, "url": "https://conda.anaconda.org/nvidia/win-64/cuda-crt-tools-12.8.93-0.conda", "version": "12.8.93"}