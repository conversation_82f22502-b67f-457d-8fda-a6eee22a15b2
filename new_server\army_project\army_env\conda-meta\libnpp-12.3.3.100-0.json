{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.8,<12.9.0a0"], "extracted_package_dir": "", "files": ["Library/bin/nppc64_12.dll", "Library/bin/nppial64_12.dll", "Library/bin/nppicc64_12.dll", "Library/bin/nppidei64_12.dll", "Library/bin/nppif64_12.dll", "Library/bin/nppig64_12.dll", "Library/bin/nppim64_12.dll", "Library/bin/nppist64_12.dll", "Library/bin/nppisu64_12.dll", "Library/bin/nppitc64_12.dll", "Library/bin/npps64_12.dll"], "fn": "libnpp-12.3.3.100-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "", "type": 1}, "md5": "d7e431c3287a99462bbbac9c3f944b22", "name": "libnpp", "package_tarball_full_path": "", "paths_data": {"paths": [{"_path": "Library/bin/nppc64_12.dll", "path_type": "hardlink", "sha256": "3ba119e90e86903c61f7002f9412d90baf166946cb7e3528f87e7b4d5b0b24e3", "sha256_in_prefix": "3ba119e90e86903c61f7002f9412d90baf166946cb7e3528f87e7b4d5b0b24e3", "size_in_bytes": 294912}, {"_path": "Library/bin/nppial64_12.dll", "path_type": "hardlink", "sha256": "cf1c4c4dd249667d1390b3276c39038a54d8e2753b459a4ff32b853aa8200d2e", "sha256_in_prefix": "cf1c4c4dd249667d1390b3276c39038a54d8e2753b459a4ff32b853aa8200d2e", "size_in_bytes": 20503552}, {"_path": "Library/bin/nppicc64_12.dll", "path_type": "hardlink", "sha256": "7ed1935d34ec0d99629e1cc7e055895b13c99f500784fd2fa4ee0379e2e9e798", "sha256_in_prefix": "7ed1935d34ec0d99629e1cc7e055895b13c99f500784fd2fa4ee0379e2e9e798", "size_in_bytes": 7881216}, {"_path": "Library/bin/nppidei64_12.dll", "path_type": "hardlink", "sha256": "c74101a2482038d24b35579aee8ffc1f959e81c3d895cd899c83eb4d3b71ff6f", "sha256_in_prefix": "c74101a2482038d24b35579aee8ffc1f959e81c3d895cd899c83eb4d3b71ff6f", "size_in_bytes": 12242944}, {"_path": "Library/bin/nppif64_12.dll", "path_type": "hardlink", "sha256": "cd7d1a4b83135c00b1a98a87bf3a0095bc9297f6a9f03180b40703d13e7ec68e", "sha256_in_prefix": "cd7d1a4b83135c00b1a98a87bf3a0095bc9297f6a9f03180b40703d13e7ec68e", "size_in_bytes": 126647808}, {"_path": "Library/bin/nppig64_12.dll", "path_type": "hardlink", "sha256": "d25876cf188520a68aff7adb69770fb081432a455e710335e3007981364887d0", "sha256_in_prefix": "d25876cf188520a68aff7adb69770fb081432a455e710335e3007981364887d0", "size_in_bytes": 53779968}, {"_path": "Library/bin/nppim64_12.dll", "path_type": "hardlink", "sha256": "f441511bcce54b310c1ba456bf909172a90e2540444ccd5a72d67dded9d4b634", "sha256_in_prefix": "f441511bcce54b310c1ba456bf909172a90e2540444ccd5a72d67dded9d4b634", "size_in_bytes": 11828224}, {"_path": "Library/bin/nppist64_12.dll", "path_type": "hardlink", "sha256": "2eb98eaf3f99c8d702e1824beb9cc69362a3f44a5f9823a8869a29a19bd8bf64", "sha256_in_prefix": "2eb98eaf3f99c8d702e1824beb9cc69362a3f44a5f9823a8869a29a19bd8bf64", "size_in_bytes": 48549888}, {"_path": "Library/bin/nppisu64_12.dll", "path_type": "hardlink", "sha256": "ae17890c6e58b61ed3dbea52ac3ebad90e9e6c1243bd220080280748d42c17b6", "sha256_in_prefix": "ae17890c6e58b61ed3dbea52ac3ebad90e9e6c1243bd220080280748d42c17b6", "size_in_bytes": 391168}, {"_path": "Library/bin/nppitc64_12.dll", "path_type": "hardlink", "sha256": "8172f64cdead9311d1a09deeaffa1b1672cc65f72ae2726521e22d1a89e08c99", "sha256_in_prefix": "8172f64cdead9311d1a09deeaffa1b1672cc65f72ae2726521e22d1a89e08c99", "size_in_bytes": 5284352}, {"_path": "Library/bin/npps64_12.dll", "path_type": "hardlink", "sha256": "bd095636152313b07517ae21e8cf3adeeb164a0e684c113e43c9eba982e11706", "sha256_in_prefix": "bd095636152313b07517ae21e8cf3adeeb164a0e684c113e43c9eba982e11706", "size_in_bytes": 16626176}], "paths_version": 1}, "requested_spec": "None", "sha256": "045c74d2c67156000d61dcd75bc3d04360ca200044a80203fe04410abe3db875", "size": 133612743, "subdir": "win-64", "timestamp": 1739451755000, "url": "https://conda.anaconda.org/nvidia/win-64/libnpp-12.3.3.100-0.conda", "version": "12.3.3.100"}