{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/noarch", "constrains": [], "depends": ["cuda-version >=12.8,<12.9.0a0", "cuda-crt-dev_win-64 12.8.93 0", "cuda-nvvm-dev_win-64 12.8.93 0"], "extracted_package_dir": "", "files": ["Library/bin/__nvcc_device_query.exe", "Library/bin/bin2c.exe", "Library/bin/cudafe++.exe", "Library/bin/fatbinary.exe", "Library/bin/nvcc.exe", "Library/bin/nvcc.profile", "Library/bin/nvlink.exe", "Library/bin/ptxas.exe", "Library/include/fatbinary_section.h", "Library/include/nvPTXCompiler.h", "Library/lib/nvptxcompiler_static.lib"], "fn": "cuda-nvcc-dev_win-64-12.8.93-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "", "type": 1}, "md5": "c68f4af0e5039aea1ce986f5fd68431c", "name": "cuda-nvcc-dev_win-64", "noarch": "generic", "package_tarball_full_path": "", "package_type": "noarch_generic", "paths_data": {"paths": [{"_path": "Library/bin/__nvcc_device_query.exe", "path_type": "hardlink", "sha256": "bcd813649e4702b4019987c32b6df2795934eff3420bdc96e8dcc0807b6d738a", "sha256_in_prefix": "bcd813649e4702b4019987c32b6df2795934eff3420bdc96e8dcc0807b6d738a", "size_in_bytes": 84480}, {"_path": "Library/bin/bin2c.exe", "path_type": "hardlink", "sha256": "d62602e02759e90e7bf68cfaabf72b0973b9e8cf76043c00dcd95fec4d14a1f1", "sha256_in_prefix": "d62602e02759e90e7bf68cfaabf72b0973b9e8cf76043c00dcd95fec4d14a1f1", "size_in_bytes": 220160}, {"_path": "Library/bin/cudafe++.exe", "path_type": "hardlink", "sha256": "ceadbcfd172eb66f0dd52bd643a296f1648b409a2061531d432a959c962fa702", "sha256_in_prefix": "ceadbcfd172eb66f0dd52bd643a296f1648b409a2061531d432a959c962fa702", "size_in_bytes": 9069056}, {"_path": "Library/bin/fatbinary.exe", "path_type": "hardlink", "sha256": "3191a84617d160569da13e97d302a918c720ef428f27af9b00abc6496f80f669", "sha256_in_prefix": "3191a84617d160569da13e97d302a918c720ef428f27af9b00abc6496f80f669", "size_in_bytes": 891392}, {"_path": "Library/bin/nvcc.exe", "path_type": "hardlink", "sha256": "07565c215cc96e3b3609a931eb6eaffb1ecca24339442603f92832494c656727", "sha256_in_prefix": "07565c215cc96e3b3609a931eb6eaffb1ecca24339442603f92832494c656727", "size_in_bytes": 17741312}, {"_path": "Library/bin/nvcc.profile", "path_type": "hardlink", "sha256": "7e6a8e0bf7c482df79a5b24319f101974742333dbf73bcfa6efc11a0618a2649", "sha256_in_prefix": "7e6a8e0bf7c482df79a5b24319f101974742333dbf73bcfa6efc11a0618a2649", "size_in_bytes": 432}, {"_path": "Library/bin/nvlink.exe", "path_type": "hardlink", "sha256": "ad32f85bbcff80818f54e9530c29361dd4e887ab6b6027575b6a50aebc69f8ff", "sha256_in_prefix": "ad32f85bbcff80818f54e9530c29361dd4e887ab6b6027575b6a50aebc69f8ff", "size_in_bytes": 25339904}, {"_path": "Library/bin/ptxas.exe", "path_type": "hardlink", "sha256": "7f6f04d25e5a7ab2c07c15e4ef26a07cec08104ebfebf364331fafa8f3c443d6", "sha256_in_prefix": "7f6f04d25e5a7ab2c07c15e4ef26a07cec08104ebfebf364331fafa8f3c443d6", "size_in_bytes": 24753152}, {"_path": "Library/include/fatbinary_section.h", "path_type": "hardlink", "sha256": "4ebcd4d30c74ee5c5798ee8a8856a7cf709e940e6beea73364b47792035bd0a8", "sha256_in_prefix": "4ebcd4d30c74ee5c5798ee8a8856a7cf709e940e6beea73364b47792035bd0a8", "size_in_bytes": 1870}, {"_path": "Library/include/nvPTXCompiler.h", "path_type": "hardlink", "sha256": "b00eebeca246f5e189bc3be77ae29f8e3a9213544fdee68c65e80ae50f00c362", "sha256_in_prefix": "b00eebeca246f5e189bc3be77ae29f8e3a9213544fdee68c65e80ae50f00c362", "size_in_bytes": 14799}, {"_path": "Library/lib/nvptxcompiler_static.lib", "path_type": "hardlink", "sha256": "b242a14aba79267b095cee4461dfe20c4e7583c0c100e84d3828187027b36c43", "sha256_in_prefix": "b242a14aba79267b095cee4461dfe20c4e7583c0c100e84d3828187027b36c43", "size_in_bytes": 153127616}], "paths_version": 1}, "requested_spec": "None", "sha256": "417b3a932d6659b41a6942fd78753a783d96563045abc3613816c0a07882e0b4", "size": 51810002, "subdir": "noarch", "timestamp": 1740205421000, "url": "https://conda.anaconda.org/nvidia/noarch/cuda-nvcc-dev_win-64-12.8.93-0.conda", "version": "12.8.93"}