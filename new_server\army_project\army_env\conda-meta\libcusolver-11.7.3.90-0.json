{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": ["libcublas", "libcusparse", "libnvjitlink", "vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.8,<12.9.0a0"], "extracted_package_dir": "", "files": ["Library/bin/cusolver64_11.dll", "Library/bin/cusolverMg64_11.dll"], "fn": "libcusolver-11.7.3.90-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "", "type": 1}, "md5": "f623ab03ddc3826c0b3be7b54976b5c2", "name": "libcusolver", "package_tarball_full_path": "", "paths_data": {"paths": [{"_path": "Library/bin/cusolver64_11.dll", "path_type": "hardlink", "sha256": "3d4f7a66b5f352db56d4bb5962bb453a42d5feb2d831779f4a0bebc9971c36fb", "sha256_in_prefix": "3d4f7a66b5f352db56d4bb5962bb453a42d5feb2d831779f4a0bebc9971c36fb", "size_in_bytes": 225683456}, {"_path": "Library/bin/cusolverMg64_11.dll", "path_type": "hardlink", "sha256": "c3377f10606ff0606be2f08401d54aff6c37f137b78d6b6e21be356a5b4d6cc2", "sha256_in_prefix": "c3377f10606ff0606be2f08401d54aff6c37f137b78d6b6e21be356a5b4d6cc2", "size_in_bytes": 157071360}], "paths_version": 1}, "requested_spec": "None", "sha256": "bb1db9b32136cc43114601838a34b5fe999bd0c819fd01d2b40489256c567513", "size": 158377524, "subdir": "win-64", "timestamp": 1739458934000, "url": "https://conda.anaconda.org/nvidia/win-64/libcusolver-11.7.3.90-0.conda", "version": "11.7.3.90"}