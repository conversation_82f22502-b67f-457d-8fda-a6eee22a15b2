{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/noarch", "constrains": [], "depends": ["__win", "cuda-libraries 12.8.1.*"], "extracted_package_dir": "", "files": [], "fn": "cuda-runtime-12.8.1-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "", "type": 1}, "md5": "506b61609690690aaa58c27eca2cf4f0", "name": "cuda-runtime", "noarch": "generic", "package_tarball_full_path": "", "package_type": "noarch_generic", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "f9879c493d18204eb1a25c5aafb5a9ab91092aa38756be3c82277bc3c871fd5c", "size": 17086, "subdir": "noarch", "timestamp": 1741063825000, "url": "https://conda.anaconda.org/nvidia/noarch/cuda-runtime-12.8.1-0.conda", "version": "12.8.1"}