{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": ["cuda-cudart-dev_win-64 12.8.*", "cuda-nvcc-dev_win-64 12.8.93.*", "cuda-nvcc-impl 12.8.93.*", "cuda-nvcc-tools 12.8.93.*"], "extracted_package_dir": "", "files": ["etc/conda/activate.d/~cuda-nvcc_activate.bat", "etc/conda/deactivate.d/~cuda-nvcc_deactivate.bat"], "fn": "cuda-nvcc_win-64-12.8.93-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "", "type": 1}, "md5": "0ce7c724fc2d4d15ec4a86ced020fa23", "name": "cuda-nvcc_win-64", "package_tarball_full_path": "", "paths_data": {"paths": [{"_path": "etc/conda/activate.d/~cuda-nvcc_activate.bat", "path_type": "hardlink", "sha256": "09099d0217b61f51f6f29a61ae499fe78b01b4c46e4c2d1add4bfe788f3f50d2", "sha256_in_prefix": "09099d0217b61f51f6f29a61ae499fe78b01b4c46e4c2d1add4bfe788f3f50d2", "size_in_bytes": 272}, {"_path": "etc/conda/deactivate.d/~cuda-nvcc_deactivate.bat", "path_type": "hardlink", "sha256": "3603b82424db1a02c14285d4f8926fbaee35dbcf5ba52572e6adcd7b45b2cf6f", "sha256_in_prefix": "3603b82424db1a02c14285d4f8926fbaee35dbcf5ba52572e6adcd7b45b2cf6f", "size_in_bytes": 202}], "paths_version": 1}, "requested_spec": "None", "sha256": "452a0dbe83318632a2a468ed629e047cfb835ae1ce92fe02ca287dc53504c9fa", "size": 18502, "subdir": "win-64", "timestamp": 1740205556000, "url": "https://conda.anaconda.org/nvidia/win-64/cuda-nvcc_win-64-12.8.93-0.conda", "version": "12.8.93"}