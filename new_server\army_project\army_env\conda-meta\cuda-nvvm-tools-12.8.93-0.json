{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.8,<12.9.0a0"], "extracted_package_dir": "", "files": ["Library/nvvm/bin/cicc.exe", "Library/nvvm/bin/nvvm64_40_0.dll", "Library/nvvm/libdevice/libdevice.10.bc"], "fn": "cuda-nvvm-tools-12.8.93-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "", "type": 1}, "md5": "75edcfb001d77b6d3abeb4a50556f4e2", "name": "cuda-nvvm-tools", "package_tarball_full_path": "", "paths_data": {"paths": [{"_path": "Library/nvvm/bin/cicc.exe", "path_type": "hardlink", "sha256": "eb19ac8ee40278a372f79ac80a9c96209ce257bd19c85c55aa901e6137094fa9", "sha256_in_prefix": "eb19ac8ee40278a372f79ac80a9c96209ce257bd19c85c55aa901e6137094fa9", "size_in_bytes": 67792896}, {"_path": "Library/nvvm/bin/nvvm64_40_0.dll", "path_type": "hardlink", "sha256": "87c9e1d200a03a010c1ca0c5f6ba3eae1c2423e26691bb15077c1430f1945325", "sha256_in_prefix": "87c9e1d200a03a010c1ca0c5f6ba3eae1c2423e26691bb15077c1430f1945325", "size_in_bytes": 52873216}, {"_path": "Library/nvvm/libdevice/libdevice.10.bc", "path_type": "hardlink", "sha256": "ac34941fb4c4c2fd747462265c2958773f2337690759348fe3f71d0b8863ced2", "sha256_in_prefix": "ac34941fb4c4c2fd747462265c2958773f2337690759348fe3f71d0b8863ced2", "size_in_bytes": 484876}], "paths_version": 1}, "requested_spec": "None", "sha256": "46defdcb8fdda9351d5110332c2b3f9a14d62d9543e3cf8e316a9c7d17e7e5a6", "size": 39639881, "subdir": "win-64", "timestamp": 1740205264000, "url": "https://conda.anaconda.org/nvidia/win-64/cuda-nvvm-tools-12.8.93-0.conda", "version": "12.8.93"}