{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.8,<12.9.0a0"], "extracted_package_dir": "", "files": ["Library/bin/nvprune.exe"], "fn": "cuda-nvprune-12.8.90-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "", "type": 1}, "md5": "64fff98199359e946b22a8f072816cf6", "name": "cuda-nvprune", "package_tarball_full_path": "", "paths_data": {"paths": [{"_path": "Library/bin/nvprune.exe", "path_type": "hardlink", "sha256": "ccdb64de2a024c2bd36e29325aaf7b2e6493340e32ccc68e97c34e642301baba", "sha256_in_prefix": "ccdb64de2a024c2bd36e29325aaf7b2e6493340e32ccc68e97c34e642301baba", "size_in_bytes": 245248}], "paths_version": 1}, "requested_spec": "None", "sha256": "688bde72f190333ce1e9e4cd7a963045461b8e490274dae81dbd6e775ba679f1", "size": 133623, "subdir": "win-64", "timestamp": 1739448430000, "url": "https://conda.anaconda.org/nvidia/win-64/cuda-nvprune-12.8.90-0.conda", "version": "12.8.90"}