{"build": "3", "build_number": 3, "channel": "https://conda.anaconda.org/nvidia/noarch", "constrains": ["__cuda >=12", "cudatoolkit 12.8|12.8.*"], "depends": [], "extracted_package_dir": "", "files": [], "fn": "cuda-version-12.8-3.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "", "type": 1}, "md5": "c4a0e656851b1a8a5ec8f7a407d0204c", "name": "cuda-version", "noarch": "generic", "package_tarball_full_path": "", "package_type": "noarch_generic", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "0193901b9be91ea09278538707908fbd0a5b21059c57ff0aa7ce1a597009f56d", "size": 17104, "subdir": "noarch", "timestamp": 1741063629000, "url": "https://conda.anaconda.org/nvidia/noarch/cuda-version-12.8-3.conda", "version": "12.8"}